.mainContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
}

.form-group-new {
    display: grid;
    grid-template-columns: 1fr;
    padding: 0px !important;
}

.signInContainer {
    display: flex;
    flex-direction: row;
    width: 100%;
    min-height: 600px;
    padding: 20px 0;
}

.leftContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px 40px;
}

.loginImage {
    height: 356.343px;
    align-self: stretch;
    aspect-ratio: 533.00/356.34;
    margin-top: 20px;
    object-fit: contain;
}

.rightSide {
    flex: 0.8;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
}

.container {
    width: 367px;
    background: #170903;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    padding: 35px 25px;
    text-align: center;
}

.buttonContainer {
    display: flex;
    justify-content: flex-end;
    /* margin-bottom: 15px; */
}

.description {
    color: #F8F8F7;
    font-family: "poppins", sans-serif;
    text-align: left;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.passwordContainer {
    position: relative;
    width: 100%;
    padding-top: 20px;
}

.passwordInput {
    width: 100%;
    padding: 10px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    outline: none;
    font-family: "Inter";
    font-weight: 400;
    font-size: 16px;
}

.passwordToggle {
    position: absolute;
    right: -15px; /* Positioned consistently within the input field */
    top: 60%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    font-size: 18px;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.passwordToggle:hover {
    color: #495057;
}

.passwordToggle:focus {
    outline: none;
}


.forgotPassword {
    color: #B9B5B3;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    font-family: "Inter", sans-serif;
    text-align: right;
    margin-top: 5px;
    /* margin-bottom: 15px; */
    text-decoration: underline;
    cursor: pointer;
}

.buttonContainerNew {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
}

.buttonIcon {
    height: 24px;
    width: 24px;
    margin-right: 10px;
    vertical-align: middle;
}

.signinButton {
    display: flex;
    height: 40px;
    padding: 10px 45px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 3px;
    background: #EF8963;
    color: #170903;
    text-align: center;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin: auto;
    cursor: pointer;
    border: none;
}
.signinButton:hover{
    background-color: #ec9d7f;
}

.featuresSection {
    width: 100%;
    padding: 0 20px;
    margin-top: 20px;
}

.featureItem {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.trustedBySection {
    width: 100%;
    margin-top: 30px;
    margin-bottom: 100px;
}

.trustedByContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.trustedByTitle {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    font-family: "Poppins", sans-serif;
}

.supportText {
    color: white;
    font-family: "Inter";
    font-size: 15px;
    font-weight: 400;
    line-height: 24px;
    text-align: center;
    margin-top: 15px;
}

.supportLink {
    color: #EF8963;
    text-decoration: underline;
}

.supportLink:hover {
    color: #d67850;
}

/* Eye icon styling */
.eye-icon {
    width: 20px;
    height: 20px;
    color: #6c757d;
}

/* Input field focus states */
.form-control:focus,
.passwordInput:focus {
    border-color: #EF8963;
    box-shadow: 0 0 0 0.2rem rgba(239, 137, 99, 0.25);
}

/* Ensure both inputs have the same visual appearance */
.form-control,
.passwordInput {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (max-width: 1200px) {
    .signInContainer {
        padding: 10px;
    }

    .leftContent {
        padding: 10px 20px;
    }

    .loginImage {
        height: 300px;
        aspect-ratio: 533.00/356.34;
    }
}

@media (max-width: 992px) {
    .container {
        width: 320px;
        padding: 25px 20px;
    }

    .loginImage {
        height: 250px;
        aspect-ratio: 533.00/356.34;
    }
}

@media (max-width: 768px) {
    .signInContainer {
        flex-direction: column;
        min-height: auto;
    }

    .leftContent {
        padding: 20px;
        align-items: center;
        text-align: center;
    }

    .rightSide {
        width: 100%;
        padding: 20px 10px 40px;
        align-items: center;
    }

    .container {
        width: 90%;
        max-width: 367px;
    }

    .featuresSection {
        padding: 0 10px;
    }
}

@media (max-width: 480px) {
    .container {
        width: 95%;
        padding: 25px 15px;
    }

    .buttonIcon {
        height: 20px;
        width: 20px;
        margin-right: 8px;
    }

    .loginImage {
        height: 200px;
        aspect-ratio: 533.00/356.34;
    }
}