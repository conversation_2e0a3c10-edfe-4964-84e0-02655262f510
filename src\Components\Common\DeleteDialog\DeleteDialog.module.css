.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialog {
    display: flex;
    width: 705px;
    padding: 30px 45px 45px 45px;
    flex-direction: column;
    align-items: flex-start;
    gap: 25px;
    border-radius: 10px;
    background: #170903;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.3);
    position: relative;
}

.header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: flex-start;
}

.title {
    color: #EA5822;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 171.429% */
    margin: 0;
    flex: 1;
    padding-right: 20px;
}

.closeButton {
    background: none;
    border: none;
    color: #FFFFFF;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.closeButton:hover {
    color: #ec9d7f;
}

.content {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
}

.warningText {
    color: #FFFFFF;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin: 0;
}

.descriptionText {
    color: #FFFFFF;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin: 0;
}

.deleteButton {
    display: flex;
    height: 40px;
    padding: 10px 45px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    border-radius: 3px;
    background: #EF8963;
    border: none;
    color: #170903;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.deleteButton:hover {
    background: #ec9d7f;
}

.deleteButton:active {
    background: #d16b43;
}
