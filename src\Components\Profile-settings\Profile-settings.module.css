.main-section {
    margin-left: 220px;
    padding: 20px 45px 20px 72px;
    flex: 1;
    transition: margin-left 0.3s ease;
    background-color: #FFFF;
    height: 100vh;
}
.main-section.expanded {
    margin-left: 85px;
}
.dFlex {
    display: flex;
    /*align-items: center;*/

}

.section1{
    width: 30%;
    padding-left: 0;
    top: 0;
    left: 0;
}

.section2 {
    width: 70%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    padding-left: 45px;
}
.profileInput{
    margin-left: 20px;
    margin-bottom: 0px;
}

.passwordContainer {
    position: relative;
    width: 100%;
    padding-top: 20px;
    display: flex;
}

.passwordInput {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 4px;
    outline: none;
    font-family: "Inter";
    font-weight: 400;
    font-size: 16px;
    padding: 10px;
    padding-right: 80px;
}

.passwordIconsContainer {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-40%);
    display: flex;
    align-items: center;
    height: 100%;
}

.eyeButton {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
}

.eyeIcon {
    width: 20px;
    height: 20px;
    color: #666;
}

.validationIcon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkIcon {
    width: 20px;
    height: 20px;
    color: green;
}

.xIcon {
    width: 20px;
    height: 20px;
    color: red;
}

.formCheckInput:checked {
    appearance: none;
    /*background-color: #746B68;*/
    /*border-color: #746B68;*/
    position: relative;
    input{
        padding: 0 !important;
    }
}

.formCheckInput:checked::after {
    appearance: none;
    padding: 0;
    content: "";
    width: 8px;
    height: 8px;
    background-color: #5a524f;
    border-radius: 50%;
    /*display: block;*/
    position: absolute;
    /*top: 50%;*/
    /*left: 50%;*/
    transform: translate(-50%, -50%);

}

#root{
    height: 100vh;
    overflow: scroll;
}
.avatarContainer {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    /*display: flex;*/
    /*justify-content: center;*/
    /*align-items: center;*/
}
.topAvatarSection {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 580px;
}

.topAvatarSection .avatarContainer {
    width: 250px;
    height: 250px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 10px;
}

.topAvatarSection .avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.topAvatarSection .editButton {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 10px;
}

.topAvatarSection .editButton:hover {
    background: #2980b9;
}

.avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.editButton {
    padding: 8px 16px;
    border: 1px solid #ccc;
    background-color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

.editButton:hover {
    background-color: #f1f1f1;
}
