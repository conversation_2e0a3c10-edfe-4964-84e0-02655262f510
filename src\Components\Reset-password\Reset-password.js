import { useState } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { CheckIcon, XIcon } from "@heroicons/react/solid"
import Header from "../Header/Header"
import globalStyles from "../globalStyles.module.css"
import styles from "./Reset-password.module.css"
import "../global_styles.css"
import config from '../../Config/config.json';

export default function ResetPassword() {
  const navigate = useNavigate()
  const location = useLocation()
  const email = location.state?.email

  const [formSubmitted, setFormSubmitted] = useState(false)
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [strongPassword, setStrongPassword] = useState(false)
  const [passwordsMatch, setPasswordsMatch] = useState(true)
  const [resetPassword, setResetPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const trustedLogos = config.TRUSTED_BY_LOGOS
  const signInClick = () => {
    navigate("/")
  }

  const checkStrongPassword = (e) => {
    const passwordValue = e.target.value
    setPassword(passwordValue)

    // Password must have at least 8 characters, 1 uppercase, 1 number, and 1 special character
    const strongPasswordPattern = /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/
    setStrongPassword(strongPasswordPattern.test(passwordValue))
  }

  const handleConfirmPasswordChange = (e) => {
    const confirmValue = e.target.value
    setConfirmPassword(confirmValue)
    setPasswordsMatch(confirmValue === password)
  }

  const resetPasswordSuccess = (e) => {
    e.preventDefault()
    setFormSubmitted(true)

    if (password && strongPassword && confirmPassword && passwordsMatch) {
      setIsLoading(true)
      // Simulate API call
      setTimeout(() => {
        setIsLoading(false)
        setResetPassword(true)
      }, 1000)
    }
  }

  return (
    <div className="d-flex flex-column">
      <div>
        <Header />
      </div>

      <div className={styles.mainContainer}>
        <div className={styles.signInContainer}>
          <div className={styles.leftContent}>
            <div className={globalStyles.headerStyle}>
              <div style={{ fontSize: "45px" }}>Effortless Data Migration</div>
              <div style={{ fontSize: "45px" }}>Automated!</div>
            </div>
            <div className={globalStyles.interSummaryStyle} style={{ padding: "20px" }}>
              With <b>migrateGenie</b>, move your data between platforms with confidence. Automate mapping, track
              progress and ensure secure transactions - no headaches.
            </div>
            <img src="/assets/Login Animation.gif" alt="image" className={styles.loginImage} />
          </div>

          <div className={styles.rightSide}>
            {!resetPassword ? (
              <div className={styles.container}>
                <div className={styles.buttonContainer}>
                  <button className="no-color-button" onClick={signInClick}>
                    Back to Sign-in
                  </button>
                </div>
                <div className="form-container" style={{ paddingTop: "0px" }}>
                  <p className={styles.description}>Enter a new password</p>
                  <form onSubmit={resetPasswordSuccess}>
                    <div className="form-group-new">
                      <input
                        type="text"
                        className="form-control"
                        style={{ width: "100%", padding: "10px" }}
                        value={email}
                        disabled
                      />
                    </div>

                    <div className={styles.passwordContainer}>
                      <input
                        type="password"
                        className={`${styles.passwordInput} ${formSubmitted && (!password || !strongPassword) ? "input-error" : ""}`}
                        placeholder="Your new password"
                        value={password}
                        onChange={checkStrongPassword}
                      />
                      <button
                        type="button"
                        className={styles.passwordToggle}
                        style={formSubmitted && !strongPassword ? { top: "50%" } : {}}
                        aria-label="Password strength indicator"
                      >
                        {!strongPassword && password.length > 0 ? (
                          <XIcon className="eye-icon" style={{ color: "red" }} />
                        ) : strongPassword ? (
                          <CheckIcon className="eye-icon" style={{ color: "green" }} />
                        ) : null}
                      </button>
                      {formSubmitted && !password && <p className="error-message">Password cannot be empty</p>}
                      {formSubmitted && password && !strongPassword && (
                        <p className="error-message">Password must be strong</p>
                      )}
                    </div>

                    <div className={styles.passwordContainer} style={{ marginTop: "15px" }}>
                      <input
                        type="password"
                        className={`${styles.passwordInput} ${formSubmitted && (!confirmPassword || !passwordsMatch) ? "input-error" : ""}`}
                        placeholder="Confirm password"
                        value={confirmPassword}
                        onChange={handleConfirmPasswordChange}
                      />
                      <button type="button" className={styles.passwordToggle} aria-label="Password match indicator">
                        {confirmPassword &&
                          (passwordsMatch ? (
                            <CheckIcon className="eye-icon" style={{ color: "green" }} />
                          ) : (
                            <XIcon className="eye-icon" style={{ color: "red" }} />
                          ))}
                      </button>
                      {formSubmitted && !confirmPassword && (
                        <p className="error-message">Please confirm your password</p>
                      )}
                      {formSubmitted && confirmPassword && !passwordsMatch && (
                        <p className="error-message">Passwords do not match</p>
                      )}
                    </div>

                    <p className={styles.passwordRequirements}>
                      Password must be 8 characters long with 1 capital letter, 1 number, 1 symbol
                    </p>

                    <button className="main-button" type="submit">
                      {isLoading ? (
                        <div className={globalStyles.loaderContainer}>
                          <div className={globalStyles.loader}></div>
                        </div>
                      ) : (
                        "Reset password"
                      )}
                    </button>
                  </form>
                </div>
              </div>
            ) : (
              <div
                className={styles.container}
                style={{
                  minHeight: "350px",
                  paddingTop: "20%",
                }}
              >
                <div className="form-container">
                  <p className={styles.description}>Success!</p>
                </div>

                <div className={styles.successContainer}>
                  <div className={styles.successIcon}>
                    <CheckIcon className={styles.checkIcon} />
                  </div>
                  <p className={styles.successMessage}>Your password has been reset!</p>
                </div>

                <button className="no-color-button" style={{ marginTop: "60px" }} onClick={signInClick}>
                  Back to sign-in
                </button>
              </div>
            )}
          </div>
        </div>

        <div className={styles.featuresSection}>
          <div className={`${globalStyles.bottomContainer} ${globalStyles.poppinsStyle}`} style={{ fontWeight: "600" }}>
            <div className={styles.featureItem}>
              <div className={globalStyles.imageContainer}>
                <img src="/assets/nano.png" alt="Smart auto-mapping" className={globalStyles.imageBottom} />
              </div>
              <span>Smart auto-mapping</span>
            </div>
            <div className={styles.featureItem}>
              <div className={globalStyles.imageContainer}>
                <img src="/assets/lock.png" alt="Secure with encryption" className={globalStyles.imageBottom} />
              </div>
              <span>Secure with encryption</span>
            </div>
            <div className={styles.featureItem}>
              <div className={globalStyles.imageContainer}>
                <img src="/assets/lightstrike.png" alt="No-code configurations" className={globalStyles.imageBottom} />
              </div>
              <span>No-code configurations</span>
            </div>
            <div className={styles.featureItem}>
              <div className={globalStyles.imageContainer}>
                <img src="/assets/analytics.png" alt="Migration reports" className={globalStyles.imageBottom} />
              </div>
              <span>Migration reports</span>
            </div>
          </div>

          <div className={styles.trustedBySection}>
            <hr className={globalStyles.signupHr} />
            <div className={styles.trustedByContainer}>
              <div className={styles.trustedByTitle}>Trusted by</div>
              <div className={globalStyles.trustedLogos}>
                {trustedLogos.map((logo, idx) =>
                  logo.url ? (
                    <a
                      key={logo.alt}
                      href={logo.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={styles.trustedLogo}
                      style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                    >
                      <img
                        src={logo.src}
                        alt={logo.alt}
                        style={{ width: 60, height: 50, objectFit: "contain" }}
                      />
                    </a>
                  ) : (                    <div
                      key={logo.alt}
                      className={styles.trustedLogo}
                      style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                    >
                      <img
                        src={logo.src}
                        alt={logo.alt}
                        style={{ width: 100, height: 80, objectFit: "contain" }}
                      />
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
