import { useState, useRef, useEffect } from "react"
import globalStyles from "../globalStyles.module.css"
import LoaderSpinner from "../loaderspinner"
import Sidebar from "../Sidebar/Sidebar"
import styles from "./Data-security.module.css"
import { GlobeIcon, SearchIcon } from "@heroicons/react/solid"
import Tabs from "@mui/material/Tabs"
import Tab from "@mui/material/Tab"
import { HiOutlineFunnel } from "react-icons/hi2"
import { FaAngleDoubleLeft, FaChevronLeft, FaChevronRight, FaAngleDoubleRight } from "react-icons/fa"
import { deleteConn, getConnDetails } from "../apiService"
import { displayArticle } from "../../Helper/helper";
import config from "../../Config/config.json";
import DeleteDialog from "../Common/DeleteDialog/DeleteDialog"


export default function DataSecurity() {
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
        return localStorage.getItem("isSidebarCollapsed") === "true"
    })
    const [value, setValue] = useState(0)
    const [isLoading, setIsLoading] = useState(false)
    const [filterDropdownOpen, setFilterDropdownOpen] = useState(false)
    const [selectedFilter, setSelectedFilter] = useState("See all")
    const dropdownRef = useRef(null)
    const [currentPage, setCurrentPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(25)
    const [totalPages, setTotalPages] = useState(5)
    const [searchQuery, setSearchQuery] = useState("")
    const [tableSearchQuery, setTableSearchQuery] = useState("")
    const [email, setEmail] = useState(localStorage.getItem("email"))
    const [sourceData, setSourceData] = useState([])
    const [targetData, setTargetData] = useState([])
    const [deleteLoading, setDeleteLoading] = useState(false)
    const [showPopup, setShowPopup] = useState(false)
    const [selectedItem, setSelectedItem] = useState(null)

    const deleteConnection = async (data) => {
        setDeleteLoading(true)
        const res = await deleteConn(email, data.isTarget, data.isSource, data.providerName, data.accountEmail, data.domain)
        if (res.status === 204) {
            setDeleteLoading(false)
            if (data.isSource) {
                setSourceData((prevData) =>
                    prevData.filter(
                        (item) => !(item.accountEmail === data.accountEmail && item.providerName === data.providerName),
                    ),
                )
            } else if (data.isTarget) {
                setTargetData((prevData) =>
                    prevData.filter(
                        (item) => !(item.accountEmail === data.accountEmail && item.providerName === data.providerName),
                    ),
                )
            }
        }
    }

    useEffect(() => {
        const fetchConnections = async () => {
            setIsLoading(true)
            try {
                const [sourceRes, targetRes] = await Promise.all([
                    getConnDetails(email, true, false),
                    getConnDetails(email, false, true),
                ])
                setSourceData(sourceRes)
                setTargetData(targetRes)
            } catch (error) {
                console.error("Failed to fetch connection details:", error)
            } finally {
                setIsLoading(false)
            }
        }

        fetchConnections()
    }, [])

    useEffect(() => {
        const data = value === 0 ? getFilteredSourceData() : getFilteredTargetData()
        setTotalPages(Math.ceil(data.length / rowsPerPage))
    }, [rowsPerPage, value, sourceData, targetData, selectedFilter, tableSearchQuery])

    const filterOptions = config.CONNECTORS_LIST

    const handleChange = (event, newValue) => {
        setValue(newValue)
        setCurrentPage(0)
    }

    const toggleFilterDropdown = () => {
        setFilterDropdownOpen(!filterDropdownOpen)
    }

    const handleFilterSelect = (filter) => {
        setSelectedFilter(filter)
        setFilterDropdownOpen(false)
        setCurrentPage(0)
    }

    const handlePageChange = (page) => {
        setCurrentPage(page)
    }

    const handleRowsPerPageChange = (rows) => {
        setRowsPerPage(rows)
        setCurrentPage(0)
    }

    const handleSearchChange = (e) => {
        setSearchQuery(e.target.value.toLowerCase())
    }

    const handleTableSearchChange = (e) => {
        setTableSearchQuery(e.target.value.toLowerCase())
        setCurrentPage(0)
    }

    const formatTimestamp = (timestamp) => {
        return new Date(timestamp).toLocaleString()
    }

    useEffect(() => {
        function handleClickOutside(event) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setFilterDropdownOpen(false)
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => {
            document.removeEventListener("mousedown", handleClickOutside)
        }
    }, [])

    const getVisiblePages = () => {
        if (totalPages <= 1) return [0]
        if (totalPages <= 5) {
            return Array.from({ length: totalPages }, (_, i) => i)
        }

        const pages = []

        pages.push(0)

        if (currentPage > 1) {
            pages.push("ellipsis-start")
        }

        if (currentPage > 0) {
            pages.push(currentPage - 1)
        }

        if (currentPage > 0 && currentPage < totalPages - 1) {
            pages.push(currentPage)
        }

        if (currentPage < totalPages - 1) {
            pages.push(currentPage + 1)
        }

        if (currentPage < totalPages - 2) {
            pages.push("ellipsis-end")
        }

        if (totalPages > 1 && !pages.includes(totalPages - 1)) {
            pages.push(totalPages - 1)
        }

        return [...new Set(pages)]
    }

    const getFilteredSourceData = () => {
        let filtered = sourceData

        if (selectedFilter !== "See all") {
            filtered = filtered.filter(
                (item) =>
                    item.providerName?.toLowerCase() === selectedFilter.toLowerCase() ||
                    item.source?.toLowerCase() === selectedFilter.toLowerCase(),
            )
        }

        if (tableSearchQuery) {
            filtered = filtered.filter(
                (item) =>
                    item.accountEmail?.toLowerCase().includes(tableSearchQuery) ||
                    item.providerName?.toLowerCase().includes(tableSearchQuery) ||
                    item.templateCount?.toString().includes(tableSearchQuery),
            )
        }

        return filtered
    }

    const getFilteredTargetData = () => {
        let filtered = targetData

        if (selectedFilter !== "See all") {
            filtered = filtered.filter(
                (item) =>
                    item.providerName?.toLowerCase() === selectedFilter.toLowerCase() ||
                    item.target?.toLowerCase() === selectedFilter.toLowerCase(),
            )
        }

        if (tableSearchQuery) {
            filtered = filtered.filter(
                (item) =>
                    item.accountEmail?.toLowerCase().includes(tableSearchQuery) ||
                    item.providerName?.toLowerCase().includes(tableSearchQuery) ||
                    item.templateCount?.toString().includes(tableSearchQuery),
            )
        }

        return filtered
    }
    const getPaginatedData = () => {
        const filteredData = value === 0 ? getFilteredSourceData() : getFilteredTargetData()
        const start = currentPage * rowsPerPage
        const end = start + rowsPerPage
        return filteredData.slice(start, end)
    }

    const currentTabData = value === 0 ? sourceData : targetData
    const filteredData = getPaginatedData()
    const bothDataEmpty = sourceData.length === 0 && targetData.length === 0
    const showNoDataMessage = (value === 0 && sourceData.length === 0) || (value === 1 && targetData.length === 0)

    return (
        <div className={styles.appContainer}>
            {isLoading ? (
                <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                    <LoaderSpinner />
                </div>
            ) : (
                <div className={styles.pageWrapper}>
                    <Sidebar isCollapsed={isSidebarCollapsed} setIsCollapsed={setIsSidebarCollapsed} />
                    <div className={`${styles.mainSection} ${isSidebarCollapsed ? styles.expanded : ""}`}>
                        <DeleteDialog
                            isOpen={showPopup}
                            onConfirm={() => {
                                setShowPopup(false)
                                deleteConnection(selectedItem)
                            }}
                            onCancel={() => {
                                setShowPopup(false)
                            }}
                            itemName={selectedItem?.providerName}
                            itemType="CONNECTION"
                        />
                        <div className={styles.headerContainer}>
                            <div className={globalStyles.headerStyle} style={{ paddingLeft: "0" }}>
                                Data & Security
                            </div>
                            <div className={globalStyles.searchBarContainer} style={{ paddingRight: "0" }}>
                                <div className={globalStyles.searchBar}>
                                    {/* Search input commented out 
                                    <div className={globalStyles.searchWrapper}>
                                        <SearchIcon className={globalStyles.searchIcon} />
                                        <input
                                            type="text"
                                            placeholder="Search..."
                                            className={globalStyles.searchInput}
                                            onChange={handleSearchChange}
                                            value={searchQuery}
                                            onFocus={(e) => {
                                                e.target.style.width = "200px"
                                                e.target.placeholder = "Typing..."
                                            }}
                                            onBlur={(e) => {
                                                e.target.style.width = "80px"
                                                e.target.placeholder = "Search..."
                                            }}
                                        />
                                    </div>
                                    */}

                                    <div className={globalStyles.searchWrapper} style={{ marginRight: 0 }}>
                                        <GlobeIcon className={globalStyles.searchIcon} />
                                        <input
                                            type="text"
                                            placeholder="Eng"
                                            className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                                            readOnly
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className={styles.summaryContainer}>
                            <div className={globalStyles.interSummaryStyle} style={{ fontSize: "16px" }}>
                                See all the accounts and files you have connected on migrateGenie
                            </div>

                            <div className={styles.helpContainer}>
                                <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} onClick={() => { displayArticle("How does migrateGenie protect my files and API keys?") }} />
                                <span className={globalStyles.guideName}>How does migrateGenie protect my files and API keys?</span>
                            </div>
                        </div>

                        {bothDataEmpty ? (
                            <div className={styles.noDataContainer}>
                                <span className={globalStyles.interSummaryStyle} style={{ color: "#746B68" }}>
                                    Yet to connect accounts or platforms
                                </span>
                            </div>
                        ) : (
                            <div className={styles.contentContainer}>
                                <Tabs
                                    value={value}
                                    onChange={handleChange}
                                    variant="fullWidth"
                                    aria-label="data mapping tabs"
                                    className={styles.tab}
                                    sx={{
                                        width: "100%",
                                        "& .MuiTabs-indicator": {
                                            backgroundColor: "#FFFFFF",
                                        },
                                        "& .MuiTabs-scrollButtons": {
                                            color: "#FFFFFF",
                                        },
                                        "& .Mui-disabled": {
                                            color: "rgba(255, 255, 255, 0.3)",
                                        },
                                    }}
                                >
                                    <Tab
                                        key="1"
                                        label="SOURCE CONNECTIONS"
                                        sx={{
                                            color: "#DCDAD9",
                                            border: "1px solid #DCDAD9",
                                            borderRadius: 0,
                                            padding: "10px 15px",
                                            fontSize: "12px",
                                            flex: 1,
                                            transition: "all 0.2s ease",
                                            "&:hover": {
                                                border: "1px solid #EA5822",
                                                color: "#EA5822",
                                            },
                                            "&.Mui-selected": {
                                                color: "#EA5822",
                                                fontWeight: "700",
                                                fontSize: "12px",
                                                backgroundColor: "#FFFFFF",
                                                boxShadow: "3px -5px 7px 0px #00000026 !important",
                                                border: "1px solid #EA5822",
                                            },
                                        }}
                                    />
                                    <Tab
                                        key="2"
                                        label="TARGET CONNECTIONS"
                                        sx={{
                                            color: "#DCDAD9",
                                            border: "1px solid #DCDAD9",
                                            borderRadius: 0,
                                            padding: "10px 15px",
                                            fontSize: "12px",
                                            flex: 1,
                                            transition: "all 0.2s ease",
                                            "&:hover": {
                                                border: "1px solid #EA5822",
                                                color: "#EA5822",
                                            },
                                            "&.Mui-selected": {
                                                color: "#EA5822",
                                                fontWeight: "700",
                                                fontSize: "12px",
                                                backgroundColor: "#FFFFFF",
                                                boxShadow: "3px -5px 7px 0px #00000026 !important",
                                                border: "1px solid #EA5822",
                                            },
                                        }}
                                    />
                                </Tabs>

                                {deleteLoading ? (
                                    <div
                                        className={styles.loaderContainer}
                                        style={{
                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            padding: "100px 0",
                                            height: "300px",
                                        }}
                                    >
                                        <LoaderSpinner />
                                    </div>
                                ) : showNoDataMessage ? (
                                    <div className={`${styles.noDataContainer} ${styles.fullWidth}`}>
                                        <span className={globalStyles.interSummaryStyle} style={{ color: "#746B68" }}>
                                            {value === 0
                                                ? "No source connections available. Please connect a source platform."
                                                : "No target connections available. Please connect a target platform."}
                                        </span>
                                    </div>
                                ) : (
                                    <>
                                        <div className={styles.tableControls}>
                                            <div className={styles.filterContainer} ref={dropdownRef}>
                                                <button className={`${styles.dFlex} ${styles.buttonStyle}`} onClick={toggleFilterDropdown}>
                                                    <HiOutlineFunnel className={styles.iconStyle} />
                                                    <span className={styles.filterText}>{selectedFilter}</span>
                                                </button>
                                                {filterDropdownOpen && (
                                                    <div className={styles.filterDropdown}>
                                                        <div className={styles.scrollContainer}>
                                                            {filterOptions.map((option, index) => (
                                                                <div
                                                                    key={index}
                                                                    className={styles.filterOption}
                                                                    onClick={() => handleFilterSelect(option)}
                                                                >
                                                                    {option}
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                            <div className={globalStyles.searchWrapper} style={{ marginRight: "0" }}>
                                                <SearchIcon className={globalStyles.searchIcon} />
                                                <input
                                                    type="text"
                                                    placeholder="Search..."
                                                    className={globalStyles.searchInput}

                                                    onChange={handleTableSearchChange}
                                                    value={tableSearchQuery}
                                                    onFocus={(e) => {
                                                        e.target.style.width = "200px"
                                                        e.target.placeholder = "Typing..."
                                                    }}
                                                    onBlur={(e) => {
                                                        e.target.style.width = "80px"
                                                        e.target.placeholder = "Search..."
                                                    }}
                                                />
                                            </div>
                                        </div>

                                        <div className={styles.tableWrapper}>
                                            <div className={styles.tableContainer}>
                                                <table className={globalStyles.table}>
                                                    <thead className={globalStyles.tableHeader}>
                                                        <tr className={globalStyles.rowStyles}>
                                                            <th className={`${globalStyles.headerCell} ${styles.leftAlignHeader}`}>Domain</th>
                                                            <th className={`${globalStyles.headerCell} ${styles.leftAlignHeader}`}>Account</th>
                                                            <th className={`${globalStyles.headerCell} ${styles.leftAlignHeader}`}>{value === 0 ? "Source" : "Target"}</th>
                                                            <th className={`${globalStyles.headerCell} ${styles.leftAlignHeader}`}>Connected on</th>
                                                            <th className={`${globalStyles.headerCell} ${styles.leftAlignHeader}`}>Templates</th>
                                                            <th className={`${globalStyles.headerCell} ${styles.leftAlignHeader}`}>Remove</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {filteredData.length > 0 ? (
                                                            filteredData.map((data, index) => (
                                                                <tr key={index} className={globalStyles.tableRow}>
                                                                    <td className={`${styles.dataCell} ${styles.leftAlignCell}`} data-label="Domain">
                                                                        {data.domain || "-"}
                                                                    </td>
                                                                    <td className={`${styles.dataCell} ${styles.leftAlignCell}`} data-label="Account">
                                                                        {data.accountEmail || "-"}
                                                                    </td>
                                                                    <td className={`${styles.dataCell} ${styles.leftAlignCell}`} data-label={value === 0 ? "Source" : "Target"}>
                                                                        {data.providerName || "-"}
                                                                    </td>
                                                                    <td className={`${styles.dataCell} ${styles.leftAlignCell}`} data-label="Connected on">
                                                                        {data.connectedAt ? formatTimestamp(data.connectedAt) : "-"}
                                                                    </td>
                                                                    <td className={`${styles.dataCell} ${styles.leftAlignCell}`} data-label="Templates">
                                                                        {data.templateCount !== null && data.templateCount !== undefined ? data.templateCount : "-"}
                                                                    </td>
                                                                    <td className={`${styles.dataCell} ${styles.leftAlignCell}`} data-label="Remove">
                                                                        <button className={styles.deleteButton} onClick={() => {
                                                                            setSelectedItem(data)
                                                                            setShowPopup(true)
                                                                        }}>
                                                                            <img src="/assets/minus-sign.png" alt="Delete" className={styles.deleteIcon} />
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            ))
                                                        ) : (
                                                            <tr>
                                                                <td colSpan="5" className={`${styles.noResults} ${styles.leftAlignCell}`}>
                                                                    No matching results found
                                                                </td>
                                                            </tr>
                                                        )}
                                                    </tbody>
                                                </table>
                                            </div>

                                            {filteredData.length > 0 && totalPages > 0 && (
                                                <div className={styles.paginationContainer}>
                                                    <div className={styles.pageInfo}>
                                                        <span className={styles.paginationText}>Page no</span>
                                                        <div className={styles.pageButtons}>
                                                            <button
                                                                className={styles.arrowButton}
                                                                onClick={() => handlePageChange(0)}
                                                                disabled={currentPage === 0}
                                                            >
                                                                <FaAngleDoubleLeft />
                                                            </button>
                                                            <button
                                                                className={styles.arrowButton}
                                                                onClick={() => handlePageChange(currentPage - 1)}
                                                                disabled={currentPage === 0}
                                                            >
                                                                <FaChevronLeft />
                                                            </button>

                                                            {getVisiblePages().map((page, index) => {
                                                                if (page === "ellipsis-start" || page === "ellipsis-end") {
                                                                    return (
                                                                        <span key={`${page}-${index}`} className={styles.pageEllipsis}>
                                                                            ...
                                                                        </span>
                                                                    )
                                                                }
                                                                return (
                                                                    <button
                                                                        key={`page-${page}`}
                                                                        className={`${styles.pageButton} ${currentPage === page ? styles.active : ""}`}
                                                                        onClick={() => handlePageChange(page)}
                                                                    >
                                                                        {(page + 1).toString().padStart(2, "0")}
                                                                    </button>
                                                                )
                                                            })}

                                                            <button
                                                                className={styles.arrowButton}
                                                                onClick={() => handlePageChange(currentPage + 1)}
                                                                disabled={currentPage >= totalPages - 1}
                                                            >
                                                                <FaChevronRight />
                                                            </button>
                                                            <button
                                                                className={styles.arrowButton}
                                                                onClick={() => handlePageChange(totalPages - 1)}
                                                                disabled={currentPage >= totalPages - 1}
                                                            >
                                                                <FaAngleDoubleRight />
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div className={styles.rowsPerPageContainer}>
                                                        <span className={styles.paginationText}>Show</span>
                                                        <div className={styles.rowsButtons}>
                                                            <button
                                                                className={`${styles.rowsButton} ${rowsPerPage === 25 ? styles.active : ""}`}
                                                                onClick={() => handleRowsPerPageChange(25)}
                                                            >
                                                                25
                                                            </button>
                                                            <button
                                                                className={`${styles.rowsButton} ${rowsPerPage === 50 ? styles.active : ""}`}
                                                                onClick={() => handleRowsPerPageChange(50)}
                                                            >
                                                                50
                                                            </button>
                                                            <button
                                                                className={`${styles.rowsButton} ${rowsPerPage === 75 ? styles.active : ""}`}
                                                                onClick={() => handleRowsPerPageChange(75)}
                                                            >
                                                                75
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    )
}