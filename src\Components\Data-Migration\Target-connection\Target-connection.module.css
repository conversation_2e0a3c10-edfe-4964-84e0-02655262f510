.dFlex {
  display: flex;
}

.section {
  /* flex: 1;
  width: 50%; */
  padding: 20px 0 20px 0;
  padding-right: 5px;
}

.customDropdownContainer {
  margin-top: 5px;
  width: 100%;
  height: 50%;
}

.customDropdownSelect {
  background-color: #ffffff;
  border: 1px solid #dcdad9;
  border-radius: 3px;
  width: 100%;
  height: 45px;
  margin-top: 15px;
}

.customDropdownSelect .MuiSelect-select {
  padding: 12px 16px;
  height: 12px;
  display: flex;
  align-items: center;
  color: #1a1a1a;
}

.customDropdownSelect .MuiSelect-select[value=""] {
  color: #64748b;
}

.customDropdownSelect .MuiSelect-icon {
  right: 12px;
  color: #64748b;
}

.customDropdownSelect .MuiOutlinedInput-root {
  border-color: #ccc;
}

.customDropdownSelect .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: grey !important;
}

.MuiMenuItem-root {
  padding: 12px 16px;
  color: #1a1a1a;
  min-height: 24px;
}

.MuiMenuItem-root:hover {
  background-color: #f1f5f9;
}


.MuiMenuItem-root.Mui-selected,
.MuiMenuItem-root.Mui-selected:hover {
  background-color: #f1f5f9;
}

.MuiMenu-paper {
  margin-top: 4px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.customDialog .MuiDialogContent-root {
  text-align: center;
  padding: 20px;
  font-size: 16px;
  background-color: #170903 !important;
}

.loaderContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader {
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-left: 2px solid #ffffff;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.targetGraphic {
  height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.targetConnectorContainer {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.targetGraphicImage {
  width: 100%;
  height: auto;
  display: block;
}

.selectedTargetLogoOverlay {
  position: absolute;
  top: 15%;
  left: 80%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 20%;
  /* Make the overlay size relative to container */
}

.selectedTargetLogo {
  width: 100%;
  /* Take full width of parent overlay */
  aspect-ratio: 1/1;
  /* Maintain perfect circle */
  border-radius: 50%;
  background-color: rgba(254, 229, 220, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.selectedTargetLogo img {
  width: 60%;
  height: 60%;
  object-fit: contain;
}


@media (max-width: 768px) {
  .selectedTargetLogoOverlay {
    width: 16%;
  }
}

@media (max-width: 480px) {
  .selectedTargetLogoOverlay {
    width: 12%;
  }
}

.targetConnectorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  position: relative;
}

.selectedTargetLogoOverlay {
  position: absolute;
}

.selectedTargetLogo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.inputContainer {
  margin-top: 15px;
  width: 97%;
  position: relative;
}

.formControl {
  width: 100%;
  padding: 8px 12px;
  font-family: "Inter";
  font-weight: 400;
  font-size: 16px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}


.passwordInput {
  padding-bottom: 5px;
}

.eyeIconContainer {
  position: absolute;
  top: 40%;
  right: -20px;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 0;
  z-index: 2;
}

.eyeIcon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.eyeIcon:hover {
  color: #4b5563;
}
