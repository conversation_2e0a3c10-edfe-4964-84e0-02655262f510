import React from "react";
import PropTypes from 'prop-types';
import { HiChevronUp, HiChevronDown } from "react-icons/hi2";
import styles from "../../../MigrationDetails.module.css";
import globalStyles from "../../../../globalStyles.module.css";
import bseStyles from "../BSE.module.css";

// Add custom styles for nested tables
const customStyles = {
    nestedTable: {
        marginLeft: "5px",
        boxShadow: "0 0 5px rgba(0,0,0,0.1)",
    },
    indentedCell: {
        padding: "20px"
    }
};

// RecordRow component for recursive rendering of record rows with their children
const RecordRow = ({
    record,
    index,
    level = 0,
    parentIndex = "",
    expandedRows,
    toggleRowExpand,
    subIsLoading,
    subTableData,
    formatTimestamp,
    getRowClass,
    getStatusClass,
    openPayloadDialog,
    activeDependentTabs,
    toggleTab
}) => {
    // Create a display index based on level and position
    const displayIndex = parentIndex ? `${parentIndex}.${index + 1}` : index + 1;

    // Create a unique key for this record using entityType and source_id
    const recordKey = `${record.entityType || 'unknown'}_${record.source_id || record.sourceId}`;

    // Check if this record is expanded
    const isExpanded = expandedRows[recordKey];

    // Get any children from subTableData if available, or use record.dependents/children
    const childRecords = subTableData[recordKey] || [];
    const hasDependents = record.dependents && record.dependents.length > 0;

    // Increase indentation for nested levels
    const indentStyle = { paddingLeft: `${level * 20}px` };

    return (
        <React.Fragment>
            <tr className={getRowClass(record.status)}>
                <td style={{ ...indentStyle, textAlign: 'center', padding: "0px 2px" }}>{displayIndex}</td>
                <td>{record.source_id || record.sourceId || "-"}</td>
                <td>{record.target_id || record.targetId || "-"}</td>
                <td>{formatTimestamp(record.start_time || record.startTime)}</td>
                <td>{formatTimestamp(record.end_time || record.endTime)}</td>
                <td>
                    <span className={`${bseStyles.statusIndicator} ${getStatusClass(record.status)}`}>
                        {record.status || "unknown"}
                    </span>
                </td>                
                <td className={globalStyles.cell}>
                    {(record.status === "failed" || record.status === "FAILED" || record.status === "ERROR") && (
                        <button className={bseStyles.payloadButton} onClick={() => openPayloadDialog(record)}>
                            <i className="fa fa-external-link" aria-hidden="true"></i>
                        </button>
                    )}
                </td>
                <td>
                    <div className={styles.actionButtons} style={{ justifyContent: "space-between" }}>
                        {/* Only show expand button if there are dependents/children */}
                        {(hasDependents || (record.children && record.children.length > 0)) && (
                            <div onClick={() => toggleRowExpand(record.batchId, record.source_id || record.sourceId, record.key_id || record.id, record.entityType)}>
                                {isExpanded ? (
                                    <HiChevronUp className={globalStyles.closeIcon} />
                                ) : (
                                    <HiChevronDown className={globalStyles.closeIcon} />
                                )}
                            </div>
                        )}
                    </div>
                </td>
            </tr>

            {/* Render expanded row with dependents/children if expanded */}            {isExpanded && ((hasDependents || childRecords.length > 0)) && (
                <tr className={bseStyles.expandedRow}>
                    <td colSpan="8" style={{ padding: "4px" }}>
                        <div className={bseStyles.expandedContent} style={{ padding: "0px" }}>
                            {subIsLoading[recordKey] ? (
                                <div style={{ textAlign: "center" }}>Loading details...</div>
                            ) : (
                                <>
                                    {/* Show tabs if record has multiple dependents types */}
                                    {hasDependents && record.dependents.length > 1 && (
                                        <div className={bseStyles.tabsContainer}>
                                            {record.dependents.map((dependent, idx) => {
                                                // Get the dependent type (first key of the object)
                                                const dependentType = Object.keys(dependent)[0];
                                                return (
                                                    <button
                                                        key={dependentType}
                                                        className={`${bseStyles.tabButton} ${activeDependentTabs[recordKey] === idx ? bseStyles.activeTab : ''}`}
                                                        onClick={() => toggleTab(recordKey, idx)}
                                                    >
                                                        {dependentType.charAt(0).toUpperCase() + dependentType.slice(1)}
                                                    </button>
                                                );
                                            })}
                                        </div>
                                    )}

                                    <table className={styles.subTable} style={{ ...customStyles.nestedTable, width: "100%" }}>                                        <thead>
                                        <tr>
                                            <th className={`${globalStyles.headerCell} ${styles.centerColumn}`}>S.No</th>
                                            <th className={globalStyles.headerCell}>Source ID</th>
                                            <th className={globalStyles.headerCell}>Record ID</th>
                                            <th className={globalStyles.headerCell}>Start Time</th>
                                            <th className={globalStyles.headerCell}>End Time</th>
                                            <th className={globalStyles.headerCell}>Status</th>
                                            <th className={globalStyles.headerCell}>See Payload</th>
                                            <th className={globalStyles.headerCell}>Actions</th>
                                        </tr>
                                    </thead>
                                        <tbody>
                                            {/* Recursively render child records */}
                                            {childRecords.map((childRecord, childIndex) => (
                                                <RecordRow
                                                    key={childRecord.key_id || childRecord.id || childRecord.sourceId || childIndex}
                                                    record={childRecord}
                                                    index={childIndex}
                                                    level={level + 1}
                                                    parentIndex={displayIndex}
                                                    expandedRows={expandedRows}
                                                    toggleRowExpand={toggleRowExpand}
                                                    subIsLoading={subIsLoading}
                                                    subTableData={subTableData}
                                                    formatTimestamp={formatTimestamp}
                                                    getRowClass={getRowClass}
                                                    getStatusClass={getStatusClass}
                                                    openPayloadDialog={openPayloadDialog}
                                                    activeDependentTabs={activeDependentTabs}
                                                    toggleTab={toggleTab}
                                                />
                                            ))}
                                        </tbody>
                                    </table>
                                </>
                            )}
                        </div>
                    </td>
                </tr>
            )}
        </React.Fragment>
    );
};

// PropTypes validation
RecordRow.propTypes = {
    record: PropTypes.shape({
        id: PropTypes.string,
        key_id: PropTypes.string,
        sourceId: PropTypes.string,
        source_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        target_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        targetId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        startTime: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        start_time: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        endTime: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        end_time: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        status: PropTypes.string.isRequired,
        entityType: PropTypes.string,
        batchId: PropTypes.string,
        dependents: PropTypes.array,
        children: PropTypes.array
    }).isRequired,
    index: PropTypes.number.isRequired,
    level: PropTypes.number,
    parentIndex: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    expandedRows: PropTypes.object.isRequired,
    toggleRowExpand: PropTypes.func.isRequired,
    subIsLoading: PropTypes.object.isRequired,
    subTableData: PropTypes.object.isRequired,
    formatTimestamp: PropTypes.func.isRequired,
    getRowClass: PropTypes.func.isRequired,
    getStatusClass: PropTypes.func.isRequired,
    openPayloadDialog: PropTypes.func.isRequired,
    activeDependentTabs: PropTypes.object.isRequired,
    toggleTab: PropTypes.func.isRequired
};

export default RecordRow;
