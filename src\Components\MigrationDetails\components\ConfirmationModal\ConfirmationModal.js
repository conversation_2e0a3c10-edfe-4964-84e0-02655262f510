import React from "react";
import PropTypes from "prop-types";
import { Dialog, DialogContent } from "@mui/material";

const ConfirmationModal = ({
    open,
    onClose,
    onConfirm,
    title = "Are you sure?",
    confirmText = "Yes",
    cancelText = "Cancel",
    confirmButtonProps = {},
    cancelButtonProps = {},
    children
}) => {
    return (
        <Dialog open={open} onClose={onClose} PaperProps={{
            style: {
                borderRadius: 20,
                boxShadow: '0 8px 32px rgba(0,0,0,0.18)',
                fontFamily: 'Inter, sans-serif',
                minWidth: 420,
                maxWidth: 520,
                minHeight: 220,
                maxHeight: 420
            }
        }}>
            <DialogContent style={{
                padding: '40px 32px',
                textAlign: 'center',
                fontFamily: 'Inter, sans-serif',
                background: '#fff',
                borderRadius: 20,
                minHeight: 180,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
            }}>
                <div style={{
                    fontSize: 20,
                    fontWeight: 600,
                    marginBottom: 18,
                    color: '#222',
                    fontFamily: 'Inter, sans-serif',
                    letterSpacing: 0.1,
                    textAlign: 'center',
                    width: '100%'
                }}>
                    {title}
                </div>
                <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    {children}
                </div>
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    gap: 16,
                    marginTop: 24,
                    fontFamily: 'Inter, sans-serif',
                    width: '100%'
                }}>
                    <button
                        onClick={onConfirm}
                        style={{
                            minWidth: 110,
                            padding: '10px 0',
                            borderRadius: 6,
                            border: 'none',
                            background: '#EF8963',
                            color: '#fff',
                            fontWeight: 500,
                            fontSize: 15,
                            fontFamily: 'Inter, sans-serif',
                            boxShadow: '0 2px 8px rgba(239,137,99,0.08)',
                            cursor: 'pointer',
                            transition: 'background 0.2s',
                            outline: 'none',
                            ...confirmButtonProps.style
                        }}
                        {...confirmButtonProps}
                    >
                        {confirmText}
                    </button>
                    <button
                        onClick={onClose}
                        style={{
                            minWidth: 110,
                            padding: '10px 0',
                            borderRadius: 6,
                            border: 'none',
                            background: '#f4f4f4',
                            color: '#333',
                            fontWeight: 500,
                            fontSize: 15,
                            fontFamily: 'Inter, sans-serif',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
                            cursor: 'pointer',
                            transition: 'background 0.2s',
                            outline: 'none',
                            ...cancelButtonProps.style
                        }}
                        {...cancelButtonProps}
                    >
                        {cancelText}
                    </button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

ConfirmationModal.propTypes = {
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    title: PropTypes.string,
    confirmText: PropTypes.string,
    cancelText: PropTypes.string,
    confirmButtonProps: PropTypes.object,
    cancelButtonProps: PropTypes.object,
    children: PropTypes.node
};

export default ConfirmationModal;
