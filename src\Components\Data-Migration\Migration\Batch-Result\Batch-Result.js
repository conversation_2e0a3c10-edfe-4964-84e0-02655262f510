import React, {useEffect, useRef, useState} from "react";
import styles from "./Batch-Result.module.css";
import globalStyles from "../../../globalStyles.module.css";
import {HiArrowPath, HiChevronDown, HiXMark} from "react-icons/hi2";
import {
    getRecordByBatchConversations,
    getRecordsByBatchId,
    getWorkflowErrorLog,
    retryRecordMigration,
    // transformData
} from "../../../apiService";
import {HiChevronUp} from "react-icons/hi";
import LoaderSpinner from "../../../loaderspinner";
import websocketService from "../../../websocketService";
import {webSocket} from "rxjs/webSocket";
import {Dialog, DialogContent} from "@mui/material";
import Feedback from "../Feedback/Feedback";
import PayloadDialog from "../../../Payload/Payload";

const BatchResult = ({ close, batchId, hasError }) => {
    const [selectedPayload, setSelectedPayload] = useState(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const apiCalledRef = useRef(false);
    const [migrationLogs, setMigrationLogs] = useState([]);
    const [expandedRows, setExpandedRows] = useState({});
    const [subTableData, setSubTableData] = useState({});
    const [isLoading, setIsLoading] = useState({});
    const [errorLogData, setErrorLogData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [retryRecordLoading, setRetryRecordLoading] = useState(false);
    const [retryRecordId, setRetryRecordId] = useState('');
    const [subTableActiveFilter, setSubTableActiveFilter] = useState("all");
    const [activeFilter, setActiveFilter] = useState("all");
    const [updateBatch, setUpdateBatch] = useState(false);

    const showPages = [5, 10, 15];

    const filterOptions = [
        { id: "all", label: "See all" },
        { id: "noErrors", label: "Records Passed" },
        { id: "withErrors", label: "Records Failed" },
    ];

    // Filter main table data based on active filter
    const filteredMainData = migrationLogs.filter(log => {
        if (activeFilter === "all") return true;
        if (activeFilter === "noErrors") return log.status === "success";
        if (activeFilter === "withErrors") return log.status !== "success";
        return true;
    });

    const [activeTabs, setActiveTabs] = useState('');


    const handleTabChange = (sourceId, tabKey) => {
        setActiveTabs(prev => ({
            ...prev,
            [sourceId]: tabKey
        }));
    };


    const initializeActiveTab = (sourceId) => {
        if (subTableData[sourceId] && Object.keys(subTableData[sourceId]).length > 0) {
            const firstKey = Object.keys(subTableData[sourceId])[0];
            handleTabChange(sourceId, firstKey);
        }
    };
    useEffect(() => {

        Object.keys(expandedRows).forEach(sourceId => {

            if (expandedRows[sourceId] &&
                subTableData[sourceId] &&
                Object.keys(subTableData[sourceId]).length > 0 &&
                !activeTabs[sourceId]) {

                const firstKey = Object.keys(subTableData[sourceId])[0];
                handleTabChange(sourceId, firstKey);
            }
        });
    }, [subTableData, expandedRows]);

    // Filter sub-table data based on subTableActiveFilter
    const filterSubTableData = (data) => {
        if (!data) return [];
        if (subTableActiveFilter === "all") return data;
        if (subTableActiveFilter === "noErrors") return data.filter(item => item.status === "success");
        if (subTableActiveFilter === "withErrors") return data.filter(item => item.status !== "success");
        return data;
    };

    const totalPages = Math.ceil(filteredMainData.length / itemsPerPage);
    const paginatedData = filteredMainData.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

    const handlePageChange = (page) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page);
        }
    };

    const handleShowPage = (newItemsPerPage) => {
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1);
    };

    const openPayloadDialog = async (log) => {
        if (log.errorId) {
            try {
                const errorData = await getWorkflowErrorLog(log.errorId);

                setErrorLogData(errorData.response[0].transformationInputModel.source);
                setSelectedPayload(log);
                setIsDialogOpen(true);

                console.log("Error Log Data:", errorData.response[0].transformationInputModel.source);
            } catch (error) {
                console.error("Error fetching error log:", error);
            }
        } else {
            setSelectedPayload(log);
            setIsDialogOpen(true);

        }
    };

    const fetchSubTableData = async (sourceId, batchId, targetId) => {
        if (!sourceId || !targetId) return;

        setIsLoading(prev => ({ ...prev, [sourceId]: true }));

        try {
            const response = await getRecordByBatchConversations(batchId, targetId) || [];
            setSubTableData(prev => ({
                ...prev,
                [sourceId]: response
            }));
            initializeActiveTab(sourceId);
        } catch (error) {
            console.error('Error fetching sub-table data:', error);
            setSubTableData(prev => ({
                ...prev,
                [sourceId]: []
            }));
        } finally {
            setIsLoading(prev => ({ ...prev, [sourceId]: false }));
        }
    };

    const toggleRowExpand = (batchId, sourceId, targetId) => {
        const willExpand = !expandedRows[sourceId];

        setExpandedRows(prev => ({
            ...prev,
            [sourceId]: willExpand
        }));

        if (willExpand && !subTableData[sourceId]) {
            fetchSubTableData(sourceId, batchId, targetId);
        }
    };

    const closePayloadDialog = () => {
        setIsDialogOpen(false);
        setSelectedPayload(null);
    };

    useEffect(() => {
        const fetchData = async () => {
            if (batchId && !apiCalledRef.current) {
                setLoading(true);
                apiCalledRef.current = true;
                try {
                    const res = await getRecordsByBatchId(batchId);
                    setLoading(false);
                    setMigrationLogs(res);
                    console.log("API Response:", res);
                } catch (error) {
                    throw error;
                }
            }
        };

        fetchData();
    }, [batchId]);

    const formatDate = (timestamp) => {
        const date = new Date(timestamp);
        return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
    };

    const handleFilterChange = (filterId) => {
        setActiveFilter(filterId);
        setCurrentPage(1);
    };

    const handleSubTableFilterChange = (filterId) => {
        setSubTableActiveFilter(filterId);
    };

    const getDialogTitle = () => {
        if (selectedPayload?.status === "error") {
            return `Error Log : ${selectedPayload.errorId}`;
        } else {
            return `Ticket JSON : ${selectedPayload?.subStepId}`;
        }
    };

    const formatJsonForDisplay = (jsonString) => {
        try {
            const parsed = typeof jsonString === "string" ? JSON.parse(jsonString) : jsonString;
            return JSON.stringify(parsed, null, 2);
        } catch (e) {
            return jsonString;
        }
    };

    const getResponseBody = () => {
        if (!selectedPayload) return "";
        
        if (selectedPayload.status === "error") {
            return selectedPayload.additionalDetails.response.body;
        } else {
            try {
                const responseData = JSON.parse(selectedPayload.additionalDetails.response.body);
                return JSON.stringify(responseData, null, 2);
            } catch (e) {
                return selectedPayload.additionalDetails.response.body;
            }
        }
    };

    const retryRecord = (id) => {
        if (id) {
            const payload = { errorId: id };
            setRetryRecordLoading(true);
            setRetryRecordId(id);
            setUpdateBatch(true);

            retryRecordMigration(payload)
                .then(res => {
                    const temp = res;
                    setRetryRecordLoading(false);
                    setRetryRecordId(null);


                    if (temp && temp.message === 'success') {
                        getWorkflowBatchData();

                    }
                })
                .catch(err => {
                    getWorkflowBatchData();
                    setRetryRecordLoading(false);
                    setRetryRecordId(null);
                    // setRetryRecordLoading(false);
                    // setRetryRecordId(undefined);
                });
        }
    };

    const getWorkflowBatchData = async () => {
        try {
            const response = await getRecordsByBatchId(batchId);
            const data = response;

            if (!data) return;

            const updatedRecords = await Promise.all(
                data.map(async (obj, index) => {
                    obj.index = index + 1;

                    const isPending = !['success', 'error', 'skipped'].includes(obj.status);
                    if (isPending) {
                        if (websocketService.isOpen()) {
                            listenToTopic(obj.subStepId);
                        } else {
                            try {
                                await websocketService.initializeWebSocketConnection();
                                if (websocketService.isOpen()) {
                                    listenToTopic(obj.subStepId);
                                }
                            } catch (error) {
                                console.error('WebSocket connection failed:', error);
                            }
                        }
                    }

                    return obj;
                })
            );

            setMigrationLogs(updatedRecords);

        } catch (error) {
            console.error('Error while fetching workflow details', error);


        }
    };

    const listenToTopic = (stepId) => {
        websocketService.listenToTopic(stepId).then(data => {
            const result = JSON.parse(data.body);
            if (result) {
                updateRecord(result);
            }
        });


    }

    const updateRecord = (obj) => {
        if (obj.isSubStep && migrationLogs.length > 0) {
            setMigrationLogs((prevLogs) =>
                prevLogs.map((item) => {
                    if (item.subStepId === obj.subStepId) {
                        if (['success', 'error', 'skipped'].includes(obj.status)) {
                            const topic = `/topic/${obj.subStepId}.response`;
                            websocketService.unsubscribeTopic(topic);
                        }
                        return { ...item, status: obj.status };
                    }
                    return item;
                })
            );
        }
    };

    const retryAllFailedRecords = async () => {
        for (const log of migrationLogs) {
            if (log.status !== 'success' && log.errorId) {
                try {
                    await retryRecord(log.errorId);
                } catch (error) {
                    console.error(`Failed to retry record with errorId: ${log.errorId}`, error);
                }
            }
        }
    };



    const getPayloadData = () => {
    if (!selectedPayload) return ""

    // If we have error log data from the API, use it
    if (errorLogData) {
      return JSON.stringify(errorLogData, null, 2)
    } else if (selectedPayload.status === "error") {
      return JSON.stringify(selectedPayload.additionalDetails.payload, null, 2)
    } else {
      try {
        const responseData = JSON.parse(selectedPayload.additionalDetails.response.body)
        return JSON.stringify(responseData, null, 2)
      } catch (e) {
        return selectedPayload.additionalDetails.response.body
      }
    }
  }

  const getSourceObject = () => {
    if (errorLogData && errorLogData.source) {
      return errorLogData.source
    }
    return ""
  }

    const statusColor = (status) => {
        return selectedPayload && selectedPayload.additionalDetails.response.statusCode < 400 ? 
            styles.statusSuccess : styles.statusError;
    };

    // Helper for status icon
const getStatusIcon = (status, failed) => {
  if (status === "success") return "✓";
  if (status === "error" || status === "failed" || failed > 0) return "✗";
  return "⏳";
};

    return (
        <div  className={styles.container}>
            {loading ? <LoaderSpinner/> : (
                <div>
                    <div style={{display: "flex", justifyContent: "flex-end", marginBottom: "20px"}}>
                        <HiXMark className={globalStyles.closeIcon} style={{cursor: "pointer"}} onClick={() => close(updateBatch)}/>
                    </div>
                    <div className={styles.dFlex} style={{justifyContent: "space-between"}}>
                        <div className={globalStyles.selectionName} style={{marginBottom: "20px"}}>MIGRATION RESULT LOG</div>
                        {
                            hasError && (
                                <div className={styles.dFlex} style={{gap: "10px", marginBottom: "20px"}}>
                                    <span className={globalStyles.interStyle} style={{fontSize: "12px"}}>Filter:</span>
                                    {filterOptions.map((option) => (
                                        <button
                                            key={option.id}
                                            className={`${styles.filterButton} ${activeFilter === option.id ? styles.activeFilter : ''}`}
                                            onClick={() => handleFilterChange(option.id)}
                                        >
                                            {option.label}
                                        </button>
                                    ))}
                                </div>
                            )
                        }
                    </div>
                    <div className={styles.tableContainer}>
                        <table className={globalStyles.table}>
                            <thead className={globalStyles.tableHeader}>
                            <tr className={globalStyles.rowStyles}>
                                <th className={globalStyles.headerCell}>Status</th>
                                <th className={globalStyles.headerCell}>Source ID</th>
                                <th className={globalStyles.headerCell}>Target ID</th>
                                {hasError && <th className={globalStyles.headerCell}>Retry</th>}
                                <th className={globalStyles.headerCell}>See payload</th>
                                <th className={globalStyles.headerCell}>Expand</th>
                            </tr>
                            </thead>
                            <tbody className={styles.tableBody}>
                            {paginatedData.length > 0 ? (
                                paginatedData.map((log) => (
                                    <React.Fragment key={log.id}>
                                        <tr className={globalStyles.tableRow}>
                                            <td className={styles.migrationCell}>
                                                <span className={
                                                    log.status === "success"
                                                      ? styles.successIcon
                                                      : log.status === "error" || log.status === "failed" || log.failed > 0
                                                      ? styles.failedIcon
                                                      : styles.inProgressIcon
                                                  }>
                                                    {getStatusIcon(log.status, log.failed)}
                                                  </span>
                                            </td>
                                            <td className={styles.migrationCell}>{log.sourceId}</td>
                                            <td className={styles.migrationCell}>{log.id}</td>
                                            {hasError && (
                                                <td className={styles.migrationCell}>
                                                    {log.status !== 'success' && (
                                                        retryRecordId === log.errorId && retryRecordLoading ? (
                                                            <HiArrowPath
                                                                className={`${globalStyles.closeIcon} ${styles.spin}`}
                                                                style={{ cursor: "not-allowed" }}
                                                            />
                                                        ) : (
                                                            <HiArrowPath
                                                                className={globalStyles.closeIcon}
                                                                style={{ cursor: "pointer" }}
                                                                onClick={() => retryRecord(log.errorId)}
                                                            />
                                                        )
                                                    )}
                                                </td>
                                            )}
                                            <td className={styles.migrationCell}>
                                                <button
                                                    className={styles.payloadButton}
                                                    onClick={() => openPayloadDialog(log)}
                                                >
                                                    <i className="fa fa-external-link" aria-hidden="true"></i>
                                                </button>
                                            </td>
                                            <td className={styles.migrationCell}
                                                onClick={() => toggleRowExpand(log.batchId, log.sourceId, log.id)}>
                                                {expandedRows[log.sourceId] ? (
                                                    <HiChevronUp className={globalStyles.closeIcon}/>
                                                ) : (
                                                    <HiChevronDown className={globalStyles.closeIcon}/>
                                                )}
                                            </td>
                                        </tr>
                                        {expandedRows[log.sourceId] && (
                                            <tr className={styles.expandedRow}>
                                                <td colSpan={hasError ? 6 : 5} className={globalStyles.cell}>
                                                    <div className={styles.expandedContent}>
                                                        {isLoading[log.sourceId] ? (
                                                            <div className={styles.loading}>Loading...</div>
                                                        ) : Object.keys(subTableData[log.sourceId] || {}).length > 0 ? (
                                                                <div className={styles.tabbedContainer}>
                                                                    {/* Tabs Navigation */}
                                                                    <div className={styles.tabsHeader}>
                                                                        {Object.keys(subTableData[log.sourceId]).map((tabKey, idx) => (
                                                                            <button
                                                                                key={tabKey}
                                                                                className={`${styles.tabButton} ${activeTabs[log.sourceId] === tabKey ? styles.activeTab : ''}`}
                                                                                onClick={() => handleTabChange(log.sourceId, tabKey)}
                                                                            >
                                                                                <span>{tabKey.charAt(0).toUpperCase() + tabKey.slice(1)}</span>
                                                                                <span> ({subTableData[log.sourceId][tabKey].length})</span>
                                                                            </button>
                                                                        ))}
                                                                    </div>

                                                                    {/* Tab Content */}
                                                                    <div className={styles.tabContent}>
                                                                        {Object.keys(subTableData[log.sourceId]).map((tabKey) => (
                                                                            <div
                                                                                key={tabKey}
                                                                                className={`${styles.tabPanel} ${activeTabs[log.sourceId] === tabKey ? styles.activePanel : styles.hiddenPanel}`}
                                                                            >
                                                                                <table className={styles.subTable} style={{width: "100%"}}>
                                                                                    <thead>
                                                                                    <tr>
                                                                                        <th className={globalStyles.headerCell}>SNo</th>
                                                                                        <th className={globalStyles.headerCell}>Log Message</th>
                                                                                        <th className={globalStyles.headerCell}>Status</th>
                                                                                        <th className={globalStyles.headerCell}>Actions</th>
                                                                                    </tr>
                                                                                    </thead>
                                                                                    <tbody>
                                                                                    {subTableData[log.sourceId][tabKey].length > 0 ? (
                                                                                        subTableData[log.sourceId][tabKey].map((subItem, index) => (
                                                                                            <tr key={subItem.id} className={styles.subTableRow}>
                                                                                                <td className={globalStyles.cell}>{index + 1}</td>
                                                                                                <td className={globalStyles.cell}>{subItem.logMessage}</td>
                                                                                                <td className={globalStyles.cell}>
                                                                                                    <span className={
                                                                                                        subItem.status === "success"
                                                                                                          ? styles.successIcon
                                                                                                          : subItem.status === "error" || subItem.status === "failed" || subItem.failed > 0
                                                                                                          ? styles.failedIcon
                                                                                                          : styles.inProgressIcon
                                                                                                        }>
                                                                                                        {getStatusIcon(subItem.status, subItem.failed)}
                                                                                                      </span>
                                                                                                </td>
                                                                                                <td className={globalStyles.cell}>
                                                                                                    {subItem.status !== 'success' && (
                                                                                                        retryRecordId === subItem.errorId && retryRecordLoading ? (
                                                                                                            <HiArrowPath
                                                                                                                className={`${globalStyles.closeIcon} ${styles.spin}`}
                                                                                                                style={{cursor: "not-allowed"}}
                                                                                                            />
                                                                                                        ) : (
                                                                                                            <HiArrowPath
                                                                                                                className={globalStyles.closeIcon}
                                                                                                                style={{cursor: "pointer"}}
                                                                                                                onClick={() => retryRecord(subItem.errorId)}
                                                                                                            />
                                                                                                        )
                                                                                                    )}
                                                                                                </td>
                                                                                            </tr>
                                                                                        ))
                                                                                    ) : (
                                                                                        <tr>
                                                                                            <td colSpan={4} className={styles.noData}>No records found</td>
                                                                                        </tr>
                                                                                    )}
                                                                                    </tbody>
                                                                                </table>
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                </div>


                                                        ) : (
                                                            <div className={styles.noData}>No details available</div>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        )}
                                    </React.Fragment>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan={hasError ? 6 : 5} className={styles.noData}>No records found</td>
                                </tr>
                            )}
                            </tbody>
                        </table>
                    </div>

                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "10px",
                            marginTop: "30px",
                            marginBottom: "50px",
                        }}
                    >
                        <span className={globalStyles.interStyle} style={{color: "#170903", width: "80px"}}>
                            Page no
                        </span>
                        {[...Array(totalPages)].map((_, index) => (
                            <button
                                key={index + 1}
                                className={`${currentPage === index + 1 ? globalStyles.activePage : globalStyles.pageButton}`}
                                onClick={() => handlePageChange(index + 1)}
                            >
                                {String(index + 1).padStart(2, "0")}
                            </button>
                        ))}
                        <span className={globalStyles.interStyle} style={{color: "#170903", marginLeft: "30px"}}>
                            Show
                        </span>
                        {showPages.map((pageLimit, index) => (
                            <button
                                key={index}
                                className={`${itemsPerPage === pageLimit ? globalStyles.activePage : globalStyles.pageButton}`}
                                onClick={() => handleShowPage(pageLimit)}
                            >
                                {String(pageLimit).padStart(2, "0")}
                            </button>
                        ))}

                        {hasError &&
                            <div style={{display: "flex", justifyContent: "flex-end", width: "100%"}}>
                                <button className={globalStyles.mainButton} style={{marginLeft: "auto", margin: "0"}} onClick={retryAllFailedRecords}>
                                    Retry all failed records
                                </button>
                            </div>
                        }
                    </div>
                    <Dialog open={isDialogOpen} maxWidth="md"
                            fullWidth PaperProps={{style: {width: "70%"}}}>
                        <DialogContent
                            sx={{backgroundColor: "#170903", padding: "0"}}>
                            <PayloadDialog
                                isOpen={isDialogOpen}
                                selectedPayload={selectedPayload}
                                errorLogData={errorLogData}
                                onClose={closePayloadDialog}
                            />
                        </DialogContent>

                    </Dialog>


                </div>
            )}
        </div>
    );
};

export default BatchResult;
