.headerStyle {
    font-family: Poppins;
    font-weight: 600;
    font-size: 27px;
    color: #EA5822;
    padding-left: 20px;
}

.tab {
    border: 1px solid #EFEEED;
    display: flex !important;
    justify-content: stretch !important;
}

.interStyle {
    font-family: Inter;
    font-weight: 400;
    font-size: 12px;
    color: #514742;
}

.poppinsStyle {
    font-family: Poppins;
    font-weight: 400;
    font-size: 12px;
    color: #514742;
}

.poppinsHeaderStyle {
    font-family: Poppins;
    font-weight: 600;
    font-size: 12px;
    color: #746B68;
}

.selectionName {
    font-family: Poppins;
    font-weight: 700;
    font-size: 12px;
    color: #EA5822;
}

.guideName {
    font-family: Inter;
    font-weight: 400;
    font-size: 12px;
    color: #746B68;
}

.interSummaryStyle {
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    color: #170903;
    line-height: 24px;
    align-items: start;
}

.helpIcon {
    height: 14.25px;
    width: 14.25px;
    /*padding-top: 20px;*/
    margin-right: 5px;
    cursor: pointer;
    margin-left: 5px;
}

.mainButton {
    background-color: #EF8963;
    height: 40px;
    padding: 10px 45px;
    border-radius: 5px;
    color: #170903;
    font-size: 16px;
    font-weight: 500;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    border: none;
}
.mainButton:hover{
    background-color: #ec9d7f;
}
.connectedSuccess {
    background-color: #170903;
    height: 40px;
    padding: 10px 45px;
    border-radius: 5px;
    color: #F3F3F2;
    font-size: 16px;
    font-weight: 500;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    border: none;
}

.searchBarContainer {
    display: flex;
    justify-content: flex-end;
    /*width: 100%;*/
    padding: 10px;

}

.searchBar {
    display: flex;
    gap: 10px;
    align-items: center;
}

.searchWrapper {
    display: flex;
    align-items: center;
    position: relative;
    margin-right: 20px;
}

.searchIcon {
    position: absolute;
    left: 10px;
    width: 20px;
    height: 20px;
    color: gray;
    padding-bottom: 10px;
}

.searchInput {
    width: 80px;
    padding: 10px 12px 10px 40px;
    border-radius: 7px;
    background-color: #FFFFFF;
    border: 1px solid #DCDAD9;
    font-family: "Inter";
    font-weight: 400;
    font-size: 16px;
    box-shadow: none;

}

input {
    border: 1px solid #DCDAD9;
    padding: 10px 15px;
    border-radius: 3px;
    font-family: "Inter", sans-serif;
    font-weight: 400;
    font-size: 16px;
}

.noColorButton {
    background: none;
    color: #B9B5B3;
    font-family: "Inter";
    font-weight: 400;
    font-size: 12px;
    line-height: 24px;
    border: 1px solid #746B68;
    border-radius: 4px;
    padding: 5px 10px;
    /*width: 125px;*/
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: left;
    cursor: pointer;
}

.table {
    width: 100%;
    border-collapse: collapse;
    text-align: center;
    vertical-align: middle;
    /*background-color: #EFEEED;*/
}

.tableHeader {
    background-color: white;
}

.rowStyles {
    background-color: #EFEEED;
}

.headerCell {
    padding: 0.75rem 1rem;
    text-align: center;
    color: #746B68;
    font-weight: 600;
    font-family: Poppins;
    font-size: 12px;

}

.cell {
    padding: 0.75rem 1rem;
    color: #170903;
    text-align: center;
    border-bottom: 3px solid #EFEEED;
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;


}

td,
th {
    border-left: none !important;
    border-right: none !important;
}

.centerContent {
    display: flex;
    justify-content: center;
}

.centerText {
    text-align: center;
    vertical-align: middle;
}

.tableRow {
    border-top: 1px solid #e5e7eb;
    background-color: #ffffff;
    font-size: 14px;
}

.statusIconSuccess {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 9999px;
    background-color: #d1fae5;
    color: #10b981;
}

.statusIconWarning {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 9999px;
    background-color: #fef3c7;
    color: #f59e0b;
}

.statusIconError {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 9999px;
    background-color: #fee2e2;
    color: #ef4444;
}

.activePage {
    background-color: #170903;
    font-family: Inter;
    font-weight: 400;
    font-size: 12px;
    line-height: 24px;
    color: #F8F8F7;
    width: 34px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

}

.pageButton {
    background-color: #FFFFFF;
    border: 1px solid #DCDAD9;
    font-family: Inter;
    font-weight: 400;
    font-size: 12px;
    line-height: 24px;
    color: #514742;
    width: 34px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.customDropdownContainer {
    margin-top: 5px;
    width: 100%;
    height: 50%;
}

.customDropdownSelect {
    background-color: #ffffff;
    border: 1px solid #DCDAD9;
    border-radius: 3px;
    width: 100%;
    height: 38px;
    margin-top: 12px;

}


.customDropdownSelect .MuiSelect-select {
    padding: 12px 16px;
    height: 12px;
    display: flex;
    align-items: center;
    color: #1a1a1a;
}

.customDropdownSelect .MuiSelect-select[value=""] {
    color: #64748b;
}

.customDropdownSelect .MuiSelect-icon {
    right: 12px;
    color: #64748b;
}

.customDropdownSelect .MuiOutlinedInput-root {
    border-color: #ccc;
    /* Default border */
}

.customDropdownSelect .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: grey !important;
    /* Change to your desired focus color */
}

.MuiMenuItem-root {
    padding: 12px 16px;
    color: #1a1a1a;
    min-height: 24px;
}

.MuiMenuItem-root:hover {
    background-color: #f1f5f9;
}

/* Selected item style */
.MuiMenuItem-root.Mui-selected,
.MuiMenuItem-root.Mui-selected:hover {
    background-color: #f1f5f9;
}

.formCheckInput:checked {
    appearance: none;
    /*background-color: #746B68;*/
    /*border-color: #746B68;*/
    position: relative;

    input {
        padding: 0 !important;
    }
}

.formCheckInput:checked::after {
    appearance: none;
    padding: 0;
    content: "";
    width: 8px;
    height: 8px;
    background-color: #5a524f;
    border-radius: 50%;
    /*display: block;*/
    position: absolute;
    /*top: 50%;*/
    /*left: 50%;*/
    transform: translate(-50%, -50%);

}

.closeIcon {
    height: 20px;
    width: 20px;
}

.passwordContainer {
    position: relative;
    width: 100%;
    padding-top: 20px;
}

.passwordInput {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 4px;
    outline: none;
    font-family: "Inter", sans-serif;
    font-weight: 400;
    font-size: 16px;
}

.passwordToggle {
    position: absolute;
    right: 0px;
    top: 60%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    font-size: 18px;
}

.selectedColumn {
    background-color: #EFEEED;
}

.plainButton {
    width: 100%;
    /*height: 24px;*/
    padding: 0px 15px;
    border-radius: 3px;
    border-width: 1px;
    border: 1px solid #DCDAD9;
    background-color: #ffffff;
    font-family: Inter;
    font-weight: 400;
    font-size: 12px;
    line-height: 24px;
    text-align: center;
    color: #514742;
    cursor: pointer;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loaderContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.loader {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

.imageLogin {
    width: 100%;
    height: 50%;
    margin-left: 30px;
    margin-top: 20px;
}

.imageBottom {
    height: 30px;
    width: 30px;
}

.bottomContainer {
    display: flex;
    justify-content: center;
    align-items: center;

    gap: 35px;

}

.imageContainer {
    background-color: #f5f5f5;
    border-radius: 50%;
    padding: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.signupHr {
    margin-bottom: 30px;
    border: 1px solid #DCDAD9;
    margin-top: 30px;
}

.clientLogo {
    width: 50px;
    height: 60px;
    background-color: #EFEEED;
    /*border-radius: 4px;*/
}

.expandedRow {
    background-color: #f9f9f9;
}

.expandedRow td {
    padding: 0;
}

.expandedContent {
    margin-left: 60px;
}

@keyframes myAnim {
    0% {
        transform: scale(0.5);
    }

    100% {
        transform: scale(1);
    }
}

body {
    margin: 0;
    overflow: hidden;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(24px) scale(0.96);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.trustedLogos {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    opacity: 0;
    animation: fadeInUp 0.7s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}