import React, { useState, useEffect, useMemo } from "react"
import styles from "./activity-and-analytics.module.css"
import globalStyles from "../globalStyles.module.css"
import { GlobeIcon, SearchIcon } from "@heroicons/react/solid"
import { ChevronDownIcon } from "@heroicons/react/solid"
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import { 
  FaAngleDoubleLeft, 
  FaChevronLeft, 
  FaChevronRight, 
  FaAngleDoubleRight 
} from "react-icons/fa"
import Sidebar from "../Sidebar/Sidebar"
import {getActivityLog, getTemplateLog} from "../apiService"
import LoaderSpinner from "../loaderspinner";
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import {HiDownload} from "react-icons/hi";

pdfMake.vfs = pdfFonts.vfs;


const ActivityAndAnalytics = () => {

  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    return localStorage.getItem("isSidebarCollapsed") === "true"
  })
  const [email, setEmail] = useState(localStorage.getItem('email'));

  const [usageView, setUsageView] = useState("week")
  const [migrationView, setMigrationView] = useState("week")
  const [activeSection, setActiveSection] = useState("15.03.2025") 
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [weeklyData, setWeeklyData] = useState([]);
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(25)
  const [totalPages, setTotalPages] = useState(10)

  const [activityLogs, setActivityLogs] = useState([])
  const [downloadData, setDownloadData] = useState([]);
  const [data, setData] = useState({
    week: { templates: 0, migrations: 0 }
  });

  useEffect(() => {
    const fetch = async () => {
      setIsLoading(true);
      try {
        const res = await getTemplateLog(email);
        console.log(res);
        setData( {week: { templates: res.templates_created , migrations:  res.migrations_completed }}
        );
      } catch (e) {
        console.log(e);
      } finally {
        setIsLoading(false);
      }
    };
    fetch();
  }, []);

  useEffect(() => {
    const fetchActivityLogs = async () => {
      try {
        setIsLoading(true)
        const userEmail = email;

        const response = await getActivityLog(userEmail)

        
        if (response && response.length > 0) {
          setDownloadData(response);
          const chartData = processActivityLogs(response)
          setWeeklyData(chartData)
          const formattedLogs = processActivityChartLogs(response)
          setActivityLogs(formattedLogs)
          setTotalPages(Math.ceil(response.length / rowsPerPage))
          

        } else {

          setActivityLogs([])
        }
        
        setIsLoading(false)
      } catch (err) {
        setError("Failed to load activity logs. Please try again later.")
        setIsLoading(false)
        setActivityLogs([]) 
      }
    }

    fetchActivityLogs()
  }, [rowsPerPage])

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const downloadActivityLogsPdf = () => {
    if (!downloadData || downloadData.length === 0) return

    const tableBody = [

      ['User', 'Activity', 'Time'],

      ...downloadData.map(log => [
        log.email || '-',
        log.activity || '-',
        formatTimestamp(log.timestamp) || '-'
      ])
    ]

    const docDefinition = {
      content: [
        { text: 'Activity Logs', style: 'header' },
        {
          table: {
            headerRows: 1,
            widths: ['*', '*', '*'],
            body: tableBody
          }
        }
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          marginBottom: 10
        }
      }
    }

    pdfMake.createPdf(docDefinition).download('activity-logs.pdf')
  }

  const  processActivityLogs = (logs) => {
    // Create an array of the last 7 days
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      last7Days.push({
        date,
        name: date.toLocaleDateString("en-US", { weekday: "short" }),
        usage: 0
      });
    }

    // Count activities for each day
    logs.forEach(log => {
      const logDate = new Date(log.timestamp);
      // Only count logs from the last 7 days
      const dayIndex = last7Days.findIndex(day => 
        day.date.getDate() === logDate.getDate() &&
        day.date.getMonth() === logDate.getMonth() &&
        day.date.getFullYear() === logDate.getFullYear()
      );
      if (dayIndex !== -1) {
        last7Days[dayIndex].usage += 1;
      }
    });

    // Format data for chart
    return last7Days.map(day => ({
      name: day.name,
      usage: day.usage
    }));
  }

 
  const processActivityChartLogs = (logs) => {
    if (!logs || logs.length === 0) return []

    
    const groupedByDate = logs.reduce((acc, log) => {
      
      const { email, activity, timestamp, id } = log
      
      
      const date = new Date(timestamp)
      const formattedDate = formatDate(date)
      
      
      if (!acc[formattedDate]) {
        acc[formattedDate] = []
      }
      
      acc[formattedDate].push({
        user: email, 
        activity: activity, 
        time: formatTimeAgo(date), 
        timestamp: date, 
        id: id 
      })
      
      return acc
    }, {})
    
    
    return Object.keys(groupedByDate)
      .sort((a, b) => new Date(b.split('.').reverse().join('-')) - new Date(a.split('.').reverse().join('-'))) 
      .map(date => ({
        date,
        activities: groupedByDate[date].sort((a, b) => b.timestamp - a.timestamp) 
      }))
  }

  
  const formatDate = (date) => {
    const day = String(date.getDate()).padStart(2, '0')
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const year = date.getFullYear()
    return `${day}.${month}.${year}`
  }

 
  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const diff = Math.floor((now - timestamp) / 1000) 
    
    if (diff < 60) return `${diff} seconds ago`
    if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`
    if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`
    
    
    const hours = timestamp.getHours().toString().padStart(2, '0')
    const minutes = timestamp.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }

  
  const filteredActivities = useMemo(() => {
    if (!searchQuery) return activityLogs
    
    return activityLogs.map(group => ({
      date: group.date,
      activities: group.activities.filter(activity => 
        activity.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.activity.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.time.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })).filter(group => group.activities.length > 0)
  }, [activityLogs, searchQuery])

  
  const toggleSection = (section) => {
    setActiveSection(activeSection === section ? "" : section)
  }

  
  const handlePageChange = (page) => {
    setCurrentPage(page)
  }

  const handleRowsPerPageChange = (rows) => {
    setRowsPerPage(rows)
    setCurrentPage(0) 
  }

 
  const getVisiblePages = () => {
    const delta = 2; 
    let pages = [];
  
  
    pages.push(0);
  
  
    let start = Math.max(1, currentPage - delta);
    let end = Math.min(totalPages - 2, currentPage + delta);
  
    
    if (start > 1) {
      pages.push("ellipsis-start");
    }
  
   
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
  
    
    if (end < totalPages - 2) {
      pages.push("ellipsis-end");
    }
  
    
    if (totalPages > 1) {
      pages.push(totalPages - 1);
    }
  
    return pages;
  };

 


  
  const quarterlyData = [
    { name: "Jan", usage: 5200 },
    { name: "Feb", usage: 6100 },
    { name: "Mar", usage: 7300 },
  ]

  
  const pieData = [
    { name: "Templates created", value: data.week.templates, color: "#DCDAD9" },
    { name: "Migrations completed", value: data.week.migrations, color:   "#EF8963" },
  ]

  return (
    <div className={styles.container}>
      <Sidebar isCollapsed={isSidebarCollapsed} setIsCollapsed={setIsSidebarCollapsed} />

      <div className={`${styles.mainSection} ${isSidebarCollapsed ? styles.expanded : ""}`}>
        <div className={styles.headerContainer}>
          <div className={styles.titleWithBackButton}>
            <h1 className={styles.pageTitle}>Activity and Analytics</h1>
          </div>

          <div className={styles.headerControls}>
            {/* <div className={globalStyles.searchWrapper}>
              <SearchIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Search..."
                className={globalStyles.searchInput}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={(e) => {
                  e.target.style.width = "200px"
                  e.target.placeholder = "Typing..."
                }}
                onBlur={(e) => {
                  e.target.style.width = "80px"
                  e.target.placeholder = "Search..."
                }}
              />
            </div> */}

            <div className={globalStyles.searchWrapper} style={{ marginRight: 0 }}>
              <GlobeIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Eng"
                className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                readOnly
              />
            </div>
          </div>
        </div>

        <div className={styles.dashboardGrid}>
         
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h2 className={styles.cardTitle}>USAGE TIMELINE</h2>
              <div className={styles.viewButtons}>
                {/*<button*/}
                {/*  onClick={() => setUsageView("quarter")}*/}
                {/*  className={`${styles.viewButton} ${usageView === "quarter" ? styles.active : ""}`}*/}
                {/*>*/}
                {/*  Last Quarter*/}
                {/*</button>*/}
                <button                  onClick={() => setUsageView("week")}
                  className={`${styles.viewButton} ${usageView === "week" ? styles.active : ""}`}
                >
                  7-Day Activity
                </button>
              </div>
            </div>
            <div className={styles.chartContainer}>
              <ResponsiveContainer width="100%" height={250}>
                <AreaChart
                  data={usageView === "week" ? weeklyData : quarterlyData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <defs>
                    <linearGradient id="colorUsage" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#DCDAD980" />
                      <stop offset="95%" stopColor="#DCDAD980" />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="name"
                    axisLine={false}
                    tickLine={false}
                    dy={10}
                    tick={{ fontSize: "14px", fontFamily: "Inter", fontWeight: "600", fill: "#746B68" }}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    dx={-10}
                    tick={{ fontSize: "14px", fontFamily: "Poppins", fontWeight: "400", fill: "#B9B5B3" }}
                    ticks={[0, 15, 30, 45, 60]}
                    domain={[0, 60]}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "white",
                      border: "1px solid #ddd",
                      borderRadius: "4px",
                      fontFamily: "inter",
                    }}
                  />
                  <Area
                    type="monotone"
                    dataKey="usage"
                    stroke="#EF8963"
                    strokeWidth={4}
                    fillOpacity={1}
                    fill="url(#colorUsage)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h2 className={styles.cardTitle}>MIGRATION LOG</h2>
              <div className={styles.viewButtons}>
                {/*<button*/}
                {/*  onClick={() => setMigrationView("quarter")}*/}
                {/*  className={`${styles.viewButton} ${migrationView === "quarter" ? styles.active : ""}`}*/}
                {/*>*/}
                {/*  Last Quarter*/}
                {/*</button>*/}
                <button                  onClick={() => setMigrationView("week")}
                  className={`${styles.viewButton} ${migrationView === "week" ? styles.active : ""}`}
                >
                  Trailing 7 Days
                </button>
              </div>
            </div>
            <div className={styles.migrationStats}>
              <div className={styles.statNumbers}>
                <div className={styles.statItem}>
                  <div className={styles.statValue}>
                    {String(data.week.templates).padStart(2, '0')}</div>
                  <div style={{display: "flex", alignItems: "center"}}>
                    <span className={`${styles.dot} ${styles.templateDot}`}></span>
                    <div className={styles.statLabel}>
                      Templates created
                    </div>
                  </div>

                </div>
                <div className={styles.statItem}>
                  <div className={styles.statValue}>{String(data.week.migrations).padStart(2, '0')}</div>
                  <div style={{display: "flex", alignItems: "center"}}>
                    <span className={`${styles.dot} ${styles.migrationDot}`}></span>
                    <div className={styles.statLabel}>
                      Migrations completed
                    </div>
                  </div>

                </div>
              </div>
              <div className={styles.pieChartContainer}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={3}
                      dataKey="value"
                      animationBegin={0}
                      animationDuration={800}
                      isAnimationActive={true}
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>


        <div className={styles.activityHistorySection}>
          <div className={styles.sectionHeader}>
            <h2>ACTIVITY HISTORY</h2>
            <button className={styles.downloadReportButton}  style={{ display: 'flex', alignItems: 'center', gap: '8px' }} onClick={downloadActivityLogsPdf}>
              <HiDownload className={globalStyles.closeIcon}/>
              Download activity report</button>
          </div>

          <div className={styles.activityTableWrapper}>
            {isLoading ? (
              <LoaderSpinner fullpage={false} height={25}/>
            ) : activityLogs.length === 0 ? (
              <div className={styles.emptyState}>No activity logs available.</div>
            ) : (
              <table className={styles.activityTable}>
                <tbody>
                  {filteredActivities.map((group, groupIndex) => (
                    <React.Fragment key={`date-group-${groupIndex}`}>
                      <tr>
                        <td colSpan="3" className={styles.dateContainer} onClick={() => toggleSection(group.date)}>
                          <div className={styles.dateHeader}>
                            <span className={styles.dateText}>{group.date}</span>
                            <span className={`${styles.chevron} ${activeSection === group.date ? styles.chevronUp : ""}`}>
                              <ChevronDownIcon className={styles.chevronIcon} />
                            </span>
                          </div>
                        </td>
                      </tr>
                      {activeSection === group.date && (
                        <>
                          <tr className={styles.headerRow}>
                            <th>User name</th>
                            <th>Activity log</th>
                            <th>Time stamp</th>
                          </tr>
                          {group.activities.map((activity, index) => (
                            <tr key={`activity-${activity.id || index}`}>
                              <td>
                                <div className={styles.userCell}>
                                  <img src="/assets/profile-orange.png" alt="User Avatar" className={styles.userAvatar}/>
                                  <span>{activity.user}</span>
                                </div>
                              </td>
                              <td>{activity.activity}</td>
                              <td>{activity.time}</td>
                            </tr>
                          ))}
                        </>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            )}
            {error && (
              <div className={styles.errorNotice}>
                <p>{error}</p>
              </div>
            )}
          </div>
        </div>
          
       
        {!isLoading && activityLogs.length > 0 && (
          <div className={styles.paginationContainer}>
            <div className={styles.pageInfo}>
              <span className={styles.paginationText}>Page no</span>
              <div className={styles.pageButtons}>
                <button
                  className={styles.arrowButton}
                  onClick={() => handlePageChange(0)}
                  disabled={currentPage === 0}
                >
                  <FaAngleDoubleLeft />
                </button>
                <button
                  className={styles.arrowButton}
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 0}
                >
                  <FaChevronLeft />
                </button>
                
                {getVisiblePages().map((page, index) => {
                  if (page === "ellipsis-start" || page === "ellipsis-end") {
                    return (
                      <span key={index} className={styles.pageEllipsis}>
                        ...
                      </span>
                    )
                  }
                  return (
                    <button
                      key={page}
                      className={`${styles.pageButton} ${currentPage === page ? styles.pageActive : ""}`}
                      onClick={() => handlePageChange(page)}
                    >
                      {(page + 1).toString().padStart(2, "0")}
                    </button>
                  )
                })}
                
                <button
                  className={styles.arrowButton}
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages - 1}
                >
                  <FaChevronRight />
                </button>
                <button
                  className={styles.arrowButton}
                  onClick={() => handlePageChange(totalPages - 1)}
                  disabled={currentPage >= totalPages - 1}
                >
                  <FaAngleDoubleRight />
                </button>
              </div>
            </div>
            <div className={styles.rowsPerPageContainer}>
              <span className={styles.paginationText}>Show</span>
              <div className={styles.rowsButtons}>
                <button
                  className={`${styles.rowsButton} ${rowsPerPage === 25 ? styles.pageActive : ""}`}
                  onClick={() => handleRowsPerPageChange(25)}
                >
                  25
                </button>
                <button
                  className={`${styles.rowsButton} ${rowsPerPage === 50 ? styles.pageActive : ""}`}
                  onClick={() => handleRowsPerPageChange(50)}
                >
                  50
                </button>
                <button
                  className={`${styles.rowsButton} ${rowsPerPage === 75 ? styles.pageActive : ""}`}
                  onClick={() => handleRowsPerPageChange(75)}
                >
                  75
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ActivityAndAnalytics