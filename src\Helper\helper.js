import config from "../Config/config.json";
import { showArticle } from "@intercom/messenger-js-sdk";
import axios from 'axios';

export const displayArticle = (title) => {
    console.log("Title:", title);
    const article = config.INTERCOM_ARTICLES_ENUM.find(item => item.title.toLocaleLowerCase() === title.toLocaleLowerCase());
    if (article) {
        console.log("Article:", article);
        showArticle(parseInt(article.id));
    }
};

export const parseJsonFields = (data) => {
    // Handle an array of objects
    if (Array.isArray(data)) {
        return data.map(item => {
            if (item && typeof item === 'object') {
                const parsed = { ...item };

                for (const key in parsed) {
                    if (typeof parsed[key] === 'string') {
                        const str = parsed[key].trim();
                        if (
                            (str.startsWith('{') && str.endsWith('}')) ||
                            (str.startsWith('[') && str.endsWith(']'))
                        ) {
                            try {
                                parsed[key] = JSON.parse(str);
                            } catch (err) {
                                console.warn(`Failed to parse JSON for key "${key}":`, err);
                            }
                        }
                    }
                }

                return parsed;
            }

            return item;
        });
    }

    // Handle a single object
    if (data && typeof data === 'object') {
        const parsed = { ...data };

        for (const key in parsed) {
            if (typeof parsed[key] === 'string') {
                const str = parsed[key].trim();
                if (
                    (str.startsWith('{') && str.endsWith('}')) ||
                    (str.startsWith('[') && str.endsWith(']'))
                ) {
                    try {
                        parsed[key] = JSON.parse(str);
                    } catch (err) {
                        console.warn(`Failed to parse JSON for key "${key}":`, err);
                    }
                }
            }
        }

        return parsed;
    }

    return data;
};

/**
 * Generic executor that makes an API call based on the schema.
 * @param {Object} schema - Schema describing the API call.
 * @returns {Promise<Object>} - The response from the API.
 */
export const executeApi = async (schema) => {
    const apiUrlTemplate = schema.apiUrl;
    let apiUrl = apiUrlTemplate;

    // Handle headers
    const headers = {};
    if (Array.isArray(schema.headers)) {
        schema.headers.forEach(header => {
            if (header.key && header.value) {
                headers[header.key] = header.value;
            }
        });
    }

    // Handle path params
    if (Array.isArray(schema.pathParams)) {
        schema.pathParams.forEach(param => {
            if (param.key && param.value) {
                apiUrl = apiUrl.replace(`:${param.key}`, encodeURIComponent(param.value));
            }
        });
    }

    // Handle query params
    const queryParams = {};
    if (Array.isArray(schema.queryParams)) {
        schema.queryParams.forEach(param => {
            if (param.key && param.value) {
                queryParams[param.key] = param.value;
            }
        });
    }

    // Handle request body
    const method = schema.executor_method_type?.toUpperCase() || 'GET';
    const hasBody = ['POST', 'PUT', 'PATCH'].includes(method);
    const data = hasBody && schema.requestBody?.value ? schema.requestBody.value : undefined;

    try {
        const response = await axios({
            method,
            url: apiUrl,
            headers,
            params: queryParams,
            data
        });

        return response.data;
    } catch (error) {
        return {
            error: true,
            message: error.message,
            ...(error.response?.data && { response: error.response.data })
        };
    }
};
