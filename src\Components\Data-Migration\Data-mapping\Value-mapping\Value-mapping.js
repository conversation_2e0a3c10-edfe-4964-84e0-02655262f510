"use client"

import React, { useEffect, useState, useContext } from "react"
import styles from "./Value-mapping.module.css"
import globalStyles from "../../../globalStyles.module.css"
import { HiChevronDown, HiXMark, HiLink } from "react-icons/hi2"
import { SearchIcon } from "@heroicons/react/solid"
import { HiChevronUp, HiPlus } from "react-icons/hi"
import Dropdown from "../../Dropdown/Dropdown"
import RestoreDetailsModal from "../Restoredetails/Restoredetails"
import { Dialog, DialogContent, DialogActions, Button } from "@mui/material"
import { toast } from "react-toastify"
import { MigrationContext } from "../../Data-Migration"
import { ca } from "date-fns/locale"
import { sub } from "date-fns"

export default function ValueMapping({
  close,
  attribute,
  selectedObjectData,
  selectedEntityData,
  sourceMapRes,
  source,
  uniqueSourceValues,
  target,
  targetData,
  targetExeRes,
  onSave,
}) {
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const dataMappingData = migrationState.dataMappingData
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [openGuide, setOpenGuide] = useState(false)
  const [openRestore, setOpenRestore] = useState(false)
  const [showSmartAutoMapConfirm, setShowSmartAutoMapConfirm] = useState(false)
  const [showClearAllConfirm, setShowClearAllConfirm] = useState(false)

  // Use cached mappings from attribute or initialize new mappings
  const [mainMappings, setMainMappings] = useState(() => {
    if (attribute?.mappings && Array.isArray(attribute.mappings) && attribute.mappings.length > 0) {
      return attribute.mappings.map((mapping) => ({
        ...mapping,
        sourcevalue: Array.isArray(mapping.sourcevalue)
          ? mapping.sourcevalue.map((value) => (value === null ? "Null" : value === "" ? "Blank" : value))
          : mapping.sourcevalue,
      }))
    }
    return [
      {
        sourcevalue: [],
        targetvalue: null,
        subCategories: [],
      },
    ]
  })

  const [expandedMappings, setExpandedMappings] = useState(() => {
    const length = attribute?.mappings?.length || 1
    return {
      main: Array(length).fill(false),
      subCategory: Array(length)
        .fill([])
        .map(() => []),
    }
  })

  // Only update mappings if attribute content actually changes
  useEffect(() => {
    if (!attribute?.mappings) {
      // If no mappings exist (e.g., new file uploaded), reset to initial state
      setMainMappings([
        {
          sourcevalue: [],
          targetvalue: null,
          subCategories: [],
        },
      ])
      setExpandedMappings({
        main: [false],
        subCategory: [[]],
      })
      return
    }

    const serializedNewMappings = JSON.stringify(attribute.mappings)
    const serializedCurrentMappings = JSON.stringify(
      mainMappings.map((m) => ({
        ...m,
        sourcevalue: Array.isArray(m.sourcevalue)
          ? m.sourcevalue.map((v) => (v === "null" || v === "Null" ? null : v === '<"">' || v === "Blank" ? "" : v))
          : m.sourcevalue,
      })),
    )

    if (serializedNewMappings !== serializedCurrentMappings) {
      const updatedMappings = attribute.mappings.map((mapping) => ({
        ...mapping,
        sourcevalue: Array.isArray(mapping.sourcevalue)
          ? mapping.sourcevalue.map((value) => (value === null ? "Null" : value === "" ? "Blank" : value))
          : mapping.sourcevalue,
      }))
      setMainMappings(updatedMappings)

      setExpandedMappings({
        main: Array(updatedMappings.length).fill(false),
        subCategory: Array(updatedMappings.length)
          .fill([])
          .map(() => []),
      })
    }
  }, [attribute?.mappings])

  // Immediate check for data inconsistency on component mount/update
  useEffect(() => {
    if (source?.type === "csv" && uniqueSourceValues && attribute?.sourcefield) {
      const totalSourceValues = getTotalSourceCount(attribute, target.name);
      const totalMappedValues = getTotalMappedCount(mainMappings);

      console.log('Value Mapping Debug:', {
        totalSourceValues,
        totalMappedValues,
        sourcefield: attribute?.sourcefield,
        mappingsLength: mainMappings?.length
      });

      // If mapped count is significantly higher than source count, reset mappings
      if (totalMappedValues > totalSourceValues && totalSourceValues > 0) {
        console.log('Detected stale mapping data, resetting...');
        setMainMappings([
          {
            sourcevalue: [],
            targetvalue: null,
            subCategories: [],
          },
        ])
        setExpandedMappings({
          main: [false],
          subCategory: [[]],
        })
      }
    }
  }, [source, uniqueSourceValues, attribute?.sourcefield, target.name])

  // Reset mappings when source data changes (new file uploaded)
  useEffect(() => {
    // For CSV files: Check if the source field values have changed, indicating a new file was uploaded
    if (source?.type === "csv" && uniqueSourceValues && attribute?.sourcefield) {
      const currentSourceValues = getValuesByColumnName(uniqueSourceValues, attribute.sourcefield, "column", "values")

      // Calculate the current total source count
      const currentTotalSourceCount = currentSourceValues.filter(
        (item) =>
          item !== '<"">' &&
          item !== '<\"\">' &&
          item !== "null" &&
          item !== null &&
          item !== "" &&
          item !== undefined
      ).length

      // If we have attribute mappings, check if the mapped count is inconsistent with source count
      if (attribute?.mappings && Array.isArray(attribute.mappings)) {
        const attributeMappedCount = getTotalMappedCount(attribute.mappings)

        // If the mapped count from attribute is much larger than current source count,
        // it means we have stale mapping data from a previous file
        if (attributeMappedCount > currentTotalSourceCount && currentTotalSourceCount > 0) {
          // Force reset by clearing the mappings in the local state
          setMainMappings([
            {
              sourcevalue: [],
              targetvalue: null,
              subCategories: [],
            },
          ])
          setExpandedMappings({
            main: [false],
            subCategory: [[]],
          })
          return
        }
      }

      // Additional check: If we have previous mappings but the source values don't match any mapped values,
      // it indicates a new file has been uploaded
      if (mainMappings.length > 0 && mainMappings.some(m => m.sourcevalue?.length > 0)) {
        const mappedSourceValues = new Set()
        mainMappings.forEach(mapping => {
          if (Array.isArray(mapping.sourcevalue)) {
            mapping.sourcevalue.forEach(val => {
              const actualValue = typeof val === 'object' ? (val.value || val.label) : val
              if (actualValue && actualValue !== '<"">' && actualValue !== 'null') {
                mappedSourceValues.add(actualValue)
              }
            })
          }
        })

        // Check if any of the mapped values exist in the current source values
        const hasMatchingValues = Array.from(mappedSourceValues).some(mappedVal =>
          currentSourceValues.includes(mappedVal)
        )

        // If no matching values found, reset mappings (new file uploaded)
        if (!hasMatchingValues && mappedSourceValues.size > 0) {
          setMainMappings([
            {
              sourcevalue: [],
              targetvalue: null,
              subCategories: [],
            },
          ])
          setExpandedMappings({
            main: [false],
            subCategory: [[]],
          })
        }
      }
    }
  }, [uniqueSourceValues, source, attribute?.sourcefield, attribute?.mappings])

  useEffect(() => {
    // Only run smart auto-map if there are unmapped source values (excluding <""> and null)
    const totalSourceValues = getTotalSourceCount(attribute, target.name);
    const totalMappedValues = getTotalMappedCount(mainMappings);
    const unmappedCount = Math.max(0, totalSourceValues - totalMappedValues);

    // Check for data inconsistency: if mapped count is much higher than source count,
    // it indicates stale mapping data from a previous file upload
    if (source?.type === "csv" && totalMappedValues > totalSourceValues && totalSourceValues > 0) {
      // Force reset the mappings to clear stale data
      setMainMappings([
        {
          sourcevalue: [],
          targetvalue: null,
          subCategories: [],
        },
      ])
      setExpandedMappings({
        main: [false],
        subCategory: [[]],
      })
      return
    }

    if (unmappedCount > 0) {
      executeSmartAutoMap()
    }
  }, [])

  const transformSourceValueForSave = (value) => {
    // You can set a breakpoint here in Chrome DevTools
    if (value && typeof value === "object") {
      // Normalize NULL-like selections
      if (
        value.value === null ||
        value.value === "null" ||
        value.label === "null" ||
        value.label === "Null"
      )
        return {
          label: "Null",
          value: null,
        }

      // Normalize BLANK-like selections
      if (
        value.value === "" ||
        value.value === '<"">' ||
        value.label === '<"">' ||
        value.label === '<\"\">' ||
        value.label === "Blank"
      )
        return {
          label: "Blank",
          value: "",
        }

      return value
    }
    // If value is a string
    if (value === "null" || value === "Null" || value === "Blank") return null
    if (value === '<"">' || value === '<\"\">' || value === "") return ""
    return value
  }

  // Simple toggle - only creates one empty sub-category
  const toggleMainMapping = (index) => {
    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.main[index] = !newExpandedMappings.main[index]

    if (newExpandedMappings.main[index] && mainMappings[index].subCategories.length === 0) {
      const updatedMappings = [...mainMappings]

      // Create only one empty sub-category - user will provide mappings
      updatedMappings[index].subCategories = [
        {
          sourcevalue: [],
          targetvalue: null,
          items: [],
        },
      ]
      setMainMappings(updatedMappings)

      if (!newExpandedMappings.subCategory[index]) {
        newExpandedMappings.subCategory[index] = []
      }
      newExpandedMappings.subCategory[index] = [false]
    }

    setExpandedMappings(newExpandedMappings)
  }
  const handleSave = () => {
    // Check if there's any mapping with source value but no target value
    const hasIncompleteMappings = mainMappings.some(
      (mapping) =>
        mapping.sourcevalue &&
        Array.isArray(mapping.sourcevalue) &&
        mapping.sourcevalue.length > 0 &&
        !mapping.targetvalue,
    )

    if (hasIncompleteMappings && (target.name === "Groups" || target.name === "Agents")) {
      toast.error("Please select target values.", {
        position: "top-right",
        autoClose: 3000,
      })
      return
    }
    const updatedMappings = mainMappings.map((mapping) => {
      const processedMapping = {
        ...mapping,
        sourcevalue: Array.isArray(mapping.sourcevalue)
          ? mapping.sourcevalue.map(transformSourceValueForSave)
          : mapping.sourcevalue,
        // Extract ID for backend storage while preserving original object structure
        targetvalue: extractTargetValueForSave(mapping.targetvalue, attribute)
      }

      // For category mappings in Tickets, automatically sync subcategories
      if (target?.name === "Tickets" && attribute?.targetfield === "category" && mapping.targetvalue?.nested_options) {
        // Map subcategories if they exist in target value and the category has a source value
        if (Array.isArray(mapping.sourcevalue) && mapping.sourcevalue.length > 0) {
          processedMapping.subCategories = mapping.targetvalue.nested_options.map((subcategory) => {
            // Find existing subcategory mapping if any
            const existingSubcategory = mapping.subCategories?.find(
              (sub) => sub.targetvalue?.value === subcategory.value
            )

            return {
              sourcevalue: existingSubcategory?.sourcevalue || mapping.sourcevalue, // Copy source value from category
              targetvalue: subcategory,
              items: subcategory.nested_options ? subcategory.nested_options.map(item => {
                // Find existing item mapping if any
                const existingItem = existingSubcategory?.items?.find(
                  (i) => i.targetvalue?.value === item.value
                )

                return {
                  sourcevalue: existingItem?.sourcevalue || [],
                  targetvalue: item
                }
              }) : []
            }
          })
        }
      }      // Process existing subcategory mappings
      if (mapping.subCategories && mapping.subCategories.length > 0) {
        processedMapping.subCategories = mapping.subCategories.map((subCategory) => ({
          ...subCategory,
          sourcevalue: Array.isArray(subCategory.sourcevalue)
            ? subCategory.sourcevalue.map((value) => (value === "null" || value === "Null" ? null : value === '<"">' || value === "Blank" ? "" : value))
            : subCategory.sourcevalue,
          // Extract ID for backend storage
          targetvalue: extractTargetValueForSave(subCategory.targetvalue, attribute),
          items:
            subCategory.items && subCategory.items.length > 0
              ? subCategory.items.map((item) => ({
                ...item,
                sourcevalue: Array.isArray(item.sourcevalue)
                  ? item.sourcevalue.map((value) => (value === "null" || value === "Null" ? null : value === '<"">' || value === "Blank" ? "" : value))
                  : item.sourcevalue,
                // Extract ID for backend storage
                targetvalue: extractTargetValueForSave(item.targetvalue, attribute)
              }))
              : subCategory.items,
        }))
      }

      return processedMapping
    })

    try {
      onSave(updatedMappings, attribute, target.name)
      toast.success("Mapping saved successfully!", {
        position: "top-right",
        autoClose: 2000,
      })
      setTimeout(() => {
        close()
      }, 500)
    } catch (e) {
      toast.error("Failed to save mapping.", {
        position: "top-right",
        autoClose: 3000,
      })
    }
  }

  const toggleSubCategory = (mainIndex, subIndex) => {
    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.subCategory[mainIndex][subIndex] = !newExpandedMappings.subCategory[mainIndex][subIndex]

    if (
      newExpandedMappings.subCategory[mainIndex][subIndex] &&
      mainMappings[mainIndex].subCategories[subIndex].items.length === 0
    ) {
      const updatedMappings = [...mainMappings]
      updatedMappings[mainIndex].subCategories[subIndex].items = [
        {
          sourcevalue: [],
          targetvalue: null,
        },
      ]
      setMainMappings(updatedMappings)
    }
    setExpandedMappings(newExpandedMappings)
  }

  const areAllValuesMapped = () => {
    const totalSourceValues = getTotalSourceCount(attribute, target.name);
    const totalMappedValues = getTotalMappedCount(mainMappings);
    return totalMappedValues >= totalSourceValues;
  };

  const addNewMapping = () => {
    // Get the actual unmapped count, ensuring it never goes negative
    const totalSourceValues = getTotalSourceCount(attribute, target.name);
    const totalMappedValues = getTotalMappedCount(mainMappings);
    const unmappedCount = Math.max(0, totalSourceValues - totalMappedValues);

    // If all values are mapped (including excluding "" and null), show success message
    // if (unmappedCount === 0) {
    //   toast.success("All values have been mapped successfully!");
    //   return;
    // }

    const updatedMappings = [
      ...mainMappings,
      {
        sourcevalue: [],
        targetvalue: null,
        subCategories: [],
        matchMethod: null,
        confidence: 0,
      },
    ]
    setMainMappings(updatedMappings)

    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.main.push(false)
    newExpandedMappings.subCategory.push([])
    setExpandedMappings(newExpandedMappings)
  }

  const addNewSubCategory = (mainIndex) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories.push({
      sourcevalue: [],
      targetvalue: null,
      items: [],
    })
    setMainMappings(updatedMappings)

    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.subCategory[mainIndex].push(false)
    setExpandedMappings(newExpandedMappings)
  }

  const addNewItem = (mainIndex, subIndex) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].items.push({
      sourcevalue: [],
      targetvalue: null,
    })
    setMainMappings(updatedMappings)
  }

  const deleteMapping = (index) => {
    const updatedMappings = [...mainMappings]
    updatedMappings.splice(index, 1)
    setMainMappings(updatedMappings)

    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.main.splice(index, 1)
    newExpandedMappings.subCategory.splice(index, 1)
    setExpandedMappings(newExpandedMappings)
  }

  const deleteSubCategory = (mainIndex, subIndex) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories.splice(subIndex, 1)
    setMainMappings(updatedMappings)

    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.subCategory[mainIndex].splice(subIndex, 1)
    setExpandedMappings(newExpandedMappings)
  }

  const deleteItem = (mainIndex, subIndex, itemIndex) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].items.splice(itemIndex, 1)
    setMainMappings(updatedMappings)
  }
  const handleSubCategorySourceValueChange = (mainIndex, subIndex, newValue) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].sourcevalue = newValue
    setMainMappings(updatedMappings)
  }

  const handleSubCategoryTargetValueChange = (mainIndex, subIndex, newValue) => {
    const updatedMappings = [...mainMappings]

    // Store the full object for UI display purposes
    // The ID extraction will happen only during save
    updatedMappings[mainIndex].subCategories[subIndex].targetvalue = newValue

    // When sub-category target is selected, automatically create items if nested options exist
    if (newValue?.nested_options && newValue.nested_options.length > 0) {
      // Auto-create items based on nested options
      updatedMappings[mainIndex].subCategories[subIndex].items = newValue.nested_options.map((itemOption) => ({
        sourcevalue: [],
        targetvalue: itemOption, // Pre-fill target value
      }))
    } else {
      // Reset to single empty item if no nested options
      updatedMappings[mainIndex].subCategories[subIndex].items = [
        {
          sourcevalue: [],
          targetvalue: null,
        },
      ]
    }

    setMainMappings(updatedMappings)
  }
  const handleItemSourceValueChange = (mainIndex, subIndex, itemIndex, newValue) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].items[itemIndex].sourcevalue = newValue
    setMainMappings(updatedMappings)
  }

  const handleItemTargetValueChange = (mainIndex, subIndex, itemIndex, newValue) => {
    const updatedMappings = [...mainMappings]

    // Store the full object for UI display purposes
    // The ID extraction will happen only during save
    updatedMappings[mainIndex].subCategories[subIndex].items[itemIndex].targetvalue = newValue

    setMainMappings(updatedMappings)
  }

  const filterNulls = (arr) => {
    return arr.filter((item) => item !== null)
  }

  const handleOpen = () => {
    setOpenGuide(!openGuide)
  }

  // const getValuesByColumnName = (arr, columnName, searchFieldName, returnFieldName) => {
  //   // Gracefully handle cases where arr is not an array (API shape changes, object map, etc.)
  //   if (!arr || columnName == null) return []

  //   let sourceArray = arr

  //   // If arr is not an array, try to derive an iterable structure
  //   if (!Array.isArray(sourceArray)) {
  //     // Common patterns: { data: [...] } or an object keyed by column names
  //     if (Array.isArray(arr?.data)) {
  //       sourceArray = arr.data
  //     } else if (typeof arr === 'object') {
  //       // If it's an object keyed by column names directly
  //       if (Object.prototype.hasOwnProperty.call(arr, columnName)) {
  //         const directVal = arr[columnName]
  //         if (Array.isArray(directVal)) return directVal
  //         if (directVal == null) return []
  //         // Wrap primitive/object in array for consistency
  //         return typeof directVal === 'object' && !Array.isArray(directVal) ? [directVal] : [directVal]
  //       }
  //       // Convert object map to array of faux column objects
  //       sourceArray = Object.entries(arr).map(([key, value]) => ({
  //         [searchFieldName]: key,
  //         [returnFieldName]: value,
  //       }))
  //     } else {
  //       return []
  //     }
  //   }

  //   if (!Array.isArray(sourceArray)) return []

  //   const columnObject = sourceArray.find(
  //     (obj) =>
  //       obj && (
  //         obj[searchFieldName] === columnName ||
  //         (typeof obj[searchFieldName] === 'string' &&
  //           obj[searchFieldName].toLowerCase() === String(columnName).toLowerCase())
  //       ),
  //   )

  //   let result = columnObject ? columnObject[returnFieldName] : []

  //   if (result == null) return []
  //   if (Array.isArray(result)) return result
  //   if (typeof result === 'object') return [result]
  //   return [result]
  // }

  const getValuesByColumnName = (arr, columnName, searchFieldName, returnFieldName) => {
    if (!arr) return []

    // Get the array for the current tab directly from the arr object
    const currentTabData = arr[dataMappingData?.currentTab]
    if (!currentTabData || !Array.isArray(currentTabData)) return []

    // Find the column object in the current tab's data
    const columnObject = currentTabData.find((obj) => obj[searchFieldName] === columnName)
    return columnObject ? columnObject[returnFieldName] : []
  }

  const getSourceMappingObjectKey = (sourceMappingConfig) => {
    return sourceMappingConfig?.sourceMappingObjectKey?.trim() || "ticket_fields"
  }

  const isTargetValueMapped = (targetValue, currentIndex = -1) => {
    return mainMappings.some((mapping, index) => {
      if (index === currentIndex) {
        return false; // Exclude current mapping being edited
      }

      if (!mapping.targetvalue) {
        return false;
      }

      // Get the comparable value from the target value being checked
      const checkValue = typeof targetValue === 'object'
        ? (targetValue.value || targetValue.id || targetValue.name || targetValue.label || String(targetValue))
        : String(targetValue);

      // Get the comparable value from the mapping
      const mappedValue = typeof mapping.targetvalue === 'object'
        ? (mapping.targetvalue.value || mapping.targetvalue.id || mapping.targetvalue.name || mapping.targetvalue.label || String(mapping.targetvalue))
        : String(mapping.targetvalue);

      // Also check the display value to ensure consistency
      const checkDisplayValue = getTargetMappingDisplayValue(targetValue, target?.name);
      const mappedDisplayValue = getTargetMappingDisplayValue(mapping.targetvalue, target?.name);

      // Return true if either the raw values match or the display values match
      return (String(mappedValue) === String(checkValue)) ||
        (String(mappedDisplayValue) === String(checkDisplayValue));
    });
  };

  const getDropdownOptionsWithIndicators = (target, attribute, currentIndex = -1) => {
    const groups = getTargetFieldDropdownValues(target.name, attribute);

    const mappedOptions = groups.map((group, index) => {
      const isMapped = isTargetValueMapped(group, currentIndex);
      const displayValue = getTargetMappingDisplayValue(group, target.name);

      // Ensure displayValue is always a string
      const safeDisplayValue = String(displayValue || '');

      // Use a unique value that can reliably identify the original object
      // Prefer ID, then value, then label, then index as fallback
      const mappingConfig = selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : ""
      const sourceField = attribute.sourcefield

      let isExcluded = [];
      try {
        if (mappingConfig) {
          const parsedConfig = JSON.parse(mappingConfig);
          if (parsedConfig?.exclusion_list && Array.isArray(parsedConfig.exclusion_list)) {
            isExcluded = parsedConfig.exclusion_list.filter(
              (exclusion) => exclusion.field === sourceField
            );
          }
        }
      } catch (e) {
        // If parsing fails, treat as not excluded
        isExcluded = [];
      }

      const uniqueValue = group && typeof group === 'object'
        ? (isExcluded.length > 0
          ? group[isExcluded[0]?.targetMappingAttribute]
          : (group.id || group.value || group.label || `option_${index}`))
        : group || `option_${index}`;

      return {
        label: isMapped ? `${safeDisplayValue} [Mapped]` : safeDisplayValue,
        value: uniqueValue,
        originalValue: group,
        isMapped: isMapped,
        style: isMapped ? {
          color: '#7c3aed',
          fontWeight: '600',
          backgroundColor: '#f3f4f6',
          border: '1px solid #7c3aed',
          borderRadius: '4px',
          padding: '6px 10px',
          fontStyle: 'italic'
        } : {
          padding: '6px 10px'
        }
      };
    });

    // Add the "Null" option to the dropdown
    const nullOption = {
      label: "Null",
      value: null,
      originalValue: null,
      isMapped: false,
      style: {
        padding: '6px 10px'
      }
    };

    return [...mappedOptions, nullOption];
  };

  const getSubDropdownOptionsWithIndicators = (attribute, mainIndex, currentSubIndex = -1) => {
    let groups = [];

    if (attribute?.mappings?.[mainIndex]?.targetvalue?.nested_options) {
      groups = attribute.mappings[mainIndex].targetvalue.nested_options || [];
    } else if (mainMappings[mainIndex]?.targetvalue?.nested_options) {
      groups = mainMappings[mainIndex].targetvalue.nested_options || [];
    }

    const mappedOptions = groups.map((group) => {
      // Check if this sub-category value is already mapped
      const isMapped = mainMappings[mainIndex]?.subCategories?.some((subCat, subIndex) => {
        if (subIndex === currentSubIndex) return false; // Exclude current sub-category

        // Get comparable values
        const subTargetValue = typeof subCat.targetvalue === 'object'
          ? (subCat.targetvalue.value || subCat.targetvalue.id || subCat.targetvalue.name || subCat.targetvalue.label || String(subCat.targetvalue))
          : String(subCat.targetvalue);

        const groupValue = typeof group === 'object'
          ? (group.value || group.id || group.name || group.label || String(group))
          : String(group);

        return String(subTargetValue) === String(groupValue);
      });

      // Ensure group value is always a string
      const safeGroupValue = String(group?.value || group?.label || group?.name || group || '');

      return {
        label: isMapped ? `${safeGroupValue} [Mapped]` : safeGroupValue,
        value: group,
        isMapped: isMapped,
        style: isMapped ? {
          color: '#7c3aed',
          fontWeight: '600',
          backgroundColor: '#f3f4f6',
          border: '1px solid #7c3aed',
          borderRadius: '4px',
          padding: '6px 10px',
          fontStyle: 'italic'
        } : {
          padding: '6px 10px'
        }
      };
    });

    return mappedOptions;
  };

  const getItemDropdownOptionsWithIndicators = (attribute, mainIndex, subIndex, currentItemIndex = -1) => {
    let groups = [];

    if (attribute?.mappings?.[mainIndex]?.subCategories?.[subIndex]?.targetvalue?.nested_options) {
      groups = attribute.mappings[mainIndex].subCategories[subIndex].targetvalue.nested_options || [];
    } else if (mainMappings[mainIndex]?.subCategories?.[subIndex]?.targetvalue?.nested_options) {
      groups = mainMappings[mainIndex].subCategories[subIndex].targetvalue.nested_options || [];
    }

    const mappedOptions = groups.map((group) => {
      // Check if this item value is already mapped
      const isMapped = mainMappings[mainIndex]?.subCategories?.[subIndex]?.items?.some((item, itemIndex) => {
        if (itemIndex === currentItemIndex) return false; // Exclude current item

        // Get comparable values
        const itemTargetValue = typeof item.targetvalue === 'object'
          ? (item.targetvalue.value || item.targetvalue.id || item.targetvalue.name || item.targetvalue.label || String(item.targetvalue))
          : String(item.targetvalue);

        const groupValue = typeof group === 'object'
          ? (group.value || group.id || group.name || group.label || String(group))
          : String(group);

        return String(itemTargetValue) === String(groupValue);
      });

      // Ensure group value is always a string
      const safeGroupValue = String(group?.value || group?.label || group?.name || group || '');

      return {
        label: isMapped ? `${safeGroupValue} [Mapped]` : safeGroupValue,
        value: group,
        isMapped: isMapped,
        style: isMapped ? {
          color: '#7c3aed',
          fontWeight: '600',
          backgroundColor: '#f3f4f6',
          border: '1px solid #7c3aed',
          borderRadius: '4px',
          padding: '6px 10px',
          fontStyle: 'italic'
        } : {
          padding: '6px 10px'
        }
      };
    });

    return mappedOptions;
  };

  const getSourceMappingFieldName = (sourceMappingConfig) => {
    return sourceMappingConfig?.fieldName?.trim() || "title"
  }

  const getSourceChoiceMappingAttribute = (sourceMappingConfig) => {
    return sourceMappingConfig?.mappingAttribute?.trim() || "title"
  }

  const getTargetChoiceMappingAttribute = (sourceMappingConfig) => {
    return sourceMappingConfig?.targetMappingAttribute?.trim() || "value"
  }

  const getValuesByColumnName1 = (arr, columnName, searchFieldName, returnFieldName) => {
    if (arr) {
      if (columnName !== "sub_category" && columnName !== "item_category") {
        // const columnObject = arr.find((obj) => {
        //   if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
        //     if (obj[searchFieldName].toLowerCase() === columnName.toLowerCase()) {
        //       return true
        //     }
        //   }
        //   return false
        // })
        // const result = columnObject ? columnObject[returnFieldName] : []
        // // Convert object to array of objects if needed
        // if (result && typeof result === 'object' && !Array.isArray(result)) {
        //   let data1 = Object.entries(result).map(([key, value]) => ({
        //     id: key,
        //     value: Array.isArray(value) ? value[0] : value,
        //     label: Array.isArray(key) ? key[1] || key[0] : key
        //   }))
        //   return data1
        // }
        // return Array.isArray(result) ? result : []
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === columnName.toLowerCase()) {
              return true
            }
          }
          return false
        })

        // If no result found with primary searchFieldName, try fallback fields
        let finalColumnObject = columnObject;
        if (!finalColumnObject) {
          const fallbackFields = ['name', 'value', 'id'];

          for (const fallbackField of fallbackFields) {
            if (fallbackField !== searchFieldName) { // Skip if it's the same as primary field
              const fallbackResult = arr.find((obj) => {
                if (obj[fallbackField] && typeof obj[fallbackField] === "string") {
                  if (obj[fallbackField].toLowerCase() === columnName.toLowerCase()) {
                    return true
                  }
                }
                return false
              });

              if (fallbackResult) {
                finalColumnObject = fallbackResult;
                break; // Stop at first match
              }
            }
          }
        }

        const result = finalColumnObject ? finalColumnObject[returnFieldName] : []

        // Convert different choice formats to consistent array of objects
        if (result && typeof result === 'object' && !Array.isArray(result)) {
          // Handle object format: { "Low": 1, "2": ["Open", "Open"] }
          let data1 = Object.entries(result).map(([key, value]) => {
            // Handle both formats of choices
            if (Array.isArray(value)) {
              // Format 2: "2": ["Open", "Open"] - use key as id, first array element as label
              return {
                id: key,
                value: key, // Use key as the actual value/id
                label: value[0] || key // Use first array element as display label
              }
            } else {
              // Format 1: "Low": 1 - use value as id, key as label
              return {
                id: value,
                value: value, // Use the numeric/string value as id
                label: key // Use the key as display label
              }
            }
          })
          return data1
        } else if (Array.isArray(result)) {
          // Handle array format: ["Question", "Incident", "Problem"]
          let data1 = result.map((item, index) => {
            if (typeof item === 'string') {
              // Format 3: Simple string array - use string as both id and label
              return {
                id: item,
                value: item,
                label: item
              }
            } else if (typeof item === 'object' && item !== null) {
              // Handle array of objects (existing format)
              return {
                id: item.id || item.value || index,
                value: item.value || item.id || item,
                label: item.label || item.name || item.value || item
              }
            }
            // Fallback for other types
            return {
              id: index,
              value: item,
              label: String(item)
            }
          })
          return data1
        }

        return []
      } else if (columnName == "sub_category") {
        const tempColumnName = "category"
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === tempColumnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        const choices = columnObject ? columnObject[returnFieldName] : []
        const sub_categories = []
        if (choices && choices.length > 0) {
          for (const item of choices) {
            if (item.nested_options && item.nested_options.length > 0) {
              for (const options of item.nested_options) {
                sub_categories.push(options)
              }
            }
          }
        }
        return sub_categories
      } else if (columnName == "item_category") {
        const tempColumnName = "category"
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === tempColumnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        const choices = columnObject ? columnObject[returnFieldName] : []
        const item_category = []
        if (choices && choices.length > 0) {
          for (const item of choices) {
            if (item.nested_options && item.nested_options.length > 0) {
              for (const options of item.nested_options) {
                if (options.nested_options && options.nested_options.length > 0) {
                  for (const item1 of options.nested_options) {
                    item_category.push(item1)
                  }
                }
              }
            }
          }
        }
        return item_category
      }
    }
    return []
  }

  const getTargetFieldDropdownValues = (name, attribute) => {
    const searchFieldName = source.type === "api" || attribute.is_custom_field ? "name" : "label"

    if (name === selectedEntityData?.mainTarget[0]?.name && targetExeRes?.ticketFields) {
      return getValuesByColumnName1(targetExeRes.ticketFields, attribute.targetfield, searchFieldName, "choices")
    } else if (name && targetExeRes?.[name]) {
      return targetExeRes[name]
    }
    return []
  }

  const getDropdownOptions = (target, attribute) => {
    const groups = getTargetFieldDropdownValues(target.name, attribute)
    const data = groups.map((group) => ({
      label: getTargetMappingDisplayValue(group, target.name),
      value: group && typeof group === 'object' && group.id !== undefined ? group.id : group,
    }))

    return data
  }

  const getSubDropdownOptions = (attribute, index) => {
    let data = [];

    if (attribute?.mappings?.[index]?.targetvalue?.nested_options) {
      const groups = attribute.mappings[index].targetvalue.nested_options || []
      data = groups.map((group) => ({
        label: group?.value || "",
        value: group,
      }))
    } else if (mainMappings[index]?.targetvalue?.nested_options) {
      const groups = mainMappings[index].targetvalue.nested_options || []
      data = groups.map((group) => ({
        label: group?.value || "",
        value: group,
      }))
    }

    return data
  }

  const getItemDropdownOptions = (attribute, mainIndex, subIndex) => {
    let data = [];

    if (attribute?.mappings?.[mainIndex]?.subCategories?.[subIndex]?.targetvalue?.nested_options) {
      data = attribute.mappings[mainIndex].subCategories[subIndex].targetvalue.nested_options?.map((group) => ({
        label: group.value,
        value: group,
      })) || []
    } else if (mainMappings[mainIndex]?.subCategories?.[subIndex]?.targetvalue?.nested_options) {
      data = mainMappings[mainIndex].subCategories[subIndex].targetvalue.nested_options?.map((group) => ({
        label: group.value,
        value: group,
      })) || []
    }

    return data
  }

  // const getSourceMappingDisplayValue = (group) => {
  //   return group[0]?.label || group[0]?.value || null
  //   // if (group.value) {
  //   //   return group.label || group.value || group.name || group.id
  //   // } else {
  //   //   return group[0]?.group || group.name || group.id || group.label || group.value
  //   // }
  //   // if (!group) return null

  //   // const sourceMappingKey = getSourceMappingObjectKey(
  //   //   selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
  //   // )


  //   // const selectedObject = targetExeRes?.mappingTargets?.find((obj) => obj.name === name)

  //   // if (name === selectedEntityData?.mainTarget[0]?.name) {
  //   //   if (group.value) {
  //   //     return group.label || group.value || group.name || group.id
  //   //   } else {
  //   //     return group
  //   //   }
  //   // } else if (selectedObject?.mappingDisplayField) {
  //   //   return group[selectedObject.mappingDisplayField]
  //   // }
  //   // return ""
  // }

  // const getSourceMappingDisplayValue = (sourcevalue) => {
  //   if (Array.isArray(sourcevalue) && sourcevalue.length > 0) {
  //     return sourcevalue.map(item => {
  //       if (item && typeof item === 'object' && item !== null) {
  //         return item.label || item.value || item.name || item.id || String(item);
  //       }
  //       return String(item || '');
  //     }).join(', ');
  //   }

  //   // Handle single values
  //   if (sourcevalue && typeof sourcevalue === 'object' && sourcevalue !== null) {
  //     return sourcevalue.label || sourcevalue.value || sourcevalue.name || sourcevalue.id || String(sourcevalue);
  //   }

  //   return String(sourcevalue || '');
  // }

  const getSourceMappingDisplayValue = (sourcevalue) => {
    let data
    const toDisplay = (val) => {
      // Convert known sentinels to friendly labels for UI
      if (val === undefined) return ""
      // If object, prefer value for semantics
      if (val && typeof val === "object" && val !== null) {
        const v = val.value ?? val.label
        if (v === null || val.label === "null" || val.label === "Null") return "Null"
        if (v === "" || val.label === '<"">' || val.label === 'Blank') return "Blank"
        return String(val.label || val.value || val.name || val.id || val)
      }
      if (val === null || val === "null" || val === "Null") return "Null"
      if (val === "" || val === '<"">' || val === '<\"\">') return "Blank"
      return String(val || "")
    }

    if (Array.isArray(sourcevalue)) {
      // Return array of display strings for multi-select dropdown
      data = sourcevalue.map((val) => toDisplay(val))
      return data
    }

    // Handle single values - return as string
    data = toDisplay(sourcevalue)
    return data
  }

  const getTargetMappingDisplayValue = (group, name) => {
    if (!group) return null
    if (!name) return null

    const selectedObject = selectedEntityData?.mappingTargets?.find((obj) => obj.name === name)

    if (name === selectedEntityData?.mainTarget[0]?.name) {
      if (group.value) {
        return group.label || group.value || group.name || group.id
      } else {
        return group
      }
    } else if (selectedObject?.mappingDisplayField) {
      return group[selectedObject.mappingDisplayField] || group["name"] || group["id"] || group["value"] || group["label"]
    }
    return ""
  }

  const handleSourceValueChange = (index, newValue) => {
    const updatedMappings = [...mainMappings]

    // Convert selected labels back to full objects
    const allSourceOptions = getSourceFieldDropdownValues(attribute, target.name)
    const selectedObjects = Array.isArray(newValue)
      ? newValue.map(selectedLabel => {
        // Find the corresponding object for this label
        const matchingOption = allSourceOptions.find(option =>
          (typeof option === 'object' && option !== null)
            ? (option.label || option.value || option.name || option.id) === selectedLabel
            : option === selectedLabel
        )
        return matchingOption || selectedLabel
      })
      : newValue

    updatedMappings[index].sourcevalue = selectedObjects
    // Clear auto-mapping metadata when manually changed
    updatedMappings[index].matchMethod = null
    updatedMappings[index].confidence = 0
    setMainMappings(updatedMappings)
  }

  const handleTargetValueChange = (index, newValue) => {
    const updatedMappings = [...mainMappings]

    // Store the full object for UI display purposes
    // The ID extraction will happen only during save
    updatedMappings[index].targetvalue = newValue

    // Clear auto-mapping metadata when manually changed
    updatedMappings[index].matchMethod = null
    updatedMappings[index].confidence = 0
    setMainMappings(updatedMappings)
  }

  // Helper function to extract the proper ID/value for backend storage
  const extractTargetValueForSave = (targetValue, attribute) => {
    if (!targetValue) return null

    // Dynamically determine if this field requires ID mapping based on field metadata
    // Fields with mappingEnabled=true (dropdown/choice fields) typically require ID mapping
    const requiresIdMapping = attribute?.mappingEnabled === true

    if (requiresIdMapping && typeof targetValue === 'object') {
      // Extract the ID/value from the object
      let extractedValue = targetValue.id || targetValue.value || targetValue;

      // Convert to number if the field type is numeric
      if (attribute?.type === 'number' && extractedValue !== null && extractedValue !== undefined) {
        // Handle string values that should be converted to numbers
        if (typeof extractedValue === 'string' && extractedValue.trim() !== '') {
          const numericValue = Number(extractedValue);
          if (!isNaN(numericValue)) {
            extractedValue = numericValue;
          }
        }
      }

      return {
        id: extractedValue,
        name: targetValue.name || targetValue.label || targetValue.value || targetValue,
      }
    }

    // For non-mapping fields, apply type conversion if needed
    if (attribute?.type === 'number' && targetValue !== null && targetValue !== undefined) {
      if (typeof targetValue === 'string' && targetValue.trim() !== '') {
        const numericValue = Number(targetValue);
        if (!isNaN(numericValue)) {
          return numericValue;
        }
      }
    }

    // For other fields, return as-is (keeping existing behavior)
    return targetValue
  }

  const getTotalSourceCount = (attribute, name) => {
    if (!attribute) return 0

    if (source.type === "api") {
      if (target.name !== selectedEntityData.mainTarget[0].name) {
        return sourceMapRes[target.name]?.length || 0
      } else if (name === selectedEntityData.mainTarget[0].name) {
        // Use value-based filtering so label changes (Blank/Null) don't affect logic
        let data = getSourceFieldDropdownValues(attribute, name).filter(
          (item) => item?.value !== "" && item?.value !== "null" && item?.value !== null,
        )
        let count = data.length
        return count
      } else {
        const sourceMappingKey = getSourceMappingObjectKey()
        const fieldName = getSourceMappingFieldName()

        if (sourceMapRes[sourceMappingKey]?.length) {
          for (const field of sourceMapRes[sourceMappingKey]) {
            if (attribute.sourcefield === field[fieldName]) {
              return field.choices.length
            }
          }
        }
      }
    } else if (source.type === "csv") {
      // Use current unique source values directly instead of calling getSourceFieldDropdownValues
      // to ensure we get the most up-to-date count from the currently loaded file
      if (uniqueSourceValues && attribute.sourcefield) {
        const currentValues = getValuesByColumnName(uniqueSourceValues, attribute.sourcefield, "column", "values")
        const filteredValues = currentValues.filter(
          (item) =>
            item !== '<"">' &&
            item !== '<\"\">' &&
            item !== "null" &&
            item !== null &&
            item !== "" &&
            item !== undefined
        )
        return filteredValues.length
      }

      // Fallback to original method if uniqueSourceValues is not available
      let data = getSourceFieldDropdownValues(attribute)
      let updatedData = data.filter(
        (item) => {
          if (typeof item === "object") {
            return item.value !== "" && item.value !== "null" && item.value !== null
          }
          return item !== '<"">' && item !== 'Blank' && item !== 'null' && item !== 'Null' && item !== null
        }
      )
      return updatedData.length
    }
    return 0
  }

  const getTotalMappedCount = (mappings) => {
    if (mappings) {
      // Collect all unique mapped source values to avoid double counting
      const uniqueMappedValues = new Set()

      mappings.forEach((obj) => {
        if (Array.isArray(obj.sourcevalue)) {
          obj.sourcevalue.forEach((value) => {
            let actualValue = value ? (typeof value === "object" ? value.value || value.label : value) : "null"
            // Normalize display labels to underlying values
            if (actualValue === "Blank" || actualValue === '<"">' || actualValue === '<\"\">') actualValue = ""
            if (actualValue === "Null" || actualValue === "null") actualValue = null

            if (actualValue !== undefined && actualValue !== null && actualValue !== "") {
              uniqueMappedValues.add(actualValue)
            }
          })
        }
      })

      return uniqueMappedValues.size
    }
    return 0
  }

  const openRestoreDialog = () => {
    setOpenRestore(true)
  }

  const buildTargetIndexMap = (targetData) => {
    const map = new Map()

    if (!Array.isArray(targetData)) {
      return map
    }

    targetData.forEach((target, index) => {
      const key = target.value?.toLowerCase() || target.name?.toLowerCase()
      if (key) {
        map.set(key, index)
      }
    })

    return map
  }

  const findMatchIndex = (name, options) => {
    if (!name) return -1

    const nameLower = name.toLowerCase()

    const optionsMap = new Map()

    for (let i = 0; i < options.length; i++) {
      const key = options[i].value?.toLowerCase() || options[i].name?.toLowerCase()
      if (key) {
        optionsMap.set(key, i)
      }
    }

    return optionsMap.has(nameLower) ? optionsMap.get(nameLower) : -1
  }

  // Enhanced fuzzy logic functions for value mapping
  const levenshteinDistance = (str1, str2) => {
    const matrix = Array.from({ length: str1.length + 1 }, (_, i) =>
      Array.from({ length: str2.length + 1 }, (_, j) => (i === 0 ? j : j === 0 ? i : 0)),
    )

    for (let i = 1; i <= str1.length; i++) {
      for (let j = 1; j <= str2.length; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[i][j] = Math.min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j - 1] + cost)
      }
    }

    return matrix[str1.length][str2.length]
  }

  const findBestValueMatch = (sourceValue, targetValues, threshold = 0.4) => {
    if (!sourceValue || !targetValues || targetValues.length === 0) return null

    const normalizeValue = (str) =>
      String(str)
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9]/g, "")
    const normalizedSource = normalizeValue(sourceValue)

    let bestMatch = { target: null, similarity: 0, confidence: 0, index: -1 }

    for (let index = 0; index < targetValues.length; index++) {
      let targetValue = targetValues[index]
      let valueToCompare = targetValue
      if (typeof targetValue === "object") {
        valueToCompare = targetValue.value || targetValue.label || targetValue.name || String(targetValue)
      }

      const normalizedTarget = normalizeValue(valueToCompare)
      if (!normalizedTarget) continue

      let totalSimilarity = 0
      let weights = 0

      // Exact match - highest priority
      if (normalizedSource === normalizedTarget) {
        bestMatch = { target: targetValue, similarity: 1.0, confidence: 1.0, index }
        break
      }

      // Enhanced substring matching
      let substringScore = 0
      if (normalizedTarget.includes(normalizedSource)) {
        substringScore = 0.9
      } else if (normalizedSource.includes(normalizedTarget)) {
        substringScore = 0.85
      } else {
        let maxOverlap = 0
        for (let i = 0; i <= normalizedSource.length - 3; i++) {
          for (let j = 3; j <= normalizedSource.length - i; j++) {
            const substr = normalizedSource.substring(i, i + j)
            if (normalizedTarget.includes(substr)) {
              maxOverlap = Math.max(maxOverlap, substr.length)
            }
          }
        }
        if (maxOverlap >= 3) {
          substringScore = (maxOverlap / Math.max(normalizedSource.length, normalizedTarget.length)) * 0.8
        }
      }

      if (substringScore > 0) {
        totalSimilarity += substringScore * 0.5
        weights += 0.5
      }

      // Prefix matching boost
      let prefixScore = 0
      const minLen = Math.min(normalizedSource.length, normalizedTarget.length)
      if (minLen >= 3) {
        let commonPrefix = 0
        for (let i = 0; i < minLen; i++) {
          if (normalizedSource[i] === normalizedTarget[i]) {
            commonPrefix++
          } else {
            break
          }
        }
        if (commonPrefix >= 3) {
          prefixScore = (commonPrefix / Math.max(normalizedSource.length, normalizedTarget.length)) * 0.9
          totalSimilarity += prefixScore * 0.3
          weights += 0.3
        }
      }

      // Levenshtein distance
      const maxLength = Math.max(normalizedSource.length, normalizedTarget.length)
      if (maxLength > 0 && maxLength <= 30) {
        const distance = levenshteinDistance(normalizedSource, normalizedTarget)
        const levenshteinSim = Math.max(0, 1 - distance / maxLength)
        if (levenshteinSim > 0.5) {
          totalSimilarity += levenshteinSim * 0.2
          weights += 0.2
        }
      }

      const finalSimilarity = weights > 0 ? totalSimilarity / weights : 0
      let confidence = finalSimilarity

      if (substringScore > 0.8 || prefixScore > 0.6) {
        confidence = Math.min(confidence + 0.2, 1.0)
      }

      if (finalSimilarity > bestMatch.similarity && finalSimilarity >= threshold) {
        bestMatch = { target: targetValue, similarity: finalSimilarity, confidence, index }
      }
    }

    return bestMatch.target ? bestMatch : null
  }

  const reMap = () => {
    const source = getSourceFieldDropdownValues(attribute, target.name)
    const targets = JSON.parse(JSON.stringify(getTargetFieldDropdownValues(target.name, attribute)))

    const newData = {}
    newData.attribute = { ...attribute }
    newData.attribute.mappings = []

    const targetMap = new Map()
    for (let i = 0; i < targets.length; i++) {
      const item = targets[i]
      const key = item.value?.toLowerCase() || item.name?.toLowerCase()
      if (key) {
        targetMap.set(key, { item, index: i })
      }
    }

    const usedIndices = new Set()
    for (const src of source) {
      if (!src) continue

      const fuzzyMatch = findBestValueMatch(src, targets, 0.3)

      if (fuzzyMatch && !usedIndices.has(fuzzyMatch.index)) {
        usedIndices.add(fuzzyMatch.index)
        newData.attribute.mappings.push({
          sourcevalue: [src],
          expand: false,
          subcategories: [
            {
              sourcevalue: "",
              targetvalue: "",
            },
          ],
          targetvalue: fuzzyMatch.target,
          matchMethod: fuzzyMatch.similarity >= 0.95 ? "exact" : "fuzzy",
          confidence: fuzzyMatch.confidence,
        })
      } else {
        const srcLower = src.toLowerCase()
        const match = targetMap.get(srcLower)

        if (match && !usedIndices.has(match.index)) {
          usedIndices.add(match.index)
          newData.attribute.mappings.push({
            sourcevalue: [src],
            expand: false,
            subcategories: [
              {
                sourcevalue: "",
                targetvalue: "",
              },
            ],
            targetvalue: match.item,
            matchMethod: "exact",
            confidence: 1.0,
          })
        }
      }
    }

    return newData
  }

  const onRestore = () => {
    const newData = reMap()
    if (newData.attribute && newData.attribute.mappings) {
      setMainMappings([...newData.attribute.mappings])
    }
    setOpenRestore(false)

    return newData
  }

  const onClose = () => {
    setOpenRestore(false)
  }

  const getFieldByTargetField = (target, targetField) => {
    if (target && Array.isArray(target.fieldMappings)) {
      for (const item of target.fieldMappings) {
        if (item.targetfield === targetField) {
          return item
        }
      }
    }
    return ""
  }

  const getSourceFieldColumnValue = (target, targetField) => {
    return target?.fieldMappings?.find((item) => item.targetfield === targetField)?.sourcefield || ""
  }

  const getSubCategorySourceDropdownValues = (attribute, mainIndex, subCatName) => {
    if (source.type == "csv") {
      const temp = []
      const SubCategorySourceColumn = getSourceFieldColumnValue(target, subCatName)
      if (uniqueSourceValues) {
        const temp1 = uniqueSourceValues.find((col) => col.column === SubCategorySourceColumn)
        return temp1 ? temp1.values : []
      }
      return temp
    } else if (source.type == "api") {
      const SubCategoryObj = getFieldByTargetField(target, subCatName)
      return getSourceFieldDropdownValues(SubCategoryObj, target.name)
    }
  }

  // Fixed source field dropdown values - no filtering of mapped values
  const getSourceFieldDropdownValues = (attribute, name = "") => {
    if (source.type === "api") {
      const selectedObject = selectedEntityData?.mappingTargets?.find((obj) => obj.name === name)
      if (selectedObjectData.sourceMappingConfig && typeof selectedObjectData.sourceMappingConfig === "string") {
        selectedObjectData.sourceMappingConfig = JSON.parse(selectedObjectData.sourceMappingConfig)
      }
      if (name === selectedEntityData?.mainTarget[0]?.name) {
        const sourceMappingKey = getSourceMappingObjectKey(
          selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
        )
        const sourceChoiceKey = getSourceChoiceMappingAttribute(
          selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
        )

        const targetChoiceKey = getTargetChoiceMappingAttribute(
          selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
        )
        const fieldName = getSourceMappingFieldName(
          selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
        )

        if (sourceMapRes[sourceMappingKey]?.length) {
          for (const field of sourceMapRes[sourceMappingKey]) {
            if (field.nested_fields && field.name === 'category' && (attribute.sourcefield.toLowerCase() === 'sub_category' || attribute.sourcefield === 'Sub-Category' || attribute.sourcefield.toLowerCase() === 'item_category' || attribute.sourcefield === 'Item')) {
              for (const nestedField of field.nested_fields) {
                if (nestedField.name === 'sub_category' && (attribute.sourcefield.toLowerCase() === 'sub_category' || attribute.sourcefield === 'Sub-Category')) {
                  // Deduplicate sub-category options
                  const seen = new Set()
                  const subCategoryChoices = []
                  field.choices.forEach((catChoice) => {
                    (catChoice.nested_options || []).forEach((subCatChoice) => {
                      const val = subCatChoice?.value
                      const key = val == null ? null : String(val).toLowerCase()
                      if (val != null && !seen.has(key)) {
                        seen.add(key)
                        subCategoryChoices.push({
                          label: val,
                          value: val,
                        })
                      }
                    })
                  })
                  subCategoryChoices.push({ label: "Blank", value: "" }, { label: "Null", value: null })
                  return subCategoryChoices
                }
                else if (nestedField.name === 'item_category' && (attribute.sourcefield.toLowerCase() === 'item_category' || attribute.sourcefield === 'Item')) {
                  // Deduplicate item-category options
                  const seen = new Set()
                  const itemCategoryChoices = []
                  field.choices.forEach((catChoice) => {
                    (catChoice.nested_options || []).forEach((subCatChoice) => {
                      (subCatChoice.nested_options || []).forEach((itemCatChoice) => {
                        const val = itemCatChoice?.value
                        const key = val == null ? null : String(val).toLowerCase()
                        if (val != null && !seen.has(key)) {
                          seen.add(key)
                          itemCategoryChoices.push({
                            label: val,
                            value: val,
                          })
                        }
                      })
                    })
                  })
                  itemCategoryChoices.push({ label: "Blank", value: "" }, { label: "Null", value: null })
                  return itemCategoryChoices
                }
              }
            }
            else if (field.name === 'category' && attribute.sourcefield.toLowerCase() === 'category') {
              // Deduplicate category options
              const seen = new Set()
              const categoryChoices = []
              field.choices.forEach((catChoice) => {
                const val = catChoice?.value
                const key = val == null ? null : String(val).toLowerCase()
                if (val != null && !seen.has(key)) {
                  seen.add(key)
                  categoryChoices.push({
                    label: val,
                    value: val,
                  })
                }
              })
              categoryChoices.push({ label: "Blank", value: "" }, { label: "Null", value: null })
              return categoryChoices
            }
            else if (attribute.sourcefield === field[fieldName]) {
              let data
              if (Array.isArray(field.choices)) {
                data = field.choices.map((choice) => {
                  // Handle both string arrays and object arrays
                  if (typeof choice === "string") {
                    return {
                      label: choice,
                      value: choice,
                    }
                  } else {
                    return {
                      label: choice[sourceChoiceKey],
                      value: choice[targetChoiceKey] ?? choice[sourceChoiceKey] ?? choice,
                    }
                  }
                })
              } else {
                data = Object.entries(field.choices).map(([key, value]) => {
                  const displayValue = Array.isArray(value) ? value[0] : value
                  return {
                    label: displayValue || key,
                    value: key,
                  }
                })
              }

              // Show friendly labels in UI
              data = data
                .sort((a, b) => String(a.label).localeCompare(String(b.label)))
                .concat([{ label: "Blank", value: "" }, { label: "Null", value: null }])
              return data
            }
          }
        }
      } else if (selectedObject?.mappingDisplayField) {
        return [...sourceMapRes[target.name].map((obj) => {
          let sample = {
            label: obj[selectedObject?.mappingDisplayField],
            value: obj[selectedObject?.sourceMappingValueField] || obj?.id || obj[selectedObject?.mappingDisplayField],
          }

          return sample
        })]
          .sort()
          // Show friendly labels in UI
          .concat([{ label: "Blank", value: "" }, { label: "Null", value: null }])
      }
    } else if (source.type === "csv" && attribute.mappingEnabled) {
      return getValuesByColumnName(uniqueSourceValues, attribute.sourcefield, "column", "values")
        .sort()
        // Provide friendly display labels; these are strings in CSV mode
        .concat(["Blank", "Null"])
    }

    return []
  }

  // Returns only unmapped source values for the main mapping, except for the current mapping being edited
  const getUnmappedSourceFieldDropdownValues = (attribute, name = "", currentIndex = -1) => {
    const allOptions = getSourceFieldDropdownValues(attribute, name);
    // Collect all mapped source values except for the current mapping
    const mappedValues = new Set();
    mainMappings.forEach((mapping, idx) => {
      if (idx !== currentIndex && Array.isArray(mapping.sourcevalue)) {
        mapping.sourcevalue.forEach((val) => {
          if (val !== '<"">' && val !== "null" && val !== "" && val != null) {
            // Only add to mappedValues if this mapping has a targetvalue (i.e., is mapped)
            if (mapping.targetvalue) {
              // Extract the value from object if needed
              const valueToAdd = typeof val === 'object' && val !== null
                ? (val.value || val.label || val.name || val.id)
                : val;
              mappedValues.add(valueToAdd);
            }
          }
        });
      }
    });

    // Filter out mapped values, but keep values from current row
    let optionsData = allOptions.filter((option) => {
      const value = typeof option === "object" ? option.value : option;
      return !mappedValues.has(value);
    }).map((option) => {
      if (typeof option === "object") {
        // If label is an object, use the value instead
        if (typeof option.label === "object") {
          return option.value;
        }
        return option.label;
      }
      return option;
    });

    return optionsData;
  };

  const getStatusIcon = (mappings, k) => {
    const mapping = mappings[k]
    if (!mapping) return null

    // Check if both source and target values are selected
    const hasSourceValue =
      mapping.sourcevalue &&
      mapping.sourcevalue.length > 0 &&
      mapping.sourcevalue.some((val) => val !== "null" && val !== "Null" && val !== null && val !== '<"">' && val !== "" && val !== "Blank")
    const hasTargetValue = mapping.targetvalue !== null

    if (hasSourceValue && hasTargetValue) {
      return (
        <div className={globalStyles.statusIconSuccess}>
          <span className={globalStyles.iconText}>✓</span>
        </div>
      )
    } else {
      return (
        <div className={globalStyles.statusIconError}>
          <span className={globalStyles.iconText}>×</span>
        </div>
      )
    }
  }

  const getConfidenceIndicator = (mapping) => {
    if (!mapping || !mapping.confidence || !mapping.sourcevalue?.length || !mapping.targetvalue) {
      return null
    }

    const confidence = mapping.confidence
    let color = "#ef4444" // red for low confidence
    let label = "Low"

    if (confidence >= 0.9) {
      color = "#10b981" // green for high confidence
      label = "High"
    } else if (confidence >= 0.7) {
      color = "#f59e0b" // yellow for medium confidence
      label = "Med"
    }

    return (
      <div
        style={{
          display: "inline-flex",
          alignItems: "center",
          gap: "4px",
          fontSize: "11px",
          fontFamily: "Inter, sans-serif",
          color: color,
          fontWeight: 500,
          marginLeft: "8px",
        }}
      >
        <div
          style={{
            width: "6px",
            height: "6px",
            borderRadius: "50%",
            backgroundColor: color,
          }}
        />
        {label} ({Math.round(confidence * 100)}%)
      </div>
    )
  }

  // Helper function to get display value for search
  const getMappingSearchString = (mapping) => {
    // Combine source and target values as string for search
    let sourceStr = '';
    if (Array.isArray(mapping.sourcevalue)) {
      sourceStr = mapping.sourcevalue.map(item => {
        if (item && typeof item === 'object') {
          return item.label || item.value || item.name || item.id || String(item);
        }
        return String(item || '');
      }).join(', ');
    } else if (mapping.sourcevalue && typeof mapping.sourcevalue === 'object') {
      sourceStr = mapping.sourcevalue.label || mapping.sourcevalue.value || mapping.sourcevalue.name || mapping.sourcevalue.id || String(mapping.sourcevalue);
    } else {
      sourceStr = String(mapping.sourcevalue || '');
    }
    let targetStr = '';
    if (mapping.targetvalue && typeof mapping.targetvalue === 'object') {
      targetStr = mapping.targetvalue.label || mapping.targetvalue.value || mapping.targetvalue.name || mapping.targetvalue.id || String(mapping.targetvalue);
    } else {
      targetStr = String(mapping.targetvalue || '');
    }
    return `${sourceStr} ${targetStr}`.toLowerCase();
  };

  // Filtered mappings based on searchTerm
  const filteredMappings = searchTerm.trim() === ''
    ? mainMappings
    : mainMappings.filter(mapping => getMappingSearchString(mapping).includes(searchTerm.trim().toLowerCase()));

  // Smart Auto-Map logic for value mapping
  const handleSmartAutoMapClick = () => {
    // Check if there are unmapped source values before showing confirmation
    const totalSourceValues = getTotalSourceCount(attribute, target.name);
    const totalMappedValues = getTotalMappedCount(mainMappings);
    const unmappedCount = Math.max(0, totalSourceValues - totalMappedValues);

    if (unmappedCount === 0) {
      toast.success("All values have been mapped successfully! No unmapped source fields to auto-map.", {
        position: "top-right",
        autoClose: 4000,
        style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
      });
      return;
    }

    setShowSmartAutoMapConfirm(true)
  }
  const executeSmartAutoMap = async () => {
    setIsLoading(true);
    setShowSmartAutoMapConfirm(false);

    try {
      // Check if there are unmapped source values (excluding <""> and null)
      const totalSourceValues = getTotalSourceCount(attribute, target.name);
      const totalMappedValues = getTotalMappedCount(mainMappings);
      const unmappedCount = Math.max(0, totalSourceValues - totalMappedValues);

      // If no unmapped values, show message and exit
      if (unmappedCount === 0) {
        toast.success("All values have been mapped successfully! No unmapped source fields to auto-map.", {
          position: "top-right",
          autoClose: 4000,
          style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
        });
        return;
      }

      // Allow UI to update with loading state
      await new Promise(resolve => setTimeout(resolve, 100));

      // Gather all source and target values
      let sourceOptions = getSourceFieldDropdownValues(attribute, target.name)

      sourceOptions = sourceOptions.filter(
        (item) => {
          const v = item.value ?? item
          return v !== '<"">' && v !== "Blank" && v !== "null" && v !== "Null" && v !== null && v !== ""
        },
      )
      const sourceValues = sourceOptions.map((item) => {
        return typeof item === "object" ? { label: item.label, value: item.value } : { label: item, value: item }
      })
      const targetValues = getTargetFieldDropdownValues(target.name, attribute)

      // Precompute normalized targets for fast matching
      const normalizeValue = (str) =>
        String(str)
          .toLowerCase()
          .trim()
          .replace(/[^a-z0-9]/g, "")

      const normalizedTargets = targetValues.map((t, idx) => {
        const text = typeof t === "object" ? (t.value || t.label || t.name || String(t)) : String(t)
        const textNorm = normalizeValue(text)
        return { textNorm, original: t, index: idx, len: textNorm.length }
      })

      const exactMap = new Map()
      normalizedTargets.forEach((nt) => {
        if (nt.textNorm) exactMap.set(nt.textNorm, nt.index)
      })

      const getBigrams = (s) => {
        const res = new Set()
        for (let i = 0; i < s.length - 1; i++) res.add(s.slice(i, i + 2))
        return res
      }

      const bigramSimilarity = (a, b) => {
        if (a.length < 2 || b.length < 2) return 0
        const A = getBigrams(a)
        const B = getBigrams(b)
        let inter = 0
        A.forEach((g) => {
          if (B.has(g)) inter++
        })
        const union = A.size + B.size - inter
        return union ? inter / union : 0
      }

      // Map to keep track of used target indices
      const usedTargetIndices = new Set()
      const newMappings = []
      let exactMatches = 0
      let fuzzyMatches = 0
      let highSimilarityMatches = 0

      // Process in chunks to prevent UI blocking
      const chunkSize = 20;
      for (let i = 0; i < sourceValues.length; i += chunkSize) {
        const chunk = sourceValues.slice(i, i + chunkSize);

        chunk.forEach((src, chunkIndex) => {
          const actualIndex = i + chunkIndex;
          if (src.label === undefined || src.label === null || String(src.label).trim() === "") return
          const ns = normalizeValue(src.label)
          if (!ns) return

          // Fast exact match via map
          let matched = null
          if (exactMap.has(ns)) {
            const idx = exactMap.get(ns)
            matched = { target: normalizedTargets[idx].original, index: idx, similarity: 1, confidence: 1 }
          } else {
            // Fast scan using substring and bigram similarity (avoid heavy Levenshtein)
            let best = null
            let bestScore = 0
            for (let k = 0; k < normalizedTargets.length; k++) {
              const nt = normalizedTargets[k]
              if (!nt.textNorm) continue
              let score = 0
              if (nt.textNorm.includes(ns)) {
                // source is substring of target
                score = ns.length / (nt.len || 1)
                // small boost
                score = Math.min(1, score * 0.95 + 0.05)
              } else if (ns.includes(nt.textNorm)) {
                // target is substring of source
                score = (nt.len || 0) / (ns.length || 1)
                score = Math.min(1, score * 0.9 + 0.05)
              } else {
                // fallback to cheap bigram similarity
                score = bigramSimilarity(ns, nt.textNorm) * 0.85
              }
              if (score > bestScore) {
                bestScore = score
                best = nt
                if (bestScore >= 0.98) break // early exit on near-exact
              }
            }
            if (best && bestScore >= 0.35) {
              matched = { target: best.original, index: best.index, similarity: bestScore, confidence: bestScore }
            }
          }

          if (matched && !usedTargetIndices.has(matched.index)) {
            let matchMethod = "fuzzy"
            if (matched.similarity >= 0.95) {
              matchMethod = "exact"
              exactMatches++
            } else if (matched.confidence >= 0.7) {
              matchMethod = "high_similarity"
              highSimilarityMatches++
            } else {
              fuzzyMatches++
            }
            newMappings.push({
              sourcevalue: [src],
              targetvalue: matched.target,
              matchMethod: matchMethod,
              confidence: matched.confidence,
              subCategories: [],
            })
            usedTargetIndices.add(matched.index)
          }
        });

        // Allow UI to update between chunks
        if (i + chunkSize < sourceValues.length) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      setMainMappings(newMappings)

      const totalMapped = exactMatches + highSimilarityMatches + fuzzyMatches
      toast.success(
        `Smart Auto-Map completed! Mapped ${totalMapped}/${sourceValues.length} values. Exact: ${exactMatches}, High similarity: ${highSimilarityMatches}, Fuzzy: ${fuzzyMatches}`,
        {
          position: "top-right",
          autoClose: 6000,
          style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
        },
      )
    } catch (error) {
      toast.error("Smart Auto-Map failed. Please try again.", {
        position: "top-right",
        autoClose: 3000,
        style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Clear All Mappings functionality
  const handleClearAllMappingsClick = () => {
    // Check if there are any mappings to clear
    const hasMappings = mainMappings.some(mapping =>
      (Array.isArray(mapping.sourcevalue) && mapping.sourcevalue.length > 0) ||
      mapping.targetvalue !== null
    )

    if (!hasMappings) {
      toast.info("No mappings to clear.", {
        position: "top-right",
        autoClose: 3000,
        style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
      })
      return
    }

    setShowClearAllConfirm(true)
  }

  const executeClearAllMappings = () => {
    // Reset to initial state with one empty mapping
    const clearedMappings = [
      {
        sourcevalue: [],
        targetvalue: null,
        subCategories: [],
        matchMethod: null,
        confidence: 0,
      }
    ]

    setMainMappings(clearedMappings)

    // Reset expanded state
    setExpandedMappings({
      main: [false],
      subCategory: [[]],
    })

    toast.success("All mappings have been cleared successfully!", {
      position: "top-right",
      autoClose: 3000,
      style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
    })

    setShowClearAllConfirm(false)
  }

  return (
    <div style={{ height: "700px", padding: "0 0 20px 0", marginBottom: "150px" }}>
      <div className={styles.header}>
        <div style={{ backgroundColor: "#170903", padding: "10px 0" }}>
          <div className={styles.dFlex} style={{ alignItems: "center", justifyContent: "space-between" }}>
            <div className={globalStyles.headerStyle} style={{ paddingLeft: "45px" }}>
              {target?.name === "Tickets" &&
                (attribute?.targetfield === "category" ||
                  attribute?.targetfield === "sub_category" ||
                  attribute?.targetfield === "item_category")
                ? "Nested Mapping"
                : "Value Mapping"}
            </div>
            <div>
              <HiXMark
                style={{ fontSize: "16px", cursor: "pointer", padding: "0 24px", color: "#ffffff" }}
                onClick={close}
              />
            </div>
          </div>
        </div>

        {target?.name === "Tickets" &&
          (attribute?.targetfield === "category" ||
            attribute?.targetfield === "sub_category" ||
            attribute?.targetfield === "item_category") ? (
          !openGuide ? (
            <div className={`${styles.mappingGuide} ${openGuide ? styles.mappingGuide : styles.closemap}`}>
              <div className={`${styles.dFlex} ${styles.closemg}`}>
                <span className={globalStyles.selectionName}>SEE MAPPING GUIDE</span>
                <HiChevronDown style={{ marginLeft: "auto", marginRight: "0" }} onClick={handleOpen} />
              </div>
            </div>
          ) : (
            <div className={`${styles.mappingGuide} ${openGuide ? styles.mappingGuide : styles.closemap}`}>
              <div className={`${styles.dFlex} ${styles.closemg}`}>
                <span className={globalStyles.selectionName}>MAPPING GUIDE</span>
                <HiChevronUp style={{ marginLeft: "auto", marginRight: "0" }} onClick={handleOpen} />
              </div>

              <div className={styles.guideStyle}>
                Your data follows a 4-level nesting structure - <b>Field → Value → Sub-Category → Item.</b>
              </div>
              <div className={styles.guideStyle}>
                <div style={{ marginTop: "10px" }}>Sample mapping</div>
                <div style={{ marginTop: "10px" }}>Source: HR requests → Target: HR</div>
                <div style={{ marginLeft: "20px" }}>└─ Sub-category: Emp. Onboarding → Onboarding</div>
                <div style={{ marginLeft: "40px" }}>└─ Item: Laptop issue → Device request</div>
              </div>
            </div>
          )
        ) : null}

        <div style={{ padding: "20px 45px" }}>
          <div style={{ display: "flex", alignItems: "flex-start", gap: "30px", alignSelf: "stretch" }}>
            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <div className={globalStyles.poppinsHeaderStyle}>Data Type</div>
              <div className={globalStyles.interSummaryStyle}>{target?.name || "Tickets"}</div>
            </div>

            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <div className={globalStyles.poppinsHeaderStyle}>Source field selected</div>
              <div className={globalStyles.interSummaryStyle}>{attribute.sourcefield}</div>
            </div>
          </div>
        </div>

        <div style={{ display: "flex", gap: "30px", padding: "0 45px" }}>
          <div
            style={{
              display: "flex",
              padding: "10px 20px",
              justifyContent: "center",
              alignItems: "center",
              gap: "15px",
              flex: "1 0 0",
              alignSelf: "stretch",
              border: "1px solid #E5E7EB",
              borderRadius: "8px",
            }}
          >
            <div className={styles.countNumber}>{getTotalSourceCount(attribute, target.name)}</div>
            <div className={styles.countDescription}>Source values</div>
          </div>
          <div
            style={{
              display: "flex",
              padding: "10px 20px",
              justifyContent: "center",
              alignItems: "center",
              gap: "15px",
              flex: "1 0 0",
              alignSelf: "stretch",
              border: "1px solid #E5E7EB",
              borderRadius: "8px",
            }}
          >
            <div className={styles.countNumber}>{getTotalMappedCount(mainMappings)}</div>
            <div className={styles.countDescription}>Mapped values</div>
          </div>
          <div
            style={{
              display: "flex",
              padding: "10px 20px",
              justifyContent: "center",
              alignItems: "center",
              gap: "15px",
              flex: "1 0 0",
              alignSelf: "stretch",
              border: "1px solid #E5E7EB",
              borderRadius: "8px",
            }}
          >
            <div className={styles.countNumber}>
              {(() => {
                const sourceCount = getTotalSourceCount(attribute, target.name)
                const mappedCount = getTotalMappedCount(mainMappings)
                // Ensure unmapped count never goes negative and resets properly when new file is uploaded
                return Math.max(0, sourceCount - mappedCount)
              })()}
            </div>
            <div className={styles.countDescription}>Unmapped values</div>
          </div>
        </div>

        {/* Auto-mapping summary */}
        {(() => {
          const exactCount = mainMappings.filter((m) => m.matchMethod === "exact").length
          const highSimCount = mainMappings.filter((m) => m.matchMethod === "high_similarity").length
          const fuzzyCount = mainMappings.filter((m) => m.matchMethod === "fuzzy").length
          const totalAutoMapped = exactCount + highSimCount + fuzzyCount

          if (totalAutoMapped > 0) {
            return (
              <div
                style={{
                  padding: "0 45px",
                  marginTop: "15px",
                  marginBottom: "10px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "20px",
                    padding: "12px 20px",
                    background: "#f8fafc",
                    border: "1px solid #e2e8f0",
                    borderRadius: "8px",
                    fontSize: "13px",
                    fontFamily: "Inter, sans-serif",
                    color: "#475569",
                  }}
                >
                  <div style={{ fontWeight: "600", color: "#1e293b" }}>Auto-mapping Summary:</div>
                  {exactCount > 0 && (
                    <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
                      <div style={{ width: "8px", height: "8px", borderRadius: "50%", background: "#10b981" }} />
                      <span>{exactCount} Exact</span>
                    </div>
                  )}
                  {highSimCount > 0 && (
                    <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
                      <div style={{ width: "8px", height: "8px", borderRadius: "50%", background: "#f59e0b" }} />
                      <span>{highSimCount} High Similarity</span>
                    </div>
                  )}
                  {fuzzyCount > 0 && (
                    <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
                      <div style={{ width: "8px", height: "8px", borderRadius: "50%", background: "#ef4444" }} />
                      <span>{fuzzyCount} Fuzzy</span>
                    </div>
                  )}
                </div>
              </div>
            )
          }
          return null
        })()}

        <div className={styles.dFlex} style={{ padding: "0 45px", alignItems: "center", gap: "10px" }}>
          <Dialog open={openRestore} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <RestoreDetailsModal onRestore={onRestore} onClose={onClose} />
            </DialogContent>
          </Dialog>

          <div className={styles.dFlex} style={{ marginTop: "10px", gap: "10px" }}>
            <button
              className={`${styles.dFlex} ${styles.buttonStyle} ${styles.smartAutoMapBtn}`}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                gap: "5px",
                color: "white",
                border: "none",
                fontFamily: "Inter",
                width: "200px",
                height: "35px",
                opacity: isLoading ? 0.7 : 1,
              }}
              onClick={handleSmartAutoMapClick}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <div className={globalStyles.loader} style={{ width: '16px', height: '16px' }}></div>
                  Processing...
                </>
              ) : (
                <>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9L15 5V9H19Z" />
                  </svg>
                  Smart Auto-Map
                </>
              )}
            </button>

            <button
              className={`${styles.dFlex} ${styles.buttonStyle} ${styles.clearAllMappingsBtn}`}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                gap: "5px",
                color: "white",
                border: "none",
                fontFamily: "Inter",
                width: "200px",
                height: "35px",
              }}
              onClick={handleClearAllMappingsClick}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z" />
              </svg>
              Clear All Mappings
            </button>
          </div>

          <div className={globalStyles.searchWrapper} style={{ marginTop: "10px", marginLeft: "auto" }}>
            <SearchIcon className={globalStyles.searchIcon} />
            <input
              type="text"
              placeholder="Search..."
              className={globalStyles.searchInput}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={(e) => {
                e.target.style.width = "200px"
                e.target.placeholder = "Typing..."
              }}
              onBlur={(e) => {
                if (!e.target.value) {
                  e.target.style.width = "80px"
                  e.target.placeholder = "Search..."
                }
              }}
            />
          </div>
        </div>

        <div style={{ padding: "0 45px" }}>
          <table className={globalStyles.table} style={{ marginBottom: "20px", width: "100%" }}>
            <thead className={globalStyles.tableHeader}>
              <tr className={globalStyles.rowStyles}>
                <th className={globalStyles.headerCell}>Status</th>
                <th className={globalStyles.headerCell} style={{ width: "300px" }}>
                  Source values
                </th>
                <th className={globalStyles.headerCell} style={{ width: "300px" }}>
                  Target values
                </th>
                <th className={globalStyles.headerCell}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredMappings.map((mapping, index) => {
                // Find the actual index in mainMappings
                const mainIndex = mainMappings.findIndex(m => m === mapping);
                return (
                  <React.Fragment key={mapping.id}>
                    <tr className={globalStyles.tableRow}>
                      <td className={globalStyles.cell} style={{ width: "80px", textAlign: "center" }}>
                        <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: "4px" }}>
                          {getStatusIcon(mainMappings, mainIndex)}
                          {getConfidenceIndicator(mapping)}
                        </div>
                      </td>
                      <td className={globalStyles.cell}>
                        <Dropdown
                          value={getSourceMappingDisplayValue(mapping.sourcevalue)}
                          options={getUnmappedSourceFieldDropdownValues(attribute, target.name, mainIndex)}
                          onChange={(newValue) => handleSourceValueChange(mainIndex, newValue)}
                          placeholder="Select Source"
                          multiSelect={true}
                        />
                      </td>
                      <td className={globalStyles.cell}>
                        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                          <Dropdown
                            value={(() => {
                              // Handle null mapping explicitly
                              if (mapping.targetvalue === null) {
                                return null;
                              }

                              // Find the unique value that corresponds to the current mapping
                              if (!mapping.targetvalue) return null;

                              const currentOptions = getDropdownOptionsWithIndicators(target, attribute, mainIndex);
                              const matchingOption = currentOptions.find(opt => {
                                // Handle null option matching
                                if (opt.originalValue === null && mapping.targetvalue === null) {
                                  return true;
                                }

                                // Compare the original values to find the match
                                if (typeof opt.originalValue === 'object' && typeof mapping.targetvalue === 'object') {
                                  const optId = opt.originalValue.id || opt.originalValue.value || opt.originalValue.label;
                                  const mappingId = mapping.targetvalue.id || mapping.targetvalue.value || mapping.targetvalue.label;
                                  return optId === mappingId;
                                }
                                return opt.originalValue === mapping.targetvalue;
                              });

                              return matchingOption ? matchingOption.value : getTargetMappingDisplayValue(mapping.targetvalue, target.name);
                            })()}
                            options={getDropdownOptionsWithIndicators(target, attribute, mainIndex)}
                            onChange={(newValue) => {
                              // Handle null value selection explicitly
                              if (newValue === null) {
                                handleTargetValueChange(mainIndex, null);
                                return;
                              }

                              // Safely handle string replacement - check if newValue is a string first
                              let cleanValue = newValue;
                              if (typeof newValue === 'string') {
                                cleanValue = newValue.replace(' [Mapped]', '');
                              }

                              const allOptions = getDropdownOptionsWithIndicators(target, attribute, mainIndex);

                              // First try to find by exact value match (including null)
                              let selectedOption = allOptions.find(opt => opt.value === cleanValue || opt.value === newValue);

                              // If not found, try to find by label match (for backward compatibility)
                              if (!selectedOption) {
                                selectedOption = allOptions.find(opt => opt.label === cleanValue || opt.label === newValue);
                              }

                              // If still not found, try to find by display value match
                              if (!selectedOption) {
                                selectedOption = allOptions.find(opt => {
                                  const displayValue = getTargetMappingDisplayValue(opt.originalValue, target.name);
                                  return String(displayValue) === String(cleanValue) || String(displayValue) === String(newValue);
                                });
                              }

                              handleTargetValueChange(mainIndex, selectedOption ? selectedOption.originalValue : cleanValue);
                            }}
                            placeholder="Select Target"
                            displayKey="label"
                            valueKey="value"
                          />
                        </div>
                      </td>
                      <td className={globalStyles.cell}>
                        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                          {mainIndex >= 0 && (
                            <button onClick={() => deleteMapping(mainIndex)} className={styles.deleteButton}>
                              <img src="/assets/minus-sign.png" alt="Delete" style={{ width: "16px", height: "16px" }} />
                            </button>
                          )}
                          {target?.name === "Tickets" && attribute?.targetfield === "category" && (
                            <HiLink style={{ cursor: "pointer" }} onClick={() => toggleMainMapping(mainIndex)} />
                          )}
                        </div>
                      </td>
                    </tr>

                    {expandedMappings.main[mainIndex] && mapping.subCategories.length > 0 && (
                      <tr>
                        <td colSpan="4" style={{ padding: 0 }}>
                          <div style={{ marginLeft: "40px" }}>
                            {mapping.subCategories.map((subCategory, subIndex) => (
                              <React.Fragment key={`${mainIndex}-${subIndex}`}>
                                <table style={{ marginBottom: "10px", width: "100%" }}>
                                  <thead>
                                    <tr className={globalStyles.rowStyles}>
                                      <th className={globalStyles.headerCell}>Source sub-category</th>
                                      <th className={globalStyles.headerCell}>Target sub-category</th>
                                      <th className={globalStyles.headerCell}>Mapping actions</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr className={globalStyles.tableRow}>
                                      <td className={globalStyles.cell}>
                                        <Dropdown
                                          value={Array.isArray(subCategory.sourcevalue)
                                            ? subCategory.sourcevalue.map(val => {
                                              if (val && typeof val === 'object' && val !== null) {
                                                return val.label || val.value || val.name || val.id || String(val);
                                              }
                                              return String(val || '');
                                            })
                                            : []
                                          }
                                          options={getSubCategorySourceDropdownValues(attribute, mainIndex, "sub_category")}
                                          onChange={(newValue) => {
                                            // if (areAllValuesMapped()) {
                                            //   toast.success("All values have been mapped!")
                                            //   return
                                            // }
                                            handleSubCategorySourceValueChange(mainIndex, subIndex, newValue)
                                          }}
                                          placeholder="Select Source sub-category"
                                          multiSelect={true}
                                        />
                                      </td>
                                      <td className={globalStyles.cell}>
                                        <Dropdown
                                          value={subCategory?.targetvalue?.value}
                                          options={getSubDropdownOptionsWithIndicators(attribute, mainIndex, subIndex)}
                                          onChange={(newValue) => {
                                            // Remove [Mapped] from the selected label and find the corresponding value
                                            let cleanLabel = newValue;
                                            if (typeof newValue === 'string') {
                                              cleanLabel = newValue.replace(' [Mapped]', '');
                                            }

                                            const selectedOption = getSubDropdownOptionsWithIndicators(attribute, mainIndex, subIndex)
                                              .find(opt => {
                                                const optionLabel = typeof opt.label === 'string' ? opt.label.replace(' [Mapped]', '') : opt.label;
                                                return optionLabel === cleanLabel;
                                              });
                                            handleSubCategoryTargetValueChange(mainIndex, subIndex, selectedOption ? selectedOption.value : newValue);
                                          }}
                                          placeholder="Select Target sub-category"
                                          displayKey="label"
                                          valueKey="label"
                                        />
                                      </td>
                                      <td className={globalStyles.cell}>
                                        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                          {subIndex >= 0 && (
                                            <button
                                              onClick={() => deleteSubCategory(mainIndex, subIndex)}
                                              className={styles.deleteButton}
                                            >
                                              <img
                                                src="/assets/minus-sign.png"
                                                alt="Delete"
                                                style={{ width: "16px", height: "16px" }}
                                              />
                                            </button>
                                          )}
                                          <HiLink
                                            style={{
                                              cursor: "pointer",
                                            }}
                                            onClick={() => toggleSubCategory(mainIndex, subIndex)}
                                          />
                                        </div>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>

                                {expandedMappings.subCategory[mainIndex][subIndex] && subCategory.items.length > 0 && (
                                  <div style={{ marginLeft: "40px" }}>
                                    {subCategory.items.map((item, itemIndex) => (
                                      <div key={itemIndex}>
                                        <table
                                          style={{
                                            marginBottom: "10px",
                                            width: "100%",
                                          }}
                                        >
                                          <thead>
                                            <tr className={globalStyles.rowStyles}>
                                              <th className={globalStyles.headerCell}>Source item</th>
                                              <th className={globalStyles.headerCell}>Target item</th>
                                              <th className={globalStyles.headerCell}>Mapping actions</th>
                                            </tr>
                                          </thead>
                                          <tbody>
                                            <tr className={globalStyles.tableRow}>
                                              <td className={globalStyles.cell}>
                                                <Dropdown
                                                  value={Array.isArray(item.sourcevalue)
                                                    ? item.sourcevalue.map(val => {
                                                      if (val && typeof val === 'object' && val !== null) {
                                                        return val.label || val.value || val.name || val.id || String(val);
                                                      }
                                                      return String(val || '');
                                                    })
                                                    : []
                                                  }
                                                  options={getSubCategorySourceDropdownValues(
                                                    attribute,
                                                    mainIndex,
                                                    "item_category",
                                                  )}
                                                  onChange={(newValue) =>
                                                    handleItemSourceValueChange(mainIndex, subIndex, itemIndex, newValue)
                                                  }
                                                  placeholder="Select Source items"
                                                  multiSelect={true}
                                                />
                                              </td>
                                              <td className={globalStyles.cell}>
                                                <Dropdown
                                                  value={item?.targetvalue?.value}
                                                  options={getItemDropdownOptionsWithIndicators(attribute, mainIndex, subIndex, itemIndex)}
                                                  onChange={(newValue) => {
                                                    // Remove [Mapped] from the selected label and find the corresponding value
                                                    let cleanLabel = newValue;
                                                    if (typeof newValue === 'string') {
                                                      cleanLabel = newValue.replace(' [Mapped]', '');
                                                    }

                                                    const selectedOption = getItemDropdownOptionsWithIndicators(attribute, mainIndex, subIndex, itemIndex)
                                                      .find(opt => {
                                                        const optionLabel = typeof opt.label === 'string' ? opt.label.replace(' [Mapped]', '') : opt.label;
                                                        return optionLabel === cleanLabel;
                                                      });
                                                    handleItemTargetValueChange(mainIndex, subIndex, itemIndex, selectedOption ? selectedOption.value : newValue);
                                                  }}
                                                  placeholder="Select Target items"
                                                  displayKey="label"
                                                  valueKey="value"
                                                />
                                              </td>
                                              <td className={globalStyles.cell}>
                                                <div style={{ display: "flex", alignItems: "center" }}>
                                                  {itemIndex >= 0 && (
                                                    <button
                                                      onClick={() => deleteItem(mainIndex, subIndex, itemIndex)}
                                                      className={styles.deleteButton}
                                                    >
                                                      <img
                                                        src="/assets/minus-sign.png"
                                                        alt="Delete"
                                                        style={{ width: "16px", height: "16px" }}
                                                      />
                                                    </button>
                                                  )}
                                                </div>
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </div>
                                    ))}

                                    <div style={{ marginBottom: "20px", display: "flex" }}>
                                      <button
                                        onClick={() => addNewItem(mainIndex, subIndex)}
                                        className={styles.buttonStyle}
                                        style={{ marginLeft: "auto" }}
                                      >
                                        <span
                                          className={globalStyles.interStyle}
                                          style={{
                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            gap: "20px",
                                          }}
                                        >
                                          <HiPlus style={{ fontSize: "18px" }} /> Add item for mapping{" "}
                                        </span>
                                      </button>
                                    </div>
                                  </div>
                                )}
                              </React.Fragment>
                            ))}

                            <div style={{ marginBottom: "20px", display: "flex" }}>
                              <button
                                onClick={() => addNewSubCategory(mainIndex)}
                                className={styles.buttonStyle}
                                style={{ marginLeft: "auto" }}
                              >
                                <span
                                  className={globalStyles.interStyle}
                                  style={{
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    gap: "20px",
                                  }}
                                >
                                  <HiPlus style={{ fontSize: "18px" }} /> Add sub-category for mapping{" "}
                                </span>
                              </button>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>

          <div className={styles.dFlex} style={{ alignItems: "center", justifyContent: "center", marginTop: "20px" }}>
            <button className={styles.buttonStyle} onClick={addNewMapping} style={{ padding: "10px" }}>
              <span
                className={globalStyles.interStyle}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: "20px",
                }}
              >
                <HiPlus style={{ fontSize: "18px" }} />
                Add value for mapping
              </span>
            </button>
          </div>
        </div>
        <div className={styles.buttonContainer}>
          <button
            className={styles.combineButton}
            onClick={handleSave}
            disabled={isLoading}
          >
            {isLoading ? (
              <div className={globalStyles.loaderContainer}>
                <div className={globalStyles.loader}></div>
              </div>
            ) : (
              <>
                <img src="/assets/check list.png" alt="Checklist Icon" className={styles.buttonIcon} />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>

      {/* Smart Auto-Map Confirmation Dialog */}
      <Dialog open={showSmartAutoMapConfirm} onClose={() => setShowSmartAutoMapConfirm(false)}>
        <DialogContent
          style={{
            fontFamily: "Inter, sans-serif",
            fontSize: 16,
            color: "#222",
            padding: 32,
            lineHeight: 1.5,
          }}
        >
          <div
            style={{
              fontSize: 18,
              fontWeight: 600,
              marginBottom: 16,
              color: "#111",
            }}
          >
            Confirm Smart Auto-Map
          </div>
          <div style={{ marginBottom: 12 }}>
            Running Smart Auto-Map will <b>analyze and automatically match</b> source values to target values using
            advanced fuzzy matching algorithms.
          </div>
          <div
            style={{
              background: "#fef3c7",
              border: "1px solid #f59e0b",
              borderRadius: 6,
              padding: 12,
              fontSize: 14,
              color: "#92400e",
            }}
          >
            ⚠️ <b>Warning:</b> This will overwrite existing value mappings and may change currently mapped values.
          </div>
        </DialogContent>
        <DialogActions
          style={{
            fontFamily: "Inter, sans-serif",
            padding: "0 24px 18px 24px",
            gap: "8px",
          }}
        >
          <Button
            onClick={() => setShowSmartAutoMapConfirm(false)}
            style={{
              fontFamily: "Inter, sans-serif",
              color: "#6b7280",
              textTransform: "none",
              fontSize: 15,
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={executeSmartAutoMap}
            style={{
              fontFamily: "Inter, sans-serif",
              color: "#fff",
              background: "#EA5822",
              textTransform: "none",
              fontSize: 15,
              fontWeight: 600,
              padding: "8px 20px",
              borderRadius: 6,
            }}
          >
            Continue Auto-Map
          </Button>
        </DialogActions>
      </Dialog>

      {/* Clear All Mappings Confirmation Dialog */}
      <Dialog open={showClearAllConfirm} onClose={() => setShowClearAllConfirm(false)}>
        <DialogContent
          style={{
            fontFamily: "Inter, sans-serif",
            fontSize: 16,
            color: "#222",
            padding: 32,
            lineHeight: 1.5,
          }}
        >
          <div
            style={{
              fontSize: 18,
              fontWeight: 600,
              marginBottom: 16,
              color: "#111",
            }}
          >
            Confirm Clear All Mappings
          </div>
          <div style={{ marginBottom: 12 }}>
            This action will <b>permanently remove all current value mappings</b> and reset the mapping table to its initial state.
          </div>
          <div
            style={{
              background: "#fee2e2",
              border: "1px solid #ef4444",
              borderRadius: 6,
              padding: 12,
              fontSize: 14,
              color: "#b91c1c",
            }}
          >
            ⚠️ <b>Warning:</b> This action cannot be undone. All your current mapping work will be lost.
          </div>
        </DialogContent>
        <DialogActions
          style={{
            fontFamily: "Inter, sans-serif",
            padding: "0 24px 18px 24px",
            gap: "8px",
          }}
        >
          <Button
            onClick={() => setShowClearAllConfirm(false)}
            style={{
              fontFamily: "Inter, sans-serif",
              color: "#6b7280",
              textTransform: "none",
              fontSize: 15,
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={executeClearAllMappings}
            style={{
              fontFamily: "Inter, sans-serif",
              color: "#fff",
              background: "#DC2626",
              textTransform: "none",
              fontSize: 15,
              fontWeight: 600,
              padding: "8px 20px",
              borderRadius: 6,
            }}
          >
            Clear All Mappings
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  )
}
