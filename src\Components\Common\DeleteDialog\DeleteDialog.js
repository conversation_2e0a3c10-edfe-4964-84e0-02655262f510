import React, { useEffect } from 'react';
import styles from './DeleteDialog.module.css';

const DeleteDialog = ({ isOpen, onConfirm, onCancel, itemName, itemType = 'TEMPLATE NAME' }) => {
    useEffect(() => {
        const handleEscape = (event) => {
            if (event.key === 'Escape') {
                onCancel();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            // Prevent body scroll when dialog is open
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onCancel]);

    if (!isOpen) return null;

    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget) {
            onCancel();
        }
    };

    return (
        <div className={styles.overlay} onClick={handleOverlayClick}>
            <div className={styles.dialog}>
                <div className={styles.header}>
                    <h2 className={styles.title}>
                        DO YOU WANT TO DELETE {itemType.toUpperCase()} #{itemName || '001'}
                    </h2>
                    <button className={styles.closeButton} onClick={onCancel}>
                        ✕
                    </button>
                </div>
                
                <div className={styles.content}>
                    <p className={styles.warningText}>Deleting this {itemType.toLowerCase()} is permanent.</p>
                    <p className={styles.descriptionText}>
                        {itemType === 'CONNECTION' 
                            ? 'All associated mappings, API keys, and configuration details will be removed and cannot be recovered. Do you want to proceed?'
                            : 'All associated mappings, API keys, and configuration details will be removed and cannot be recovered. Do you want to proceed?'
                        }
                    </p>
                </div>
                
                <button className={styles.deleteButton} onClick={onConfirm}>
                    Delete permanently
                </button>
            </div>
        </div>
    );
};

export default DeleteDialog;
