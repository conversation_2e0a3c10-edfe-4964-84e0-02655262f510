/* Dialog styles */
.dialogOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialogContent {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 800px;
    height: 80vh;
    max-height: 80vh;
    min-height: 600px;
    display: flex;
    flex-direction: column;
}

.dialogHeader {
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dialogHeader h3 {
    margin: 0;
    font-weight: 500;
    color: #333;
}

.closeDialogIcon {
    cursor: pointer;
    font-size: 20px;
    color: #666;
}

.dialogTabs {
    display: flex;
    border-bottom: 1px solid #eee;
}

.tabButton {
    padding: 12px 24px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.activeTab {
    border-bottom-color: #EF8963;
    color: #EF8963;
}

.dialogBody {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

.statusLine {
    font-weight: 500;
    margin-bottom: 16px;
    font-size: 16px;
}

.successCode {
    color: #4CAF50;
    font-weight: bold;
}

.errorCode {
    color: #F44336;
    font-weight: bold;
}

.codeDisplay {
    background-color: #272822;
    color: #f8f8f2;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    border: 1px solid #3c3c3c;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    margin: 8px 0;
}

/* Enhanced JSON display styling */
.codeDisplay::selection {
    background-color: #49483e;
}

.codeDisplay::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.codeDisplay::-webkit-scrollbar-track {
    background-color: #1e1e1e;
    border-radius: 4px;
}

.codeDisplay::-webkit-scrollbar-thumb {
    background-color: #555;
    border-radius: 4px;
}

.codeDisplay::-webkit-scrollbar-thumb:hover {
    background-color: #777;
}

/* Improve readability for different screen sizes */
@media (max-width: 768px) {
    .codeDisplay {
        font-size: 12px;
        padding: 16px;
    }
}

.dialogFooter {
    padding: 16px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
}

.closeButton {
    padding: 8px 16px;
    background-color: #EF8963;
    color: #170903;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    line-height: 24px; /* 150% */
}

.sourceSection {
    margin-bottom: 16px;
}

.sourceSection h4 {
    margin-bottom: 8px;
}

/* Loading and error states */
.loadingMessage {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.loadingMessage span {
    margin-top: 16px;
    font-size: 16px;
    color: #666;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #EF8963;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.errorMessage {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: #F44336;
    font-size: 16px;
}

.noRecordsMessage {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: #666;
    font-size: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
    display: inline-block;
}

/* Add any additional styles that might be needed */
.dFlex {
    display: flex;
    align-items: center;
}
