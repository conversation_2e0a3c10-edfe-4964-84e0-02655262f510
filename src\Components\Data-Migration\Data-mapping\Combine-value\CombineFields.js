import React, {useEffect, useState} from "react";
import styles from "./CombineFields.module.css";
import globalStyles from "../../../globalStyles.module.css"
import { HiXMark } from "react-icons/hi2";
import Dropdown from "../../Dropdown/Dropdown";

export default function CombineFields({
  close,
  attribute,
  target,
  onSave,
  sourceFields
}) {
  const [selectedFields, setSelectedFields] = useState([]);

  const handleRemoveField = (field) => {
    setSelectedFields(selectedFields.filter(f => f !== field));
  };

  const handleSave = () => {
    onSave(selectedFields);
    close();
  };
    useEffect(() => {
        if (attribute && attribute?.combinedFields?.length > 0) {
            setSelectedFields(attribute.combinedFields);
        }
    }, [attribute]);

  return (
    <div className={styles.combineFieldsContainer}>
      <div className={styles.header}>
        <div className={styles.titleContainer}>
          <h2 className={styles.title}>Combine Fields</h2>
          <HiXMark className={styles.closeIcon} onClick={close} />
        </div>
        <div className={styles.mappingGuide}>
          <span className={styles.guideLabel}>MAPPING GUIDE</span>
          <span className={styles.guideText}>Combine multiple source fields to have one target data</span>
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.formRow}>
          <div className={styles.label}>Data type</div>
          <div className={styles.value}>{target?.name || "Tickets"}</div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.label}>Select source multiple fields</div>
          <div className={styles.fieldSelectionContainer}>
            <div className={styles.selectWithButton}>
              <Dropdown
                value={selectedFields}
                options={sourceFields?.filter(field => field && field !== '' && field !== 'undefined' && field !== 'null').map(field => String(field).trim()).filter(fieldName => fieldName && fieldName !== 'undefined' && fieldName !== 'null' && fieldName !== 'NaN')}
                onChange={setSelectedFields}
                placeholder="Select Source fields"
                multiSelect={true}
                showSelectAll={true}
              />
            </div>
          </div>
        </div>

        {selectedFields.length > 0 && (
          <div className={styles.selectedFieldsContainer}>
            {selectedFields.map((field, index) => (
              <div key={index} className={`${styles.selectedField} ${globalStyles.interStyle}`}>
                <span>{field || 'Unknown Field'}</span>
                <HiXMark
                  className={styles.removeFieldIcon}
                  onClick={() => handleRemoveField(field)}
                />
              </div>
            ))}
          </div>
        )}

        <div className={styles.buttonContainer}>
            <button
                className={styles.combineButton}
                onClick={handleSave}

            >
            <img
                src="/assets/check list.png"
                alt="Checklist Icon"
                className={styles.buttonIcon}
            />
           Save Changes
            </button>
        </div>
      </div>
    </div>
  );
}
