.overrideContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #f5f5f5;

    overflow: hidden;
}

.headerContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.headerTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #170903;
}

.headerTitle h2 {
    color: #EA5822;
    font-family: Poppins, sans-serif;
    font-size: 27px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 88.889% */
    margin: 0;
}


.closeIcon {
    color: white;
    font-size: 24px;
    cursor: pointer;
}

.mappingGuide {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background-color: #F6F4F2;
    border-bottom: 1px solid #E6E6E6;
}

.guideLabel {
    color: #EA5822;
    font-family: Poppins, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 171.429% */
    margin-right: 15px;
}


.guideText {
    color: #170903;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
}


.contentContainer {
    padding: 20px;
    background-color: white;
}

.fieldInfoRow {
    /*display: flex;*/
    margin-bottom: 20px;
}

.fieldInfoItem {
    width: 50%;
    display: flex;
    /*flex-direction: row;*/
}


.actionButtons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.addButton {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    background-color: white;
    border: 1px solid #E6E6E6;
    border-radius: 4px;
    color: #746B68;
    font-weight: 500;
    cursor: pointer;
    width: fit-content;
}

.addButton span {
    margin-right: 5px;
}

.overrideButton {
    padding: 10px 15px;
    background-color: white;
    border: 1px solid #E6E6E6;
    border-radius: 4px;
    color: #746B68;
    font-weight: 500;
    cursor: pointer;
    width: fit-content;
    display: flex;
    align-items: center;
}

.checkIcon {
    height: 12px;
    width: 12px;
    color: white;
}

.deleteIcon {
    height: 16px;
    width: 16px;
}

.deleteButton {
    display: flex;
    justify-content: center;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}
