import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { ChevronLeftIcon } from "@heroicons/react/solid"
import Header from "../Header/Header"
import styles from "./Forgot-password.module.css"
import globalStyles from "../globalStyles.module.css"
import config from '../../Config/config.json';

export default function ForgotPassword() {
  const [formSubmitted, setFormSubmitted] = useState(false)
  const [formTwoSubmitted, setFormTwoSubmitted] = useState(false)
  const [initialForgotPassword, setInitialForgotPassword] = useState(true)
  const [email, setEmail] = useState("")
  const [code, setCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const trustedLogos = config.TRUSTED_BY_LOGOS
  const navigate = useNavigate()

  const signInClick = () => {
    navigate("/")
  }

  const backToForgotPassword = () => {
    setInitialForgotPassword(true)
  }

  const handleSignIn = (e) => {
    e.preventDefault()
    setFormSubmitted(true)
    if (email && /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email)) {
      setIsLoading(true)
      // Simulate API call
      setTimeout(() => {
        setIsLoading(false)
        setInitialForgotPassword(false)
      }, 1000)
    }
  }

  const handleCode = (e) => {
    e.preventDefault()
    setFormTwoSubmitted(true)
    if (code) {
      setIsLoading(true)
      // Simulate API call
      setTimeout(() => {
        setIsLoading(false)
        navigate("/resetpassword", { state: { email: email } })
      }, 1000)
    }
  }

  return (
    <div className="d-flex flex-column">
      <div>
        <Header />
      </div>

      <div className={styles.mainContainer}>
        <div className={styles.signInContainer}>
          <div className={styles.leftContent}>
            <div className={globalStyles.headerStyle}>
              <div style={{ fontSize: "45px" }}>Effortless Data Migration</div>
              <div style={{ fontSize: "45px" }}>Automated!</div>
            </div>
            <div className={globalStyles.interSummaryStyle} style={{ padding: "20px" }}>
              With <b>migrateGenie</b>, move your data between platforms with confidence. Automate mapping, track
              progress and ensure secure transactions - no headaches.
            </div>
            <img src="/assets/Login Animation.gif" alt="Data migration illustration" className={styles.loginImage} />
          </div>

          <div className={styles.rightSide}>
            {initialForgotPassword ? (
              <div className={styles.container}>
                <div className={styles.buttonContainer}>
                  <button className="no-color-button" onClick={signInClick}>
                    Back to sign-in
                  </button>
                </div>
                <div className="form-container" style={{ paddingTop: "0px" }}>
                  <p className={styles.description}>Enter your details</p>
                  <form onSubmit={handleSignIn}>
                    <div className="form-group-new">
                      <input
                        className={`form-control ${formSubmitted && (!email || !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email))
                          ? "input-error"
                          : ""
                          }`}
                        style={{ width: "100%", padding: "10px" }}
                        value={email}
                        onChange={(e) => {
                          setEmail(e.target.value)
                          setFormSubmitted(false)
                        }}
                        id="exampleInputEmail1"
                        aria-describedby="emailHelp"
                        placeholder="Your registered email"
                      />
                      {formSubmitted && !email && <p className="error-message">Email is required</p>}
                      {formSubmitted && email && !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email) && (
                        <p className="error-message">Please enter a valid email address</p>
                      )}
                    </div>

                    <button className="main-button" type="submit">
                      {isLoading ? (
                        <div className={globalStyles.loaderContainer}>
                          <div className={globalStyles.loader}></div>
                        </div>
                      ) : (
                        "Send recovery code"
                      )}
                    </button>
                  </form>
                </div>
                <div className={styles.buttonContainerNew}>
                  <button className="no-color-button" style={{ marginBottom: "15px", width: "100%" }}>
                    <img src="/assets/img.png" alt="Google Icon" className={styles.buttonIcon} />
                    Sign-in with Google
                  </button>
                  <button className="no-color-button">
                    <img src="/assets/img_1.png" alt="Microsoft Icon" className={styles.buttonIcon} />
                    Sign-in with Microsoft
                  </button>
                </div>
              </div>
            ) : (
              <div className={styles.container}>
                <div className={styles.backButtonContainer}>
                  <button className={styles.backButton} onClick={backToForgotPassword}>
                    <ChevronLeftIcon className={styles.backIcon} />
                  </button>
                </div>
                <div className="form-container" style={{ paddingTop: "0px" }}>
                  <p className={styles.description}>Your details</p>
                  <form onSubmit={handleCode}>
                    <div className="form-group-new">
                      <input
                        type="email"
                        className="form-control"
                        style={{ width: "100%", padding: "10px" }}
                        id="exampleInputEmail1"
                        value={email}
                        aria-describedby="emailHelp"
                        placeholder="Your registered email"
                        disabled
                      />
                    </div>
                    <div className={styles.recoveryCodeStatus}>Recovery code sent!</div>
                    <div className="form-group-new">
                      <input
                        type="text"
                        className={`form-control ${formTwoSubmitted && !code ? "input-error" : ""}`}
                        style={{ width: "100%", padding: "10px" }}
                        id="exampleInputCode"
                        placeholder="Code"
                        value={code}
                        onChange={(e) => {
                          setCode(e.target.value)
                          setFormTwoSubmitted(false)
                        }}
                      />
                      {formTwoSubmitted && !code && <p className="error-message">Code cannot be empty</p>}
                    </div>
                    <button className="main-button" type="submit">
                      {isLoading ? (
                        <div className={globalStyles.loaderContainer}>
                          <div className={globalStyles.loader}></div>
                        </div>
                      ) : (
                        "Submit"
                      )}
                    </button>
                  </form>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className={styles.featuresSection}>
          <div className={`${globalStyles.bottomContainer} ${globalStyles.poppinsStyle}`} style={{ fontWeight: "600" }}>
            <div className={styles.featureItem}>
              <div className={globalStyles.imageContainer}>
                <img src="/assets/nano.png" alt="Smart auto-mapping" className={globalStyles.imageBottom} />
              </div>
              <span>Smart auto-mapping</span>
            </div>
            <div className={styles.featureItem}>
              <div className={globalStyles.imageContainer}>
                <img src="/assets/lock.png" alt="Secure with encryption" className={globalStyles.imageBottom} />
              </div>
              <span>Secure with encryption</span>
            </div>
            <div className={styles.featureItem}>
              <div className={globalStyles.imageContainer}>
                <img src="/assets/lightstrike.png" alt="No-code configurations" className={globalStyles.imageBottom} />
              </div>
              <span>No-code configurations</span>
            </div>
            <div className={styles.featureItem}>
              <div className={globalStyles.imageContainer}>
                <img src="/assets/analytics.png" alt="Migration reports" className={globalStyles.imageBottom} />
              </div>
              <span>Migration reports</span>
            </div>
          </div>

          <div className={styles.trustedBySection}>
            <hr className={globalStyles.signupHr} />
            <div className={styles.trustedByContainer}>
              <div className={styles.trustedByTitle}>Trusted by</div>
              <div className={globalStyles.trustedLogos}>
                {trustedLogos.map((logo, idx) =>
                  logo.url ? (
                    <a
                      key={logo.alt}
                      href={logo.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={styles.trustedLogo}
                      style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                    >
                      <img
                        src={logo.src}
                        alt={logo.alt}
                        style={{ width: 60, height: 50, objectFit: "contain" }}
                      />
                    </a>
                  ) : (                    <div
                      key={logo.alt}
                      className={styles.trustedLogo}
                      style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                    >
                      <img
                        src={logo.src}
                        alt={logo.alt}
                        style={{ width: 100, height: 80, objectFit: "contain" }}
                      />
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
