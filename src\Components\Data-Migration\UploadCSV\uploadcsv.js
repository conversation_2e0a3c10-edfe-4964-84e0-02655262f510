"use client"

import { useState, useEffect } from "react"
import styles from "./uploadcsv.module.css"
import globalStyles from "../../globalStyles.module.css"
import { HiXMark, HiInformationCircle, HiDocument } from "react-icons/hi2"
import { <PERSON><PERSON><PERSON> } from "buffer"
import { uploadToAzureBlob } from "../../apiService"

// Ensure Buffer is available globally
if (!window.Buffer) {
  window.Buffer = Buffer
}

// Helper function to properly parse CSV content
const parseCSVContent = (content) => {
  try {
    if (!content || content.trim() === "") {
      return { rows: [], headers: [] }
    }

    const lines = content.split(/\r?\n/)
    const rows = []
    let inQuotes = false
    let currentRow = []
    let currentField = ""

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex]
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i]
        const nextChar = line[i + 1]

        if (char === '"') {
          if (inQuotes && nextChar === '"') {
            // <PERSON>le escaped quotes
            currentField += '"'
            i++ // Skip next quote
          } else {
            // Toggle quote state
            inQuotes = !inQuotes
          }
        } else if (char === ',' && !inQuotes) {
          // End of field
          currentRow.push(currentField.trim())
          currentField = ""
        } else {
          currentField += char
        }
      }

      // End of line
      if (!inQuotes) {
        // Complete the current field and row
        currentRow.push(currentField.trim())
        
        // Only add row if it has meaningful content (not all empty fields)
        const hasContent = currentRow.some(field => field !== "" && field !== null && field !== undefined)
        if (hasContent) {
          rows.push([...currentRow])
        }
        
        currentRow = []
        currentField = ""
      } else {
        // Multi-line field, add newline character
        currentField += "\n"
      }
    }

    // Handle case where file doesn't end with newline
    if (currentRow.length > 0 || currentField !== "") {
      currentRow.push(currentField.trim())
      const hasContent = currentRow.some(field => field !== "" && field !== null && field !== undefined)
      if (hasContent) {
        rows.push(currentRow)
      }
    }

    const headers = rows.length > 0 ? rows[0].map(h => h.trim()) : []
    const dataRows = rows.slice(1)

    // Filter out completely empty data rows
    const validDataRows = dataRows.filter(row => 
      row.some(field => field && field.trim() !== "")
    )

    return { rows: validDataRows, headers }
  } catch (error) {
    console.error("Error parsing CSV content:", error)
    return { rows: [], headers: [] }
  }
}

const parseCSVHeaders = (file) => {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject("No file provided")
      return
    }

    const reader = new FileReader()

    reader.onload = (event) => {
      try {
        const content = event.target.result
        if (!content || content.trim() === "") {
          resolve({ headers: [], numberOfLines: 0 })
          return
        }

        // Use the robust CSV parser
        const { rows, headers } = parseCSVContent(content)
        const numberOfLines = rows.length

        // Optional: Remove these console.log statements in production
        // console.log(`CSV Parsing Debug - Headers: ${headers.length}, Data rows: ${numberOfLines}`)
        // console.log(`Headers:`, headers)
        // console.log(`Sample of first few rows:`, rows.slice(0, 3))

        resolve({ headers, numberOfLines })
      } catch (error) {
        reject(`Error parsing CSV headers: ${error.message}`)
      }
    }

    reader.onerror = (error) => {
      reject(`Error reading file: ${error}`)
    }

    reader.readAsText(file)
  })
}

const UploadCSV = ({
  onFileUploaded,
  onClose,
  entityName = "CSV",
  isLastEntity = true,
  previouslyUploadedFiles = [],
  onRecordCountUpdate, // New prop to update parent with record count
}) => {
  const [file, setFile] = useState(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadStatus, setUploadStatus] = useState("idle")
  const [isUploading, setIsUploading] = useState(false)
  const [uploadError, setUploadError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)
  const [recordCount, setRecordCount] = useState(0)

  // Sample CSV file URLs for different entities
  const sampleCSVUrls = {
    Tickets:
      "https://saasgeniestorage.blob.core.windows.net/migrategenie-v2-storage/Sample%20CSVs/Tickets%20Sample.csv",
    Conversations:
      "https://saasgeniestorage.blob.core.windows.net/migrategenie-v2-storage/Sample%20CSVs/Conversations%20Sample.csv",
  }

  // Show progress animation when uploading
  useEffect(() => {
    if (uploadStatus === "uploading") {
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval)
            setUploadStatus("uploaded")
            return 100
          }
          return prev + 10
        })
      }, 130)
      return () => clearInterval(interval)
    }
  }, [uploadStatus])

  // Reset error when file changes
  useEffect(() => {
    if (file) {
      setUploadError(null)
    }
  }, [file])

  // Update parent component with record count when it changes
  useEffect(() => {
    if (recordCount > 0 && onRecordCountUpdate) {
      onRecordCountUpdate(entityName, recordCount)
    }
  }, [recordCount, entityName, onRecordCountUpdate])

  // Function to handle downloading sample CSV files
  const handleDownloadSampleCSV = (entity) => {
    try {
      // Get the URL for the entity, or use a default message if not found
      const sampleUrl = sampleCSVUrls[entity]

      if (sampleUrl) {
        // Create a temporary link element
        const link = document.createElement("a")
        link.href = sampleUrl
        link.target = "_blank"
        link.download = `${entity} Sample.csv`

        // Append to the document, click it, and remove it
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Clear any existing errors and show a temporary success message
        setUploadError(null)
        setSuccessMessage(`Sample CSV for ${entity} is being downloaded`)

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(null), 3000)
      } else {
        // If no sample CSV is available for this entity, show an error
        setUploadError(`No sample CSV available for ${entity}. Please contact support.`)
        setTimeout(() => setUploadError(null), 5000) // Clear error after 5 seconds
      }
    } catch (error) {
      console.error("Error downloading sample CSV:", error)
      setUploadError(`Failed to download sample CSV. ${error.message}`)
      setTimeout(() => setUploadError(null), 5000) // Clear error after 5 seconds
    }
  }

  const handleFileChange = async (event) => {
    const selectedFile = event?.target?.files?.[0]
    if (selectedFile) {
      // Validate file type
      if (!selectedFile.name.toLowerCase().endsWith(".csv")) {
        setUploadError("Please select a CSV file")
        return
      }

      // Validate file size (500MB max)
      if (selectedFile.size > 500 * 1024 * 1024) {
        setUploadError("File size exceeds 500MB limit")
        return
      }

      setFile(selectedFile)
      setUploadProgress(0)
      setUploadStatus("uploading")
      setRecordCount(0) // Reset record count when new file is selected

      try {        // Parse CSV headers and get record count immediately
        const { headers, numberOfLines } = await parseCSVHeaders(selectedFile)

        // Validate record count
        if (numberOfLines > 10000000) {
          setUploadError("File contains more than 10000000 records. Please reduce the number of records.")
          setUploadStatus("error")
          return
        }

        setRecordCount(numberOfLines)
      } catch (error) {
        console.error("Error reading file:", error)
        setUploadError(error.message || "Failed to read file")
        setUploadStatus("error")
      }
    }
  }

  const handleDrop = async (event) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      // Validate file type
      if (!droppedFile.name.toLowerCase().endsWith(".csv")) {
        setUploadError("Please select a CSV file")
        return
      }

      // Validate file size (500MB max)
      if (droppedFile.size > 500 * 1024 * 1024) {
        setUploadError("File size exceeds 500MB limit")
        return
      }

      setFile(droppedFile)
      setUploadProgress(0)
      setUploadStatus("uploading")

      try {        // Parse CSV headers and get record count immediately
        const { headers, numberOfLines } = await parseCSVHeaders(droppedFile)

        // Validate record count
        if (numberOfLines > 10000000) {
          setUploadError("File contains more than 10000000 records. Please reduce the number of records.")
          setUploadStatus("error")
          return
        }

        setRecordCount(numberOfLines)
      } catch (error) {
        console.error("Error reading file:", error)
        setUploadError(error.message || "Failed to read file")
        setUploadStatus("error")
      }
    }
  }

  const handleConfirm = async () => {
    if (!file) {
      setUploadError("Please select a file to upload")
      return
    }

    setIsUploading(true)
    setUploadError(null)

    try {      // Parse the file again to ensure we have the correct count
      const { headers, numberOfLines } = await parseCSVHeaders(file)

      if (numberOfLines > 10000000) {
        throw new Error("File contains more than 10000000 records. Please reduce the number of records.")
      }

      const response = await uploadToAzureBlob(file)

      if (response) {
        const fileData = {
          name: response.blobName || file.name,
          type: file.type,
          size: `${Math.round(file.size / 1024)}KB`,
          file: file,
          azureBlobUrl: response.azureBlobURL,
          headers: headers,
          length: numberOfLines,
          recordCount: numberOfLines, // Add explicit recordCount field
          uploadedAt: new Date().toISOString(),
        }

        setUploadStatus("uploaded")
        setUploadProgress(100)

        if (onFileUploaded) {
          onFileUploaded(fileData)
        }
      }
    } catch (error) {
      console.error("Error processing file:", error)
      setUploadStatus("error")
      setUploadError(error.message || "Failed to upload file. Please try again.")
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className={styles.modal}>
      <div className={styles.container}>
        <button className={styles.closeButton} onClick={onClose}>
          <HiXMark className={globalStyles.closeIcon} />
        </button>
        <h2 className={styles.title}>
          Upload <span style={{ fontWeight: "bold" }}>{entityName}</span> file
        </h2>

        {previouslyUploadedFiles && previouslyUploadedFiles.length > 0 && (
          <div className={styles.previousFilesContainer}>
            <div className={styles.previousFilesHeader}>Previously uploaded file(s) for {entityName}:</div>
            <ul className={styles.filesList}>
              {previouslyUploadedFiles.map((file, index) => (
                <li key={index} className={styles.fileItem}>
                  <HiDocument style={{ marginRight: "5px" }} />
                  {file.name} ({file.size})
                  {file.recordCount && (
                    <span style={{ marginLeft: "10px", color: "#1976D2", fontWeight: "500" }}>
                      - {file.recordCount.toLocaleString()} records
                    </span>
                  )}
                </li>
              ))}
            </ul>
            <div className={styles.replaceWarning}>Uploading a new file will replace the existing one.</div>
          </div>
        )}

        <p className={styles.subtitle}>
          Ensure your file follows the required format for seamless integration and automated mapping.
        </p>
        <div className={styles.buttonRow}>
          <label className={styles.browseButton}>
            <img src="/assets/file-search.png" alt="File Search Icon" className={styles.browseIcon} />
            <span style={{ fontFamily: "Inter" }}>Browse file</span>
            <input type="file" accept=".csv" onChange={handleFileChange} style={{ display: "none" }} />
          </label>
          <button className={styles.downloadButton} onClick={() => handleDownloadSampleCSV(entityName)}>
            Download sample CSV
          </button>
        </div>
        <div
          className={`${styles.dropArea} ${uploadError ? styles.dropAreaError : ""}`}
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
        >
          {!file && (
            <div className={styles.uploadContainer}>
              <img
                src="/assets/file-upload.png"
                alt="Upload Icon"
                className={styles.uploadIcon}
                onClick={() => document.querySelector("input[type=file]").click()}
              />
              <p className={styles.dragText}>Drag and drop your file</p>
            </div>
          )}
          <p className={styles.fileFormat}>
            Accepted format: <strong>your-file.csv</strong> | Max file size: <strong>500MB</strong>
          </p>
          {file && (
            <div className={styles.progressContainer}>
              <div className={styles.progressBar}>
                <div
                  className={`${styles.progress} ${uploadStatus === "error" ? styles.progressError : ""}`}
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className={styles.uploadingText}>
                {uploadStatus === "uploading"
                  ? "Processing..."
                  : uploadStatus === "error"
                    ? "Error processing file"
                    : "Processed"}
              </p>
              {recordCount !== undefined && recordCount >= 0 && (
                <p
                  className={styles.recordCount}
                  style={{
                    marginTop: "15px",
                    fontSize: "12px",
                    color: "#f8f8f7",
                    fontWeight: "500",
                    textAlign: "center",
                    fontFamily: "Inter"
                  }}
                >
                  Data records found: <strong style={{ color: "#f8f8f7" }}>{recordCount?.toLocaleString() || "0"}</strong>
                  <br />
                  <span style={{ fontSize: "10px", color: "#ccc" }}>
                    (Excludes header row and empty lines)
                  </span>
                </p>
              )}
            </div>
          )}

          {/* Display error message if there is one */}
          {uploadError && (
            <div className={styles.errorMessage}>
              <HiInformationCircle style={{ marginRight: "5px" }} />
              {uploadError}
            </div>
          )}

          {/* Display success message if there is one */}
          {successMessage && (
            <div className={styles.successMessage}>
              <HiInformationCircle style={{ marginRight: "5px", color: "#4CAF50" }} />
              {successMessage}
            </div>
          )}
        </div>
        <div className={styles.buttonContainer}>
          <button
            style={{ width: "100%" }}
            className={globalStyles.mainButton}
            disabled={!file || isUploading || uploadStatus === "error"}
            onClick={handleConfirm}
          >
            {isUploading ? "Uploading..." : isLastEntity ? "Confirm" : "Next"}
          </button>
        </div>
      </div>
    </div>
  )
}

export default UploadCSV
