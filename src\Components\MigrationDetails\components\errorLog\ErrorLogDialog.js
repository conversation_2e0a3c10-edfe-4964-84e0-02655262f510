import { useState, useEffect } from 'react';
import { HiXMark } from 'react-icons/hi2';
import styles from './ErrorLogDialog.module.css';
import globalStyles from '../../../globalStyles.module.css';

const ErrorLogDialog = ({
    isOpen,
    selectedPayload,
    errorLogData,
    onClose,
    isLoading = false,
    error = null
}) => {
    const [activeTab, setActiveTab] = useState("info");

    // Calculate tab availability
    const hasErrorLogData = errorLogData && (
        (typeof errorLogData === 'object' && Object.keys(errorLogData).length > 0) ||
        (typeof errorLogData === 'string' && errorLogData.trim() !== '')
    );

    const hasResponseData = selectedPayload?.additionalDetails?.response;
    const hasInfoData = selectedPayload && Object.keys(selectedPayload).length > 0;

    // Early returns after all hooks
    if (isOpen && (!selectedPayload || Object.keys(selectedPayload).length === 0)) {
        return (
            <div className={styles.dialogOverlay}>
                <div className={styles.dialogContent}>
                    <div className={styles.dialogHeader}>
                        <span className={globalStyles.interSummaryStyle}>Error Log Details</span>
                        <HiXMark className={styles.closeDialogIcon} onClick={onClose} />
                    </div>
                    <div className={styles.dialogBody}>
                        {isLoading ? (
                            <div className={styles.loadingMessage}>
                                <div className={styles.spinner}></div>
                                <span>Loading error log data...</span>
                            </div>
                        ) : error ? (
                            <div className={styles.errorMessage}>
                                <span>Error: {error}</span>
                            </div>
                        ) : (
                            <div className={styles.noRecordsMessage}>No records fetched</div>
                        )}
                    </div>
                    <div className={styles.dialogFooter}>
                        <button
                            className={styles.closeButton}
                            onClick={onClose}
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (!isOpen) return null;

    const getDialogTitle = () => {
        return selectedPayload.type || "Error Log Details";
    }; const formatJsonForDisplay = (jsonData) => {
        if (!jsonData) return "No data available";
        try {
            if (typeof jsonData === 'string') {
                try {
                    const parsedJson = JSON.parse(jsonData);
                    return JSON.stringify(parsedJson, null, 4);
                } catch {
                    return jsonData;
                }
            } else {
                return JSON.stringify(jsonData, null, 4);
            }
        } catch (error) {
            console.error("Error formatting JSON:", error);
            return "Error formatting JSON data";
        }
    }; const getResponseBody = () => {
        // Check for new errorLogData structure first (now as parsed objects)
        if (errorLogData?.errorResponse) {
            return formatJsonForDisplay(errorLogData.errorResponse);
        }

        // Check for theErrorObject.context.response (now as parsed object)
        if (errorLogData?.theErrorObject?.context?.response) {
            return formatJsonForDisplay(errorLogData.theErrorObject.context.response);
        }

        // Fallback to existing payload structure
        if (!selectedPayload.additionalDetails || !selectedPayload.additionalDetails.response) {
            return "No response data available";
        }

        const responseBody = selectedPayload.additionalDetails.response.body;
        return formatJsonForDisplay(responseBody);
    };

    const getInfoData = () => {
        if (!selectedPayload) return "No info data available";

        const infoData = {
            id: selectedPayload.id,
            type: selectedPayload.type,
            status: selectedPayload.status,
            sourceId: selectedPayload.sourceId,
            ...(selectedPayload.timestamp && { timestamp: selectedPayload.timestamp }),
            ...(selectedPayload.createdAt && { createdAt: selectedPayload.createdAt }),
            ...(selectedPayload.updatedAt && { updatedAt: selectedPayload.updatedAt })
        };

        return formatJsonForDisplay(infoData);
    }; const getBodyData = () => {
        // Check for new errorLogData structure first (now as parsed object)
        if (errorLogData?.errorBody) {
            return formatJsonForDisplay(errorLogData.errorBody);
        }

        // Check for theErrorObject.context.request_body (now as parsed object)
        if (errorLogData?.theErrorObject?.context?.request_body) {
            return formatJsonForDisplay(errorLogData.theErrorObject.context.request_body);
        }

        // Fallback to existing payload structure
        if (selectedPayload?.additionalDetails?.response?.body) {
            return formatJsonForDisplay(selectedPayload.additionalDetails.response.body);
        }
        return "No body data available";
    };

    return (
        <div className={styles.dialogOverlay}>
            <div className={styles.dialogContent}>
                <div className={styles.dialogHeader}>
                    <span className={globalStyles.interSummaryStyle}>{getDialogTitle()}</span>
                    <HiXMark className={styles.closeDialogIcon} onClick={onClose} />
                </div>                <div className={styles.dialogTabs}>
                    <button
                        className={`${styles.tabButton} ${activeTab === "info" ? styles.activeTab : ""}`}
                        onClick={() => setActiveTab("info")}
                    >
                        INFO
                    </button>
                    <button
                        className={`${styles.tabButton} ${activeTab === "body" ? styles.activeTab : ""}`}
                        onClick={() => setActiveTab("body")}
                    >
                        REQUEST
                    </button>
                    <button
                        className={`${styles.tabButton} ${activeTab === "response" ? styles.activeTab : ""}`}
                        onClick={() => setActiveTab("response")}
                    >
                        RESPONSE
                    </button>
                </div>

                <div className={styles.dialogBody}>
                    {isLoading ? (
                        <div className={styles.loadingMessage}>
                            <div className={styles.spinner}></div>
                            <span>Loading error log data...</span>
                        </div>
                    ) : error ? (
                        <div className={styles.errorMessage}>
                            <span>Error: {error}</span>
                        </div>
                    ) : (<>
                        {activeTab === "info" && (
                            <pre className={styles.codeDisplay}>
                                {getInfoData()}
                            </pre>
                        )}                            {activeTab === "response" && (
                            <pre className={styles.codeDisplay}>
                                {getResponseBody()}
                            </pre>
                        )}{activeTab === "body" && (
                            <pre className={styles.codeDisplay}>
                                {getBodyData()}
                            </pre>
                        )}
                    </>
                    )}
                </div>

                <div className={styles.dialogFooter}>
                    <button
                        className={styles.closeButton}
                        onClick={onClose}
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ErrorLogDialog;
