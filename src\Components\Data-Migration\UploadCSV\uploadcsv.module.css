.modal {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
  }

  .container {
    background: #170903;
    padding: 20px;
    border-radius: 10px;
    width: 815px;
    text-align: center;
    color: white;
    position: relative;
  }

  .closeButton {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 20px;
    color: white;
    cursor: pointer;
  }

  .title {
    color: #ea5822;
    font-family: Poppins, sans-serif;
    font-size: 27px;
    font-weight: 600;
    text-align: left;
    margin-bottom: 5px;
  }

  .subtitle {
    color: #f8f8f7;
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 15px;
    text-align: left;
  }

  .buttonRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
  }

  .browseButton {
    display: flex;
    padding: 5px 15px;
    justify-content: center;
    align-items: center;
    gap: 5px;
    border-radius: 3px;
    border: 1px solid #dcdad9;
    background: #fff;
    cursor: pointer;
    color: #3a2a1f;
  }

  .browseIcon {
    width: 16px;
    height: 16px;
  }

  .downloadButton {
    background: #170903;
    border: 1px solid #746b68;
    color: #97908e;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  }

  .dropArea {
    border: 2px dashed #97908e;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
  }

  .uploadContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .uploadIcon {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }

  .dragText {
    color: #f8f8f7;
    text-align: center;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }

  .fileFormat {
    color: #97908e;
    font-family: Inter, sans-serif;
    font-size: 12px;
    font-style: italic;
    font-weight: 400;
    line-height: sans-serif;
    font-size: 12px;
    font-style: italic;
    font-weight: 400;
    line-height: 24px;
    margin-top: 5px;
  }

  .progressContainer {
    margin-top: 15px;
  }

  .progressBar {
    width: 100%;
    height: 10px;
    background: #746b68;
    border-radius: 5px;
    overflow: hidden;
    margin-top: 10px;
  }

  .progress {
    height: 100%;
    background: #f8f8f7;
    transition: width 0.2s ease-in-out;
  }

  .uploadingText {
    color: #f8f8f7;
    margin-top: 8px;
    font-family: "inter";
  }

  .buttonContainer {
    margin-top: 20px;
  }

  .uploadComplete {
    background: white;
    color: #170903;
  }

  .errorMessage {
    display: flex;
    align-items: center;
    color: #f44336;
    font-size: 14px;
    margin-top: 10px;
    padding: 8px;
    background-color: rgba(244, 67, 54, 0.1);
    border-radius: 4px;
    font-family: "Inter", sans-serif;
}

.successMessage {
    display: flex;
    align-items: center;
    color: #4CAF50;
    font-size: 14px;
    margin-top: 10px;
    padding: 8px;
    background-color: rgba(76, 175, 80, 0.1);
    border-radius: 4px;
    font-family: "Inter", sans-serif;
}

.dropAreaError {
    border-color: #f44336;
}

.recordCount {
    color: #f8f8f7;
    font-size: 12px;
    font-family: Inter, sans-serif;
    font-weight: 500;
    text-align: center;
    margin-top: 15px;
}

.previousFilesContainer {
    background: #23140a;
    border-radius: 8px;
    padding: 12px 18px;
    margin-bottom: 15px;
    text-align: left;
}

.previousFilesHeader {
    color: #f0602b;
    font-family: Poppins, sans-serif;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.filesList {
    list-style: none;
    padding: 0;
    margin: 0 0 8px 0;
}

.fileItem {
    display: flex;
    align-items: center;
    color: #f8f8f7;
    font-size: 14px;
    margin-bottom: 4px;
}

.replaceWarning {
    color: #e37b52;
    font-size: 12px;
    font-family: Inter, sans-serif;
    margin-top: 4px;
}

.progressError {
    background: #f44336 !important;
}

