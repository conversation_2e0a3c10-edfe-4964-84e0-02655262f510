.container {
  padding: 24px;
  max-width: 600px;
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dFlex {
  display: flex;
  width: 100%;
}

.section {
  /* flex: 1;
  width: 50%; */
  padding: 20px 0 20px 0;
  padding-right: 5px;
}

.targetGraphic {
  height: 500px;
  /* background-color: #f8f8f7; */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.sourceBlankImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.sourceContainer {
  width: 100%;
  /*margin-right: 20px;*/
}

.sourceHeaderContainer {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20px; /* Add space between header and button */
  height: 24px; /* Match line height to ensure proper alignment */
}

.sourceTitle {
  color: #ea5822;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px; /* Increased from 14px for better vertical alignment */
  text-align: left;
  margin: 0;
  white-space: nowrap;
  letter-spacing: -0.2px;
}

.sourceHelpContainer {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 24px;
  white-space: nowrap;
}

.sourceOptions {
  width: 100%;
}

.orText {
  color: #170903;
  text-align: center;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  margin: 0 15px;
}

.sourceInfo {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  align-self: stretch;
  width: 100%;
  margin-top: 5px;
}

.sourceInfo p {
  font-size: 12px;
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  color: #746b68;
  display: flex;
  align-items: center;
  gap: 5px;
  margin: 5px 0;
}

.helpIcon {
  width: 14px;
  height: 14px;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.modalContent,
.uploadPopup {
  background: #181818;
  color: white;
  padding: 20px;
  border-radius: 10px;
  width: 450px;
  text-align: center;
  position: relative;
  box-shadow: 0px 0px 10px rgba(255, 255, 255, 0.2);
  transform: scale(0.8);
  animation: scaleIn 0.2s forwards;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
  }

  to {
    transform: scale(1);
  }
}

.closeBtn {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 18px;
  color: white;
  cursor: pointer;
}

.modalTitle {
  color: #ea5822;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px;
}

.searchInput {
  width: 90%;
  padding: 8px;
  margin: 10px 0;
  border-radius: 5px;
  border: 1px solid gray;
  background-color: #252525;
  color: white;
}

.searchInput::placeholder {
  color: gray;
}

.sourceIcons {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
  flex-wrap: wrap;
}

.source {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.source img {
  width: 60px;
  height: 60px;
  background-color: #170903;
  border-radius: 10px;
  padding: 10px;
  transition: 0.3s ease-in-out;
}

.source:hover img {
  transform: scale(1.1);
  background-color: #dcdad9;
}

.uploadOptions {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  color: #f8f8f7;
  text-align: center;
  font-family: Inter, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

.option {
  text-align: center;
  cursor: pointer;
}

.option img {
  width: 50px;
  height: 50px;
  background-color: #252525;
  border-radius: 10px;
  padding: 10px;
  transition: 0.3s ease-in-out;
}

.option:hover img {
  transform: scale(1.1);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;
}

.formSection {
  margin-top: 24px;
}

.input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  margin-bottom: 16px;
}

.standardInput {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #DCDAD9;
  border-radius: 3px;
  font-family: "Inter";
  font-weight: 400;
  font-size: 16px;
}

.connectButton {
  display: flex;
  width: 100%;
  height: 40px;
  padding: 10px 25px;
  justify-content: center;
  align-items: center;
  gap: 5px;
  align-self: stretch;
  border-radius: 3px;
  background: #ef8963;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.connectButton span {
  color: #170903;
  text-align: center;
  font-family: Inter, sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}

.confirmButton {
  display: flex;
  width: 100%;
  max-width: 520px;
  height: 40px;
  padding: 10px 45px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 3px;
  background: #ef8963;
  border: none;
  color: #170903;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.confirmButton:hover{
    background-color: #ec9d7f;
}

.title {
  color: #ea5822;
  font-family: Poppins, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
}

.helpRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  align-self: stretch;
  color: #746b68;
  font-family: Poppins, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}

.helpText {
  color: #746b68;
  font-family: Inter, sans-serif;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

.successContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.successMessage {
  background-color: #180c02;
  color: #f3f3f2;
  text-align: center;
  font-family: Inter, sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  padding: 16px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkmark {
  color: #4caf50;
  margin-left: 8px;
  font-size: 20px;
  font-weight: bold;
}

.queryParamSection {
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  width: 100%;
}

.queryParamHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.helpCircle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #ccc;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-left: auto;
  cursor: help;
}

.helpTooltip {
  position: absolute;
  right: 0;
  top: 25px;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  display: none;
}

.helpCircle:hover+.helpTooltip {
  display: block;
}

.toggleContainer {
  display: flex;
  width: 100%;
}

.toggleButton {
  background-color: #ccc;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 16px;
}

.toggleButton.selected {
  background-color: #9c9c9c;
  color: white;
}

.queryText {
  color: #746b68;
  font-family: Poppins, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
}

.toggleSwitch {
  position: relative;
  width: 100px;
  height: 35px;
  flex-shrink: 0;
  background: #d3cdc9;
  border-radius: 20px;
  display: flex;
  align-items: center;
  padding: 5px;
  cursor: pointer;
  transition: background 0.3s ease-in-out;
}

.toggleButton {
  width: 30px;
  height: 30px;
  background: #746b68;
  border-radius: 50%;
  position: absolute;
  left: 5px;
  transition: all 0.3s ease-in-out;
}

.toggleSwitch.active {
  background: #bfb8b4;
}

.toggleSwitch.active .toggleButton {
  left: 65px;
}

.newConfirmButton {
  display: flex;
  height: 40px;
  padding: 10px 45px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  border-radius: 3px;
  background: #ef8963;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  color: #170903;
  text-align: center;
  font-family: Inter, sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}

.newConfirmButton:hover{
    background-color: #ec9d7f;
}

.selectedFileContainer {
  width: 100%;
  margin-top: 15px;
}

.inputContainer {
  width: 100%;
}

.loaderContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.loader {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.sourceDiagramContainer {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  transition: all 0.3s ease;
}

.sourceBlankImage {
  width: 100%;
  height: auto;
  display: block;
  object-fit: contain;
}

.selectedSourceLogoOverlay {
  position: absolute;
  left: 15%;
  top: 1%;
  width: 20%;
  height: auto;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  /*background-color: #ffffff;*/
  background-color: #fae8df;
  border-radius: 50%;

  padding: 4%;


}

.selectedSourceLogo {
  width: 85%;
  height: 85%;
  object-fit: contain;
  position: relative;
  z-index: 3;
  margin: 0 auto;
  border-radius: 50%;
}


@media (max-width: 768px) {
  .sourceDiagramContainer {
    max-width: 100%;
  }

  .selectedSourceLogoOverlay {
    width: 30%;
  }
}

@media (max-width: 480px) {
  .selectedSourceLogoOverlay {
    width: 34%;
    left: 6%;
    top: 6%;
  }
}

.sourceDiagramContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  /* important: aligns content to the top */
  height: 100%;
  /* ensures it stretches the available space */
}

.targetGraphic {
  height: 100%;
  /* optional: or set to a fixed height */
}

.selectedSourceLogoOverlay {
  position: absolute;
  top: 0;
}

.inputContainer {
  margin-top: 15px;
  width: 97%;
  position: relative;
}

.formControl {
  width: 100%;
  padding: 8px 12px;
  font-family: "Inter", sans-serif;
  font-weight: 400;
  font-size: 16px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}


.passwordInput {
  padding-bottom: 5px;
}

.eyeIconContainer {
  position: absolute;
  top: 40%;
  right: -20px;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 0;
  z-index: 2;
}

.eyeIcon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.eyeIcon:hover {
  color: #4b5563;
}
.black-toast {
  background-color: black !important;
  color: white !important;
}
