"use client"

import { useEffect, useState, useContext } from "react"
import styles from "./SourceSelect.module.css"
import SourceTargetSelector from "../Source-target-selector/Source-target-selector"
import { CheckIcon, MinusIcon } from "@heroicons/react/solid"
import globalStyles from "../../globalStyles.module.css"
import { Dialog, DialogContent, FormControlLabel, Switch } from "@mui/material"
import { filterOptions, getMigrationProviders, postActivity, updateConnDetails, validateSource } from "../../apiService"
import UploadCSV from "../UploadCSV/uploadcsv"
import { saveMigrationPlan } from "../../apiService"
import { useMongodb } from "../../mongodbService"
import { MigrationContext } from "../Data-Migration"
import { ToastContainer, toast } from "react-toastify"
import { EyeIcon, EyeOffIcon } from "@heroicons/react/solid"
import { displayArticle } from "../../../Helper/helper"
import LoaderSpinner from "../../loaderspinner"
import config from "../../../Config/config.json"

// Toast styles
const successToastStyle = { color: "#16A34A", fontWeight: 500 }
const errorToastStyle = { color: "#DC2626", fontWeight: 500 }

const SourceSelect = ({
  setSelectedTab,
  templateName,
  setPlanId,
  planId,
  setMigrationSource,
  migrationSource,
  migrationTarget,
}) => {
  const { watchMigrations } = useMongodb()
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const sourceObjData = migrationState.sourceObjData || {}

  const [email, setEmail] = useState(localStorage.getItem("email"))
  const [showModal, setShowModal] = useState(false)
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [selectedSource, setSelectedSource] = useState(null)
  const [formData, setFormData] = useState({})
  const [queryFormData, setQueryFormData] = useState({})
  const [queryParamData, setQueryParamData] = useState([])
  const [connectionStatus, setConnectionStatus] = useState(null)
  const [checked, setChecked] = useState(false)
  const [openDialog, setOpenDialog] = useState(false)
  const [sourceData, setSourceData] = useState([])
  const [selectedFile, setSelectedFile] = useState(null)
  const [csvHeaders, setCsvHeaders] = useState([])
  const [csvUrl, setCsvUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isConfirming, setIsConfirming] = useState(false)
  const [sourceType, setSourceType] = useState("")
  const [credentialsModified, setCredentialsModified] = useState(false)
  const [isEditable, setIsEditable] = useState(true)
  const [customQueryParams, setCustomQueryParams] = useState([])
  const [showKeyDialog, setShowKeyDialog] = useState(false)
  const [newKeyName, setNewKeyName] = useState("")
  const [deletedCustomKeys, setDeletedCustomKeys] = useState([])
  const [visibleFields, setVisibleFields] = useState({})
  const [isSourceLoading, setIsSourceLoading] = useState(false)
  const [showResetDialog, setShowResetDialog] = useState(false)
  const [initialDataLoaded, setInitialDataLoaded] = useState(false)

  const toggleVisibility = (fieldName) => {
    setVisibleFields((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }))
  }

  const validateRequiredQueryParams = () => {
    if (!checked || queryParamData.length === 0) {
      return true
    }

    const requiredParams = queryParamData.filter(param =>
      param.req !== false && !deletedCustomKeys.includes(param.key)
    )

    const missingParams = []

    for (const param of requiredParams) {
      const value = queryFormData[param.key]
      if (!value || value.trim() === "") {
        missingParams.push(param.key)
      }
    }

    if (missingParams.length > 0) {
      toast.error(`Please fill all required query parameters: ${missingParams.join(", ")}`, {
        position: "top-right",
        autoClose: 5000,
        style: errorToastStyle,
      })
      return false
    }

    return true
  }

  const isConfirmButtonDisabled = () => {
    if (sourceType === "csv") {
      return false
    }

    if (connectionStatus !== "success" || credentialsModified) {
      return true
    }

    if (checked && queryParamData.length > 0) {
      const requiredParams = queryParamData.filter(param =>
        param.visibility !== false && !deletedCustomKeys.includes(param.key)
      )

      const hasEmptyRequiredParams = requiredParams.some(param => {
        const value = queryFormData[param.key]
        return !value || value.trim() === ""
      })

      if (hasEmptyRequiredParams) {
        return true
      }
    }

    return false
  }

  const moveToNextStep = async (event) => {
    setIsConfirming(true)
    event.preventDefault()
    if (sourceType !== "csv") {
      // Validate required query params
      if (queryParamData.length > 0) {
        const requiredParams = queryParamData.filter(param => param.req !== false && !deletedCustomKeys.includes(param.key))
        const missingParams = requiredParams.filter(param => !queryFormData[param.key] || queryFormData[param.key].trim() === "")
        if (missingParams.length > 0) {
          toast.error("Please fill all required query parameters before proceeding!", {
            position: "top-right",
            autoClose: 3000,
            style: errorToastStyle,
          })
          setIsConfirming(false)
          return
        }
      }
    }

    const areQueryParamsValid = validateRequiredQueryParams()
    if (!areQueryParamsValid) {
      setIsConfirming(false)
      return
    }

    // Create array of all query parameters to preserve duplicates
    const allQueryParams = []

    // Add standard query parameters
    Object.entries(queryFormData).forEach(([key, value]) => {
      allQueryParams.push({ key, value })
    })

    // Add custom query parameters (including duplicates)
    customQueryParams.forEach((param) => {
      if (!deletedCustomKeys.includes(param.key)) {
        allQueryParams.push({ key: param.key, value: param.value })
      }
    })

    // For backward compatibility, also create the merged object format
    const mergedQueryParams = { ...queryFormData }
    // customQueryParams.forEach((param) => {
    //   if (!deletedCustomKeys.includes(param.key)) {
    //     mergedQueryParams[param.key] = param.value
    //   }
    // })
    customQueryParams.forEach((param) => {
      if (!deletedCustomKeys.includes(param.key)) {
        if (mergedQueryParams[param.key]) {
          // Already exists → push to array
          if (Array.isArray(mergedQueryParams[param.key])) {
            mergedQueryParams[param.key].push(param.value);
          } else {
            mergedQueryParams[param.key] = [mergedQueryParams[param.key], param.value];
          }
        } else {
          mergedQueryParams[param.key] = param.value;
        }
      }
    });

    deletedCustomKeys.forEach((key) => {
      delete mergedQueryParams[key]
    })

    const sourceObj = {
      source: selectedSource,
      connDetails: formData,
      queryParam: mergedQueryParams, // Keep for backward compatibility
      queryParamArray: allQueryParams, // New array format to preserve duplicates
      connectionStatus: connectionStatus,
      selectedFile: selectedFile,
      csvHeaders: csvHeaders,
      csvUrl: selectedFile?.azureBlobUrl || csvUrl,
      checked: checked,
      type: sourceType,
      uniqueSourceValues: [],
      apiRateLimit: "",
      lengthOfCSV: "",
      deletedCustomKeys: deletedCustomKeys,
    }

    setMigrationState((prevState) => ({
      ...prevState,
      sourceObjData: sourceObj,
    }))

    const newPlanId = await saveMigration(sourceObj)
    if (newPlanId) {
      const url = new URL(window.location.href)
      url.searchParams.delete("planId")
      url.searchParams.set("plan_id", newPlanId)
      window.history.replaceState({}, "", url.toString())
    }

    toast.success(`Connected to ${selectedSource.name} profile!`, {
      position: "top-right",
      autoClose: 3000,
      style: successToastStyle,
    })

    setOpenDialog(true)
    setTimeout(() => {
      setSelectedTab("2")
      setIsConfirming(false)
    }, 2000)
  }

  const saveMigration = async (sourceObj) => {
    try {
      const payload = {
        plan_name: templateName,
        migration_source: migrationSource,
        migration_target: migrationTarget,
        migration_objects: [],
        updatedAt: Date.now(),
        email_id: email,
        additional_details: {
          sourceObjData: sourceObj,
          targetData: migrationState.targetData,
          dataTypeData: migrationState.dataTypeData,
          dataMappingData: migrationState.dataMappingData,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
          sourceMapRes: migrationState.sourceMapRes || [],
          sourceResAtt: migrationState.sourceResAtt || [],
        },
      }

      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
        payload["createdAt"] = Date.now()
        const activityPayload = {
          email: email,
          activity: "Template Created",
        }
        postActivity(activityPayload)
      }

      const res = await saveMigrationPlan(payload)
      if (res?.message === "plan created successfully" && res?.response?.id) {
        const newPlanId = res.response.id
        setPlanId(newPlanId)
        return newPlanId
      }
      return planId
    } catch (error) {
      toast.error(error?.response?.data?.message || "Something went wrong!", {
        position: "top-right",
        style: errorToastStyle,
      })
      return null
    }
  }

  const handleCsvUploaded = (fileData) => {
    setSelectedFile(fileData)
    setCsvUrl(fileData.azureBlobUrl || "")
    setCsvHeaders(fileData.headers || [])
    setShowUploadDialog(false)
  }

  useEffect(() => {
    if (!sourceObjData || Object.keys(sourceObjData).length === 0) {
      setSelectedSource(null)
      setFormData({})
      setInitialDataLoaded(false)
      setConnectionStatus(null)
      setCredentialsModified(true)
      setIsEditable(true)
      setChecked(false)
      setSelectedFile(null)
      setCsvHeaders([])
      setCsvUrl("")
      setSourceType("")
      setDeletedCustomKeys([])
      setQueryFormData({})
      setCustomQueryParams([])
    } else if (sourceObjData && sourceObjData.source && !initialDataLoaded) {
      const configKey = getConfigKey(sourceObjData.source.name)
      const configSource = config.PLATFORMS_SUPPORTED[configKey]
      const sourceWithFields = {
        ...sourceObjData.source,
      }

      setSelectedSource(sourceWithFields)
      setFormData(sourceObjData.connDetails || {})
      setConnectionStatus(sourceObjData.connectionStatus)
      setIsEditable(sourceObjData.connectionStatus !== "success") // Lock fields if previously connected
      setCredentialsModified(false)
      setChecked(sourceObjData.checked || false)
      setSelectedFile(sourceObjData.selectedFile)
      setCsvHeaders(sourceObjData.csvHeaders || [])
      setCsvUrl(sourceObjData.csvUrl || "")
      setSourceType(sourceObjData.type || "")
      setDeletedCustomKeys(sourceObjData.deletedCustomKeys || [])
      setInitialDataLoaded(true)

      if (sourceObjData.source) {
        fetchFilterOptions(sourceObjData.source.name)
      }
    }

    const fetchProviders = async () => {
      try {
        // Use config.json for name and image URL only, fields come from database
        const configSources = Object.entries(config.PLATFORMS_SUPPORTED).map(([key, source]) => ({
          name: key,
          displayName: source.name,
          imgUrl: source.url,
          isSource: true,
          isTarget: false,
          // Fields will be fetched from database table, not config
        }))

        // Also get API data for compatibility and field definitions
        const apiData = await getMigrationProviders()

        // Filter out API items that are variations of config sources
        const filteredApiData = apiData.filter(apiItem => {
          return !configSources.some(configItem => {
            // Check for exact name match
            if (configItem.name === apiItem.name) return true

            // Use the same normalization logic as getConfigKey
            const normalizedApiName = apiItem.name.toLowerCase()
              .replace(/\s+json$/i, '')
              .replace(/\s+api$/i, '')
              .replace(/\s+connector$/i, '')
              .replace(/\s+/g, '')
              .trim()

            const normalizedConfigName = configItem.name.toLowerCase()
              .replace(/\s+/g, '')
              .trim()

            // Check if they match after normalization - API handles all variations
            if (normalizedConfigName === normalizedApiName) return true

            return false
          })
        })

        // Merge config sources with API data to get fields
        const enhancedConfigSources = configSources.map(configItem => {
          // Find matching API item to get fields
          const matchingApiItem = apiData.find(apiItem => {
            // Check direct name match
            if (apiItem.name === configItem.name) return true

            // Check normalized name match
            const normalizedApiName = apiItem.name.toLowerCase()
              .replace(/\s+json$/i, '')
              .replace(/\s+api$/i, '')
              .replace(/\s+connector$/i, '')
              .replace(/\s+/g, '')
              .trim()

            const normalizedConfigName = configItem.name.toLowerCase()
              .replace(/\s+/g, '')
              .trim()

            if (normalizedConfigName === normalizedApiName) return true

            return false
          })

          return {
            ...configItem,
            // Add fields from API data
            fields: matchingApiItem?.fields || [],
            id: matchingApiItem?.id || configItem.name
          }
        })

        // Merge enhanced config sources with filtered API data (config takes priority)
        const mergedData = [...enhancedConfigSources, ...filteredApiData]
        // Deduplicate by displayName
        const uniqueSources = Array.from(
          new Map(
            mergedData.map(item => [item.displayName, item])
          ).values()
        )
        setSourceData(uniqueSources)
      } catch (error) {
        console.error("Failed to load migration providers:", error)
        // Fallback to config only if API fails - but without fields
        const configSources = Object.entries(config.PLATFORMS_SUPPORTED).map(([key, source]) => ({
          name: key,
          displayName: source.name,
          imgUrl: source.url,
          isSource: true,
          isTarget: false,
          fields: [], // No fields available in fallback mode
        }))
        setSourceData(configSources)
      }
    }

    fetchProviders()
  }, [sourceObjData, initialDataLoaded])

  useEffect(() => {
    if (sourceObjData && (sourceObjData.queryParam || sourceObjData.queryParamArray) && queryParamData.length > 0) {
      const standardParamKeys = queryParamData.map((param) => param.key)

      // Check if we have the new array format that preserves duplicates
      if (sourceObjData.queryParamArray) {
        // Use the new array format
        const standardParams = {}
        const customParams = []

        sourceObjData.queryParamArray.forEach(({ key, value }) => {
          if (standardParamKeys.includes(key)) {
            // For standard params, use the last value (or first, depending on preference)
            standardParams[key] = value
          } else if (!sourceObjData.deletedCustomKeys?.includes(key)) {
            // For custom params, preserve all entries including duplicates
            customParams.push({ key, value })
          }
        })

        setQueryFormData(standardParams)
        setCustomQueryParams(customParams)
      } else {
        // Fallback to old object format for backward compatibility
        const savedQueryParams = { ...sourceObjData.queryParam }
        const standardParams = {}

        Object.entries(savedQueryParams).forEach(([key, value]) => {
          if (standardParamKeys.includes(key)) {
            standardParams[key] = value
          }
        })

        setQueryFormData(standardParams)

        const customParams = []
        Object.entries(savedQueryParams).forEach(([key, value]) => {
          if (!standardParamKeys.includes(key) && !sourceObjData.deletedCustomKeys?.includes(key)) {
            customParams.push({ key, value })
          }
        })

        setCustomQueryParams(customParams)
      }
    }
  }, [sourceObjData, queryParamData])

  useEffect(() => {
    if (selectedSource && selectedSource.name) {
      fetchFilterOptions(selectedSource.name)
    }
  }, [selectedSource])

  const fetchFilterOptions = async (name) => {
    try {
      const data = await filterOptions(name)
      if (data.response && data.response[0] && data.response[0].queryParams) {
        setQueryParamData(data.response[0].queryParams)

        // Set default values for query parameters
        const defaultQueryValues = {}
        data.response[0].queryParams.forEach((param) => {
          if (param.value && param.value.trim() !== "") {
            defaultQueryValues[param.key] = param.value
          }
        })

        const isNewSourceSelection = !sourceObjData || !sourceObjData.queryParam || Object.keys(sourceObjData.queryParam).length === 0

        if (isNewSourceSelection) {
          setQueryFormData(defaultQueryValues)
        } else {
          setQueryFormData(prevData => {
            const hasExistingData = Object.keys(prevData).length > 0
            if (hasExistingData) {
              return prevData
            }
            return defaultQueryValues
          })
        }
      }
    } catch (error) {
      console.error("Failed to load filter options:", error)
      setQueryParamData([])
    }
  }

  const getConfigKey = (sourceName) => {
    if (!sourceName) return null

    const normalizedName = sourceName.toLowerCase()
      .replace(/\s+json$/i, '')
      .replace(/\s+api$/i, '')
      .replace(/\s+connector$/i, '')
      .replace(/\s+/g, '')
      .trim()

    if (config.PLATFORMS_SUPPORTED[normalizedName]) {
      return normalizedName
    }

    if (config.PLATFORMS_SUPPORTED[sourceName]) {
      return sourceName
    }

    return normalizedName
  }

  const handleSourceSelect = (source) => {
    const newSource = sourceData.find((item) => item.name === source)

    const configKey = getConfigKey(source)
    const configSource = config.PLATFORMS_SUPPORTED[configKey]

    const sourceWithFields = {
      ...newSource,
    }

    setMigrationSource([newSource.id])
    localStorage.setItem("sourcePlatform", newSource.displayName || newSource.name)

    if (source === "csv") {
      setSourceType("csv")
    } else {
      setSourceType("api")
    }

    setSelectedSource(sourceWithFields)
    setFormData({})
    setQueryFormData({})
    setConnectionStatus(null)
    setCredentialsModified(true)
    setIsEditable(true) // Enable editing for new source selection
    setChecked(false)
    setSelectedFile(null)
    setCsvHeaders([])
    setCsvUrl("")
    setCustomQueryParams([])
    setDeletedCustomKeys([])
    setVisibleFields({}) // Reset password visibility states

    setMigrationState((prevState) => ({
      ...prevState,
      sourceObjData: {
        source: sourceWithFields,
        connDetails: {},
        connectionStatus: null,
        type: source === "csv" ? "csv" : "api",
      },
      // Clear dependent steps data when source is changed
      dataTypeData: {},
      dataMappingData: {},
      selectedObjectData: {},
      selectedEntityData: {},
      sourceExeRes: [],
      targetExeRes: [],
      sourceMapRes: [],
      sourceResAtt: [],
    }))

    fetchFilterOptions(sourceWithFields.name)
  }

  const handleConnect = async (e) => {
    e.preventDefault()
    await validateSourceFun()
  }

  const handleInputChange = (e, fieldName) => {
    let newValue = e.target.value

    if (fieldName === "instance_url" && newValue.length > 100) {
      newValue = newValue.slice(0, 100)
    } else if (fieldName === "username" && newValue.length > 50) {
      newValue = newValue.slice(0, 50)
    }

    const oldValue = formData[fieldName] || ""

    if (["username", "password", "apikey", "instance_url"].includes(fieldName) && newValue !== oldValue) {
      setCredentialsModified(true)
      setConnectionStatus(null)
      setMigrationState((prevState) => ({
        ...prevState,
        sourceObjData: {
          ...prevState.sourceObjData,
          connectionStatus: null,
        },
      }))
    }

    setFormData((prev) => ({
      ...prev,
      [fieldName]: newValue,
    }))
  }

  const handleQueryInputChange = (e, fieldName) => {
    setQueryFormData((prev) => ({
      ...prev,
      [fieldName]: e.target.value,
    }))
  }

  const handleCustomQueryInputChange = (e, index) => {
    const updatedParams = [...customQueryParams]
    updatedParams[index].value = e.target.value
    setCustomQueryParams(updatedParams)
  }

  const removeSelectedFile = () => {
    setSelectedFile(null)
    setCsvHeaders([])
    setCsvUrl("")
  }

  const handleReset = () => {
    setShowResetDialog(true)
  }

  const confirmReset = () => {
    setFormData({})

    // Reset query parameters to their default values from the database
    const defaultQueryValues = {}
    queryParamData.forEach((param) => {
      if (param.value && param.value.trim() !== "") {
        defaultQueryValues[param.key] = param.value
      }
    })
    setQueryFormData(defaultQueryValues)

    setCustomQueryParams([])
    setConnectionStatus(null)
    setCredentialsModified(true)
    setIsEditable(true) // Enable editing after reset
    setShowResetDialog(false)
    toast.info("All form fields have been reset to default values", {
      position: "top-right",
      style: {
        "--toastify-icon-color-info": "black",
        "--toastify-color-progress-info": "black",
      },
    })
  }

  const addCustomQueryParam = () => {
    setShowKeyDialog(true)
  }

  const handleKeyDialogSubmit = () => {
    if (newKeyName.trim()) {
      const isStandardParam = queryParamData.some((param) => param.key === newKeyName.trim())
      if (isStandardParam) {
        setDeletedCustomKeys((prev) => prev.filter((key) => key !== newKeyName.trim()))
        toast.info(`Parameter "${newKeyName.trim()}" restored`, {
          position: "top-right",
          autoClose: 2000,
          style: {
            "--toastify-icon-color-info": "black",
            "--toastify-color-progress-info": "black",
          },
        })
      } else {
        // Always allow adding query parameters, even with duplicate names
        setCustomQueryParams([...customQueryParams, { key: newKeyName.trim(), value: "" }])
        if (deletedCustomKeys.includes(newKeyName.trim())) {
          setDeletedCustomKeys((prev) => prev.filter((key) => key !== newKeyName.trim()))
        }
      }
      setNewKeyName("")
      setShowKeyDialog(false)
    }
  }

  const removeCustomQueryParam = (index) => {
    const paramToRemove = customQueryParams[index]
    setDeletedCustomKeys((prev) => [...prev, paramToRemove.key])
    if (queryFormData[paramToRemove.key]) {
      const updatedQueryFormData = { ...queryFormData }
      delete updatedQueryFormData[paramToRemove.key]
      setQueryFormData(updatedQueryFormData)
    }
    const updatedParams = customQueryParams.filter((_, i) => i !== index)
    setCustomQueryParams(updatedParams)
  }

  const removeQueryParam = (paramKey) => {
    setDeletedCustomKeys((prev) => [...prev, paramKey])
    const updatedQueryFormData = { ...queryFormData }
    delete updatedQueryFormData[paramKey]
    setQueryFormData(updatedQueryFormData)
    toast.info(`Parameter "${paramKey}" removed`, {
      position: "top-right",
      autoClose: 2000,
      style: {
        "--toastify-icon-color-info": "black",
        "--toastify-color-progress-info": "black",
      },
    })
  }

  const extractSourceDomain = (url) => {
    try {
      const trimmedUrl = url.trim()
      const parsedUrl = new URL(trimmedUrl)
      return parsedUrl.hostname
    } catch (error) {
      console.error("Invalid URL:", error)
      return null
    }
  }

  const handleEditMode = () => {
    setIsEditable(true)
    setConnectionStatus(null)
    setCredentialsModified(true)
  }

  const handleCancelEdit = (event) => {
    if (event) {
      event.preventDefault()
      event.stopPropagation()
    }

    setIsEditable(false)
    setCredentialsModified(false)
    setConnectionStatus("success")

    if (sourceObjData && sourceObjData.connDetails) {
      setFormData(sourceObjData.connDetails)
    }

    if (sourceObjData && (sourceObjData.queryParam || sourceObjData.queryParamArray)) {
      const standardParamKeys = queryParamData.map((param) => param.key)

      // Check if we have the new array format that preserves duplicates
      if (sourceObjData.queryParamArray) {
        // Use the new array format
        const standardParams = {}
        const customParams = []

        sourceObjData.queryParamArray.forEach(({ key, value }) => {
          if (standardParamKeys.includes(key)) {
            // For standard params, use the last value (or first, depending on preference)
            standardParams[key] = value
          } else if (!sourceObjData.deletedCustomKeys?.includes(key)) {
            // For custom params, preserve all entries including duplicates
            customParams.push({ key, value })
          }
        })

        setQueryFormData(standardParams)
        setCustomQueryParams(customParams)
      } else {
        // Fallback to old object format for backward compatibility
        const savedQueryParams = { ...sourceObjData.queryParam }
        const standardParams = {}

        Object.entries(savedQueryParams).forEach(([key, value]) => {
          if (standardParamKeys.includes(key)) {
            standardParams[key] = value
          }
        })

        setQueryFormData(standardParams)

        const customParams = []
        Object.entries(savedQueryParams).forEach(([key, value]) => {
          if (!standardParamKeys.includes(key) && !sourceObjData.deletedCustomKeys?.includes(key)) {
            customParams.push({ key, value })
          }
        })

        setCustomQueryParams(customParams)
      }
    }

    toast.info("Changes cancelled, restored to previous connection", {
      position: "top-right",
      style: {
        "--toastify-icon-color-info": "black",
        "--toastify-color-progress-info": "black",
      },
    })
  }

  const validateSourceFun = async () => {
    setIsLoading(true)
    try {
      const tempKey = String(formData.apikey || "")
      const payload = {
        sourceName: selectedSource.name,
        domain: extractSourceDomain(formData.instance_url),
        domainUrl: extractSourceDomain(formData.instance_url),
        username: tempKey,
        queryParams: [],
      }

      if (formData.username && formData.password) {
        payload["username"] = formData.username
        payload["password"] = formData.password
      }

      const response = await validateSource(payload)

      const connPayload = {
        domain: extractSourceDomain(formData.instance_url),
        accountEmail: formData.username,
        userEmail: email,
        providerName: selectedSource.name,
        isSource: true,
        isTarget: false,
      }

      await updateConnDetails(connPayload)

      setMigrationState((prevState) => ({
        ...prevState,
        sourceObjData: {
          ...prevState.sourceObjData,
          connDetails: formData,
          connectionStatus: "success",
        },
      }))

      setConnectionStatus("success")
      setCredentialsModified(false)
      setIsEditable(false) // Disable editing after successful connection

      toast.success("Connected successfully!", {
        position: "top-right",
        style: successToastStyle,
      })
    } catch (error) {
      toast.error("Connection Failed! Please check your credentials and try again.", {
        position: "top-right",
        style: errorToastStyle,
      })
      setConnectionStatus("failed")

      setMigrationState((prevState) => ({
        ...prevState,
        sourceObjData: {
          ...prevState.sourceObjData,
          connDetails: formData,
          connectionStatus: "failed",
        },
      }))
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (connectionStatus === "success" && queryParamData.length > 0 && !checked) {
      setChecked(true);
    }
  }, [connectionStatus, queryParamData]);

  return (
    <>
      {isConfirming && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
            borderRadius: "8px",
          }}
        >
          <LoaderSpinner fullpage={false} />
        </div>
      )}

      <div className={styles.dFlex}>
        <div className={styles.section} style={{ width: "35%" }}>
          <div className={styles.targetGraphic}>
            <div className={styles.sourceDiagramContainer}>
              {selectedSource ? (
                <div>
                  <div className={styles.selectedSourceLogoOverlay}>
                    <img
                      src={selectedSource.imgUrl || "/placeholder.svg"}
                      alt={selectedSource.displayName}
                      className={styles.selectedSourceLogo}
                    />
                  </div>
                  <img src="/assets/Source-desktop.png" alt="Source Diagram" className={styles.sourceBlankImage} />
                </div>
              ) : (
                <img src="/assets/Sourceconnector.png" alt="Source Diagram" className={styles.sourceBlankImage} />
              )}
            </div>
          </div>
        </div>

        <div className={styles.section} style={{ width: "65%" }}>
          <div className={styles.sourceContainer}>
            <div className={styles.sourceHeaderContainer}>
              <h3 className={styles.sourceTitle}>SELECT A SOURCE</h3>
              <div
                className={styles.sourceHelpContainer}
                onClick={() => {
                  displayArticle("What source platforms are supported?")
                }}
                style={{ cursor: "pointer" }}
              >
                <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                <span style={{ fontFamily: "Inter", fontSize: "14px", color: "#746B68", letterSpacing: "-0.2px" }}>
                  What source platforms are supported?
                </span>
              </div>
            </div>

            <div className={styles.sourceOptions}>
              <div>
                <button
                  className={globalStyles.mainButton}
                  style={
                    selectedFile || selectedSource
                      ? {
                        width: "100%",
                        marginTop: "20px",
                        backgroundColor: "#FFFFFF",
                        color: "#514742",
                        border: "1px solid #DCDAD9",
                        boxShadow: "2px 2px 5px 0 rgba(0, 0, 0, 0.10)",
                      }
                      : { width: "100%" }
                  }
                  onClick={() => {
                    setIsSourceLoading(true)
                    setTimeout(() => {
                      setShowModal(true)
                      setIsSourceLoading(false)
                    }, 500)
                  }}
                >
                  {isSourceLoading ? (
                    <div className={globalStyles.loaderContainer}>
                      <div className={globalStyles.loader}></div>
                    </div>
                  ) : selectedSource ? (
                    "Reselect Source Platform"
                  ) : (
                    "Select a source platform"
                  )}
                </button>

                {sourceType === "csv" && (
                  <>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        marginLeft: "auto",
                        marginTop: "10px",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        displayArticle("Connecting to CSV as a Source")
                      }}
                    >
                      <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                      <span className={globalStyles.guideName}>Connecting to CSV as a Source</span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {selectedFile && (
              <div
                className={styles.selectedFileContainer}
                style={{
                  backgroundColor: "#fff",
                  padding: "15px 20px",
                  borderRadius: "100px",
                  marginTop: "20px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                }}
              >
                <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                  <i className="fa-solid fa-file"></i>
                  <span className={globalStyles.interSummaryStyle}>{selectedFile.name}</span>
                </div>
                <div style={{ width: "65px" }}></div>
                <button
                  onClick={removeSelectedFile}
                  style={{
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    fontSize: "16px",
                  }}
                >
                  <i className="fa fa-trash" aria-hidden="true"></i>
                </button>
              </div>
            )}
          </div>

          <Dialog
            open={showModal}
            onClose={() => setShowModal(false)}
            PaperProps={{
              sx: { width: "705px", maxWidth: "none", backgroundColor: "#170903", marginBottom: "20px" },
            }}
          >
            <DialogContent sx={{ backgroundColor: "#170903", padding: "0", marginBottom: "30px", marginTop: "5px" }}>
              <SourceTargetSelector
                showModel={showModal}
                setShowModel={setShowModal}
                data={Array.from(
                  new Map(
                    sourceData
                      .filter((source) => source.isSource)
                      .map((source) => [source.displayName, {
                        src: source.imgUrl,
                        name: source.displayName,
                        value: source.name,
                      }])
                  ).values()
                )}
                name="SOURCE"
                onSelect={handleSourceSelect}
              />
            </DialogContent>
          </Dialog>

          <Dialog
            open={showKeyDialog}
            onClose={() => setShowKeyDialog(false)}
            maxWidth="md"
            PaperProps={{
              style: {
                borderRadius: 0,
                boxShadow: '0 8px 32px rgba(0,0,0,0.18)',
                fontFamily: 'Inter, sans-serif',
                minWidth: 420,
                maxWidth: 520,
                minHeight: 220,
                maxHeight: 420
              }
            }}
          >
            <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>
              <div
                style={{
                  display: "flex",
                  width: "450px",
                  padding: "20px 30px 30px 30px",
                  flexDirection: "column",
                  alignItems: "flex-start",
                  gap: "15px",
                  borderRadius: "10px",
                  background: "#170903",
                  position: "relative",
                }}
              >
                {/* Close button */}
                <button
                  onClick={() => setShowKeyDialog(false)}
                  style={{
                    position: "absolute",
                    top: "20px",
                    right: "20px",
                    background: "none",
                    border: "none",
                    color: "#fff",
                    fontSize: "24px",
                    cursor: "pointer",
                    padding: "0",
                    width: "30px",
                    height: "30px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  ×
                </button>

                {/* Header */}
                <h2
                  style={{
                    color: "#EA5822",
                    fontFamily: "Poppins",
                    fontSize: "14px",
                    fontStyle: "normal",
                    fontWeight: "700",
                    lineHeight: "24px",
                    margin: "0",
                    textTransform: "uppercase",
                    letterSpacing: "0.5px",
                  }}
                >
                  ENTER THE KEY NAME
                </h2>

                {/* Input field */}
                <input
                  style={{
                    width: "100%",
                    padding: "12px 16px",
                    borderRadius: "6px",
                    border: "1px solid #444",
                    background: "#2a2a2a",
                    color: "#F8F8F7",
                    fontFamily: "Inter",
                    fontSize: "14px",
                    outline: "none",
                    boxSizing: "border-box",
                  }}
                  type="text"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                  placeholder="Enter key name"
                />

                {/* Buttons */}
                <div
                  style={{
                    display: "flex",
                    gap: "10px",
                    alignSelf: "flex-end",
                    marginTop: "5px",
                  }}
                >
                  <button
                    onClick={() => setShowKeyDialog(false)}
                    style={{
                      display: "flex",
                      height: "40px",
                      padding: "8px 20px",
                      justifyContent: "center",
                      alignItems: "center",
                      gap: "10px",
                      minWidth: "100px",
                      borderRadius: "3px",
                      background: "transparent",
                      color: "#F8F8F7",
                      textAlign: "center",
                      fontFamily: "Inter",
                      fontSize: "16px",
                      fontStyle: "normal",
                      fontWeight: "500",
                      lineHeight: "24px",
                      border: "1px solid #F8F8F7",
                      cursor: "pointer",
                      transition: "all 0.3s ease",
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = "#F8F8F7";
                      e.target.style.color = "#170903";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = "transparent";
                      e.target.style.color = "#F8F8F7";
                    }}
                  >
                    Cancel
                  </button>

                  <button
                    onClick={handleKeyDialogSubmit}
                    style={{
                      display: "flex",
                      height: "40px",
                      padding: "8px 20px",
                      justifyContent: "center",
                      alignItems: "center",
                      gap: "10px",
                      minWidth: "100px",
                      borderRadius: "3px",
                      background: "#EF8963",
                      color: "#170903",
                      textAlign: "center",
                      fontFamily: "Inter",
                      fontSize: "16px",
                      fontStyle: "normal",
                      fontWeight: "500",
                      lineHeight: "24px",
                      border: "none",
                      cursor: "pointer",
                      transition: "background-color 0.3s ease",
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = "#EFEEED";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = "#EF8963";
                    }}
                  >
                    OK
                  </button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog
            open={showUploadDialog}
            onClose={() => setShowUploadDialog(false)}
            maxWidth="md"
            PaperProps={{
              sx: { backgroundColor: "transparent", boxShadow: "none", overflow: "visible" },
            }}
          >
            <DialogContent sx={{ padding: 0, overflow: "visible" }}>
              <UploadCSV onFileUploaded={handleCsvUploaded} onClose={() => setShowUploadDialog(false)} />
            </DialogContent>
          </Dialog>

          <Dialog
            open={showResetDialog}
            onClose={() => setShowResetDialog(false)}
            PaperProps={{
              sx: {
                width: "400px",
                padding: "0px",
                overflow: "hidden !important",
                backgroundColor: "#1F1611",
                borderRadius: "8px"
              },
            }}
          >
            <DialogContent sx={{ overflow: "hidden", padding: "0px", backgroundColor: "#1F1611" }}>
              <div style={{
                display: "flex",
                flexDirection: "column",
                backgroundColor: "#1F1611",
                color: "#ffffff",
                padding: "30px"
              }}>
                <div style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "20px"
                }}>
                  <h3 style={{
                    margin: 0,
                    fontSize: "14px",
                    fontFamily: "Poppins",
                    color: "#EA5822",
                    fontWeight: "700",
                    fontStyle: "normal",
                    lineHeight: "24px"
                  }}>
                    CONFIRM RESET
                  </h3>
                  <button
                    onClick={() => setShowResetDialog(false)}
                    style={{
                      background: "none",
                      border: "none",
                      color: "#ffffff",
                      fontSize: "20px",
                      cursor: "pointer",
                      padding: "0",
                      width: "24px",
                      height: "24px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    ✕
                  </button>
                </div>

                <p style={{
                  margin: "0 0 30px 0",
                  fontSize: "16px",
                  color: "#F8F8F7",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: "400",
                  lineHeight: "24px"
                }}>
                  Are you sure you want to reset all form fields?
                </p>

                <div style={{ display: "flex", justifyContent: "flex-end", gap: "10px" }}>
                  <button
                    onClick={() => setShowResetDialog(false)}
                    style={{
                      padding: "8px 16px",
                      borderRadius: "4px",
                      border: "1px solid #666",
                      background: "#1F1611",
                      color: "#ffffff",
                      cursor: "pointer",
                      fontWeight: "500"
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={confirmReset}
                    style={{
                      padding: "8px 16px",
                      borderRadius: "4px",
                      border: "none",
                      background: "#EF8963",
                      color: "#170903",
                      cursor: "pointer",
                      fontWeight: "500"
                    }}
                  >
                    Reset
                  </button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {selectedSource && sourceType !== "csv" && (
            <div className={styles.formSection} style={{ marginTop: "35px" }}>
              <div className={styles.title}>CONNECT TO A {selectedSource.displayName.toUpperCase()} SOURCE PROFILE</div>
              <div className={styles.helpRow} style={{ marginTop: "15px" }}>
                <span>Enter the details</span>
                <div
                  style={{ marginLeft: "auto", display: "flex", alignItems: "center", gap: "8px", cursor: "pointer" }}
                  onClick={() => displayArticle(`What is a ${selectedSource.displayName} Instance URL?`)}
                >
                  <img src="/assets/help.png" alt="Help" className={styles.helpIcon} />
                  <span className={styles.helpText}>{`What is a ${selectedSource.displayName} Instance URL?`}</span>
                </div>
              </div>

              <form onSubmit={handleConnect}>
                <div>
                  {selectedSource?.fields?.map((field) => (
                    <div key={field.name} className={styles.inputContainer}>
                      <input
                        className={`${styles.formControl} ${field.type === "password" ? styles.passwordInput : ""}`}
                        type={field.type === "password" && visibleFields[field.name] ? "text" : field.type}
                        placeholder={field.placeholder ? `${field.placeholder}*` : "Enter value*"}
                        value={formData[field.name] || ""}
                        onChange={(e) => handleInputChange(e, field.name)}
                        maxLength={field.name === "instance_url" ? 100 : field.name === "username" ? 50 : undefined}
                        disabled={!isEditable}
                        style={{
                          backgroundColor: !isEditable ? "#f5f5f5" : "#fff",
                          cursor: !isEditable ? "not-allowed" : "text",
                        }}
                      />
                      {field.type === "password" && isEditable && (
                        <span className={styles.eyeIconContainer} onClick={() => toggleVisibility(field.name)}>
                          {visibleFields[field.name] ? (
                            <EyeIcon className={styles.eyeIcon} />
                          ) : (
                            <EyeOffIcon className={styles.eyeIcon} />
                          )}
                        </span>
                      )}
                    </div>
                  ))}
                </div>

                <div style={{ gap: "10px", marginTop: "10px" }}>
                  {connectionStatus === "success" ? (
                    <div className={styles.successContainer} style={{ width: "100%" }}>
                      <button className={globalStyles.connectedSuccess} style={{ width: "100%", marginTop: "20px" }} type="button">
                        Connected Successfully!
                        <CheckIcon
                          className="eye-icon"
                          style={{
                            color: "green",
                            marginLeft: "10px",
                          }}
                        />
                      </button>

                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          marginLeft: "auto",
                        }}
                        onClick={() => {
                          displayArticle("What are query parameters?")
                        }}
                      >
                        <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                        <span className={globalStyles.guideName}>What are query parameters?</span>
                      </div>

                      <div className={styles.queryParamSection}>
                        <div className={styles.queryParamHeader}>
                          <div style={{ display: "flex", alignItems: "center" }}>
                            <span className={styles.queryText}>Query Parameters</span>
                            <button
                              onClick={addCustomQueryParam}
                              type="button" // prevent form submit when adding custom param
                              style={{
                                border: "1px solid #EA5822",
                                borderRadius: "4px",
                                padding: "4px 12px",
                                background: "white",
                                color: "#EA5822",
                                cursor: "pointer",
                                marginLeft: "10px",
                                fontSize: "12px",
                                fontWeight: "bold",
                              }}
                            >
                              Add
                            </button>
                          </div>
                        </div>

                        {queryParamData.length > 0 && (
                          <div
                            className={styles.inputContainer}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              width: "100%",
                              gap: "8px",
                              marginTop: "15px",
                            }}
                          >
                            {queryParamData.map(
                              (param, index) =>
                                (!deletedCustomKeys.includes(param.key) && (param.visibility !== false)) && (
                                  <div
                                    key={index}
                                    style={{ position: "relative", display: "flex", alignItems: "center", flexDirection: "column", width: "100%" }}
                                  >
                                    {(() => {
                                      // Build label without showing default values
                                      const placeholderText = param.req !== false
                                        ? `${param.key}*`
                                        : (param.placeholder || param.key)
                                      return (
                                        <>
                                          <label
                                            htmlFor={`query-param-${param.key}`}
                                            style={{
                                              alignSelf: "flex-start",
                                              fontSize: "12px",
                                              fontFamily: "Inter",
                                              fontWeight: 500,
                                              color: "#514742",
                                              marginTop: "8px",
                                              marginBottom: "4px",
                                            }}
                                          >
                                            {placeholderText}
                                          </label>
                                          <div style={{ position: "relative", width: "100%" }}>
                                            <input
                                              id={`query-param-${param.key}`}
                                              className={`${styles.formControl}`}
                                              type="text"
                                              placeholder={placeholderText}
                                              value={queryFormData?.[param.key] || ""}
                                              onChange={(e) => handleQueryInputChange(e, param.key)}
                                              style={{
                                                width: "100%",
                                                flex: 1,
                                                marginTop: 0,
                                                borderColor: param.req !== false && (!queryFormData?.[param.key] || queryFormData[param.key].trim() === "") ? "#ff6b6b" : undefined,
                                              }}
                                            />
                                            {param.req === false && (
                                              <button
                                                onClick={() => removeQueryParam(param.key)}
                                                style={{
                                                  position: "absolute",
                                                  top: "50%",
                                                  transform: "translateY(-50%)",
                                                  right: "10px",
                                                  background: "none",
                                                  border: "none",
                                                  cursor: "pointer",
                                                  display: "flex",
                                                  alignItems: "center",
                                                }}
                                                type="button"
                                              >
                                                <MinusIcon style={{ width: "20px", height: "20px", color: "#EA5822" }} />
                                              </button>
                                            )}
                                          </div>
                                        </>
                                      )
                                    })()}
                                  </div>
                                ),
                            )}

                            {customQueryParams.map((param, index) => (
                              <div
                                key={`custom-${index}`}
                                style={{ position: "relative", display: "flex", alignItems: "center", flexDirection: "column", width: "100%" }}
                              >
                                <label
                                  htmlFor={`custom-query-param-${index}`}
                                  style={{
                                    alignSelf: "flex-start",
                                    fontSize: "12px",
                                    fontFamily: "Inter",
                                    fontWeight: 500,
                                    color: "#514742",
                                    marginTop: "15px",
                                    marginBottom: "4px",
                                  }}
                                >
                                  {param.key}
                                </label>
                                <div style={{ position: "relative", width: "100%" }}>
                                  <input
                                    id={`custom-query-param-${index}`}
                                    className={`${styles.formControl}`}
                                    type="text"
                                    placeholder={param.key}
                                    value={param.value || ""}
                                    onChange={(e) => handleCustomQueryInputChange(e, index)}
                                    style={{ flex: 1, marginTop: 0, width: "100%" }}
                                  />
                                  <button
                                    onClick={() => removeCustomQueryParam(index)}
                                    style={{
                                      position: "absolute",
                                      top: "50%",
                                      transform: "translateY(-50%)",
                                      right: "10px",
                                      background: "none",
                                      border: "none",
                                      cursor: "pointer",
                                      display: "flex",
                                      alignItems: "center",
                                    }}
                                    type="button"
                                  >
                                    <MinusIcon style={{ width: "20px", height: "20px", color: "#EA5822" }} />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      <div style={{ display: "flex", gap: "10px", marginTop: "20px", marginBottom: "30px" }}>
                        {!isEditable && (
                          <button
                            className={styles.resetButton}
                            onClick={handleEditMode}
                            type="button"
                            style={{
                              padding: "8px 14px",
                              borderRadius: "4px",
                              border: "1px solid #EA5822",
                              background: "white",
                              color: "#EA5822",
                              cursor: "pointer",
                              fontWeight: "500",
                              flex: "0 0 auto",
                              width: "140px",
                            }}
                          >
                            Edit Connection
                          </button>
                        )}

                        {isEditable &&
                          credentialsModified &&
                          sourceObjData &&
                          sourceObjData.connDetails &&
                          Object.keys(sourceObjData.connDetails).length > 0 && (
                            <button
                              type="button"
                              className={styles.resetButton}
                              style={{
                                padding: "8px 14px",
                                borderRadius: "4px",
                                border: "1px solid #6B7280",
                                background: "white",
                                color: "#6B7280",
                                cursor: "pointer",
                                fontWeight: "500",
                                flex: "0 0 auto",
                                width: "140px",
                              }}
                              onClick={(e) => handleCancelEdit(e)}
                            >
                              Cancel
                            </button>
                          )}

                        <button
                          type="button"
                          className={styles.resetButton}
                          style={{
                            padding: "8px 14px",
                            borderRadius: "4px",
                            border: "1px solid #EA5822",
                            background: "white",
                            color: "#EA5822",
                            cursor: "pointer",
                            fontWeight: "500",
                            flex: "0 0 auto",
                            width: "140px",
                          }}
                          onClick={handleReset}
                        >
                          Reset Connection
                        </button>

                        <button
                          className={styles.newConfirmButton}
                          onClick={moveToNextStep}
                          type="button"
                          style={{
                            flex: 1,
                            opacity: isConfirmButtonDisabled() ? 0.6 : 1,
                            cursor: isConfirmButtonDisabled() ? "not-allowed" : "pointer"
                          }}
                          disabled={isConfirmButtonDisabled()}
                          title={
                            isConfirmButtonDisabled()
                              ? (connectionStatus !== "success" || credentialsModified
                                ? "Please validate your credentials first"
                                : "Please fill all required query parameters")
                              : "Proceed to next step"
                          }
                        >
                          Confirm & Continue
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div style={{ display: "flex", gap: "10px", marginTop: "20px", marginBottom: "30px" }}>
                      {isEditable &&
                        credentialsModified &&
                        sourceObjData &&
                        sourceObjData.connDetails &&
                        Object.keys(sourceObjData.connDetails).length > 0 && (
                          <button
                            type="button"
                            className={styles.resetButton}
                            style={{
                              padding: "8px 14px",
                              borderRadius: "4px",
                              border: "1px solid #6B7280",
                              background: "white",
                              color: "#6B7280",
                              cursor: "pointer",
                              fontWeight: "500",
                              flex: "0 0 auto",
                              width: "140px",
                            }}
                            onClick={(e) => handleCancelEdit(e)}
                          >
                            Cancel
                          </button>
                        )}

                      <button
                        type="button"
                        className={styles.resetButton}
                        style={{
                          padding: "8px 14px",
                          borderRadius: "4px",
                          border: "1px solid #EA5822",
                          background: "white",
                          color: "#EA5822",
                          cursor: "pointer",
                          fontWeight: "500",
                          flex: "0 0 auto",
                          width: "140px",
                        }}
                        onClick={handleReset}
                      >
                        Reset Connection
                      </button>

                      <button
                        type="button"
                        className={globalStyles.mainButton}
                        style={{ flex: 1, marginTop: "0", width: "auto" }}
                        onClick={validateSourceFun}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <div className={styles.loaderContainer}>
                            <div className={styles.loader}></div>
                            <span>Connecting...</span>
                          </div>
                        ) : connectionStatus === "failed" ? (
                          "Reconnect"
                        ) : (
                          "Connect"
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </form>
            </div>
          )}

          <ToastContainer />

          {sourceType === "csv" && (
            <div className={styles.formSection} style={{ marginTop: "35px" }}>
              <button className={styles.newConfirmButton} style={{ width: "100%" }} onClick={moveToNextStep}>
                Confirm & Continue
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default SourceSelect
