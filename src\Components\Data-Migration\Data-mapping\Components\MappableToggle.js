import React from "react";
import FormControlLabel from "@mui/material/FormControlLabel";
import Switch from "@mui/material/Switch";

export default function MappableToggle({ checked, onChange, sx, ...props }) {
  return (
    <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
      <FormControlLabel
        style={{ marginLeft: "auto" }}
        control={
          <Switch
            checked={!!checked}
            onChange={onChange}
            sx={sx || {
              width: 36,
              height: 18,
              padding: 0,
              "& .MuiSwitch-switchBase": {
                padding: 0,
                margin: "2px",
                transitionDuration: "300ms",
                "&.Mui-checked": {
                  transform: "translateX(16px)",
                  color: "#fff",
                  "& + .MuiSwitch-track": {
                    backgroundColor: "#E97451",
                    opacity: 1,
                    border: 0,
                  },
                  "& .MuiSwitch-thumb": {
                    backgroundColor: "#fff",
                    width: 14,
                    height: 14,
                  },
                  "&.<PERSON>i-disabled + .MuiSwitch-track": {
                    opacity: 0.5,
                  },
                },
              },
              "& .MuiSwitch-thumb": {
                backgroundColor: "#fff",
                boxSizing: "border-box",
                width: 14,
                height: 14,
                borderRadius: "50%",
                transition: "width 0.2s, height 0.2s",
              },
              "& .MuiSwitch-track": {
                borderRadius: 20,
                backgroundColor: "#B9B5B3",
                opacity: 1,
                transition: "background-color 0.5s",
              },
            }}
          />
        }
        label=""
        {...props}
      />
    </div>
  );
}
