import React, { createContext, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
// import { shutdown } from "@intercom/messenger-js-sdk";

// Create the Session Context
export const SessionContext = createContext();

// Session expiration duration in minutes
const SESSION_DURATION_MINUTES = 720; // 12 hours
const SESSION_DURATION_MS = SESSION_DURATION_MINUTES * 60 * 1000; // minutes in milliseconds

// Create a Session Provider component
export const SessionProvider = ({ children }) => {
    const navigate = useNavigate();

    const [user, setUser] = useState(() => {
        const savedUser = localStorage.getItem("email");
        const savedExpiry = localStorage.getItem("sessionExpiry");

        if (savedUser && savedExpiry) {
            const expiryTime = parseInt(savedExpiry, 10);
            const currentTime = Date.now();

            if (currentTime > expiryTime) {
                localStorage.removeItem("email");
                localStorage.removeItem("sessionExpiry");
                // localStorage.removeItem("sourcePlatform");
                // localStorage.removeItem("targetPlatform");
                // shutdown();
                // ["email", "sessionExpiry", "sourcePlatform", "targetPlatform", "forceDataMigrationRefresh", "sourceSelectionData", "dataMigrationState"].forEach((key) => {
                //     if (localStorage.getItem(key)) localStorage.removeItem(key);
                // });
                // ["dataMigrationState", "selectedSourcePlatform", "sourceSelectionData"].forEach((key) => {
                //     if (sessionStorage.getItem(key)) sessionStorage.removeItem(key);
                // });
                return null;
            }
            // Parse the saved user as JSON (since we store it as JSON in login)
            return savedUser;
        }
        return null;
    });

    const [sessionExpired, setSessionExpired] = useState(false);

    const login = (userData) => {
        setUser(userData);
        setSessionExpired(false);
        const expiryTime = Date.now() + SESSION_DURATION_MS;
        // Store userData as a JSON string
        localStorage.setItem("email", userData);
        localStorage.setItem("sessionExpiry", expiryTime.toString());
    };

    const logout = () => {
        setUser(null);
        setSessionExpired(false);
        ["email", "sessionExpiry"/*, "sourcePlatform", "targetPlatform", "forceDataMigrationRefresh", "sourceSelectionData"*/].forEach((key) => {
            if (localStorage.getItem(key)) localStorage.removeItem(key);
        });
        navigate("/");
    };

    const isAuthenticated = !!user;

    // Function to reset the session expiry timer on user activity
    const resetSessionExpiry = () => {
        if (user) { // Only reset if a user is logged in
            const newExpiryTime = Date.now() + SESSION_DURATION_MS;
            localStorage.setItem("sessionExpiry", newExpiryTime.toString());
        }
    };

    useEffect(() => {
        const checkSessionExpiry = () => {
            const savedExpiry = localStorage.getItem("sessionExpiry");
            if (savedExpiry) {
                const expiryTime = parseInt(savedExpiry, 10);
                const currentTime = Date.now();

                if (currentTime > expiryTime) {
                    setSessionExpired(true);
                }
            }
        };

        // Add event listeners for user activity
        const events = ["mousemove", "mousedown", "keypress", "scroll"];
        events.forEach((event) => {
            window.addEventListener(event, resetSessionExpiry);
        });

        checkSessionExpiry();
        const interval = setInterval(checkSessionExpiry, 5 * 1000);

        // Clean up event listeners and interval on unmount
        return () => {
            events.forEach((event) => {
                window.removeEventListener(event, resetSessionExpiry);
            });
            clearInterval(interval);
        };
    }, [user]); // Re-run if user state changes

    const value = {
        user,
        isAuthenticated,
        login,
        logout,
        sessionExpired,
    };

    return (
        <SessionContext.Provider value={value}>
            {children}
        </SessionContext.Provider>
    );
};

export const useSession = () => {
    const context = React.useContext(SessionContext);
    if (!context) {
        throw new Error("useSession must be used within a SessionProvider");
    }
    return context;
};