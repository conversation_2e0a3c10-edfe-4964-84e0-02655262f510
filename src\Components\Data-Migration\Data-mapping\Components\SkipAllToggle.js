import React from "react";
import FormControlLabel from "@mui/material/FormControlLabel";
import Switch from "@mui/material/Switch";

const SkipAllOptionalToggle = ({ checked, onChange, label = "", disabled = false, sx, ...props }) => {
  return (
    <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
      <FormControlLabel
        control={
          <Switch
            checked={!!checked}
            onChange={onChange}
            disabled={disabled}
            sx={sx || {
              width: 36,
              height: 18,
              padding: 0,
              "& .MuiSwitch-switchBase": {
                padding: 0,
                margin: "2px",
                transitionDuration: "300ms",
                "&.Mui-checked": {
                  transform: "translateX(16px)",
                  color: "#fff",
                  "& + .MuiSwitch-track": {
                    backgroundColor: "#E97451",
                    opacity: 1,
                    border: 0,
                  },
                  "& .<PERSON>iSwitch-thumb": {
                    backgroundColor: "#fff",
                    width: 14,
                    height: 14,
                  },
                  "&.Mui-disabled + .MuiSwitch-track": {
                    opacity: 0.5,
                  },
                },
                "&.Mui-disabled": {
                  color: "#bdbdbd",
                  "& .MuiSwitch-thumb": {
                    backgroundColor: "#f5f5f5",
                  },
                },
              },
              "& .MuiSwitch-thumb": {
                backgroundColor: "#fff",
                boxSizing: "border-box",
                width: 14,
                height: 14,
                borderRadius: "50%",
                transition: "width 0.2s, height 0.2s",
              },
              "& .MuiSwitch-track": {
                borderRadius: 20,
                backgroundColor: "#B9B5B3",
                opacity: 1,
                transition: "background-color 0.5s",
              },
            }}
          />
        }
        label={label}
        labelPlacement="start"
        sx={{
          marginLeft: "auto",
          "& .MuiFormControlLabel-label": {
            fontSize: "14px",
            fontWeight: "500",
            color: disabled ? "#999999" : "#333333",
            fontFamily: "'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif",
            letterSpacing: "0.02em",
            marginRight: "12px",
            userSelect: "none"
          }
        }}
        {...props}
      />
    </div>
  );
};

export default SkipAllOptionalToggle;