import { useState, createContext, useContext, useEffect, useRef, useCallback } from "react"
import styles from "./Data-Migration.module.css"
import Sidebar from "../Sidebar/Sidebar"
import { ArrowLeftIcon, GlobeIcon, SearchIcon } from "@heroicons/react/solid"
import globalStyles from "../globalStyles.module.css"

import Tab from "@mui/material/Tab"
import Tabs from "@mui/material/Tabs"
import TabContext from "@mui/lab/TabContext"
import TabPanel from "@mui/lab/TabPanel"
import { PencilIcon } from "@heroicons/react/outline"
import TargetConnection from "./Target-connection/Target-connection"
import Datatype from "./Data-type/Data-type"
import DataMapping from "./Data-mapping/Data-mapping"
import SourceSelect from "./SourceSelect/SourceSelect"
import Review from "./Review/Review"
import { useLocation, useNavigate } from "react-router-dom"
import { useSearchParams } from "react-router-dom"
import { getMigrationPlanById, migrationTemplate } from "../apiService"
import ShareYourTemplate from "../Share-your-template/ShareYourTemplate"

export const MigrationContext = createContext()

export const MigrationProvider = ({ children }) => {
  const [migrationState, setMigrationState] = useState({
    sourceObjData: {},
    targetData: {},
    dataTypeData: {},
    dataMappingData: {},
    selectedObjectData: {},
    selectedEntityData: {},
    sourceExeRes: [],
    targetExeRes: [],
    sourceMapRes: [],
    sourceResAtt: [],
    workflowDetails: {},
    templateName: "",
    lastCompletedStep: 0,
  })

  useEffect(() => {
    console.log(migrationState)
  }, [migrationState])

  return <MigrationContext.Provider value={{ migrationState, setMigrationState }}>{children}</MigrationContext.Provider>
}

export default function DataMigration() {
  const location = useLocation()
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const [searchParams] = useSearchParams()
  const editPlanId = searchParams.get("planId")
  const planIdFromUrl = searchParams.get("plan_id")
  const tracking = location.state?.tracking
  const newTemplate = location.state?.newTemplate

  const initialPlanId = editPlanId || planIdFromUrl || ""

  const tab = location.state?.tab
  const isSample = location.state?.isSample
  const paymentSuccess = location.state?.paymentSuccess
  const [selectedTab, setSelectedTab] = useState(tab || "1")
  const [email, setEmail] = useState(localStorage.getItem("email"))
  const [dependentFields, setDependentFields] = useState([])
  const [isEditing, setIsEditing] = useState(false)
  const [templateName, setTemplateName] = useState("Name of the Template")
  const [planId, setPlanId] = useState(initialPlanId)
  const [isEditMode, setIsEditMode] = useState(!!initialPlanId)
  const navigate = useNavigate()
  const [migrationSource, setMigrationSource] = useState([])
  const [migrationTarget, setMigrationTarget] = useState([])
  const hasFetched = useRef(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [lastActiveStep, setLastActiveStep] = useState(1)
  const previousPlanId = useRef(initialPlanId)

  const [completedSteps, setCompletedSteps] = useState({
    1: false,
    2: false,
    3: false,
    4: false,
    5: false,
  })

  const getAllCompletedSteps = useCallback(() => {
    const storedData = localStorage.getItem("completedSteps")
    return storedData ? JSON.parse(storedData) : []
  }, [])

  const saveCompletedSteps = useCallback(
    (planId, userEmail, steps, templateName = null) => {
      const allSteps = getAllCompletedSteps()
      const existingIndex = allSteps.findIndex((item) => item.plan_id === planId && item.user === userEmail)

      const newEntry = {
        plan_id: planId,
        user: userEmail,
        state: steps,
        templateName: templateName || migrationState.templateName,
        lastActiveStep: Math.max(
          ...Object.keys(steps)
            .filter((step) => steps[step])
            .map(Number),
          0,
        ),
      }

      if (existingIndex >= 0) {
        allSteps[existingIndex] = newEntry
      } else {
        allSteps.push(newEntry)
      }

      localStorage.setItem("completedSteps", JSON.stringify(allSteps))
    },
    [getAllCompletedSteps, migrationState.templateName],
  )

  const getCompletedStepsForPlan = useCallback(
    (planId, userEmail) => {
      const allSteps = getAllCompletedSteps()
      const planData = allSteps.find((item) => item.plan_id === planId && item.user === userEmail)
      return planData ? planData : null
    },
    [getAllCompletedSteps],
  )

  const calculateLastCompletedStep = useCallback(() => {
    let lastStep = 0
    for (let i = 1; i <= 5; i++) {
      if (completedSteps[i]) {
        lastStep = i
      }
    }
    return lastStep
  }, [completedSteps])

  const getEnabledTabs = useCallback(() => {
    const enabled = { 1: true }

    const lastCompletedStep = calculateLastCompletedStep()

    for (let i = 1; i <= lastCompletedStep + 1; i++) {
      enabled[i] = true
    }

    return enabled
  }, [calculateLastCompletedStep])

  const enabledTabs = getEnabledTabs()

  const markStepCompleted = useCallback(
    (step) => {
      setCompletedSteps((prev) => {
        const newCompletedSteps = { ...prev, [step]: true }

        if (planId && email) {
          saveCompletedSteps(planId, email, newCompletedSteps)
        }

        if (step > lastActiveStep) {
          setLastActiveStep(step)
        }

        return newCompletedSteps
      })

      setMigrationState((prev) => ({
        ...prev,
        lastCompletedStep: Math.max(prev.lastCompletedStep || 0, step),
      }))
    },
    [planId, email, lastActiveStep, setMigrationState, saveCompletedSteps],
  )

  // Debounced function to save template name to server
  const debounceTimeoutRef = useRef(null)
  const [isSavingTemplate, setIsSavingTemplate] = useState(false)

  const debouncedSaveTemplateName = useCallback(
    async (templateName) => {
      if (!planId || !templateName || templateName === "Name of the Template") {
        return
      }

      try {
        setIsSavingTemplate(true)
        await migrationTemplate.update(planId, {
          plan_name: templateName,
        })
        console.log("Template name saved successfully:", templateName)
      } catch (error) {
        console.error("Error saving template name:", error)
        // Optionally show user notification here
      } finally {
        setIsSavingTemplate(false)
      }
    },
    [planId],
  )

  const triggerDebouncedSave = useCallback(
    (templateName) => {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }

      // Set new timeout for 1 second delay
      debounceTimeoutRef.current = setTimeout(() => {
        debouncedSaveTemplateName(templateName)
      }, 1000)
    },
    [debouncedSaveTemplateName],
  )

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  const determineInitialTab = useCallback(
    (savedPlanData) => {
      if (!savedPlanData) return "1"

      const completedSteps = savedPlanData.state || {}
      // Find the highest completed step up to step 3
      let highestCompleted = 0
      for (let i = 1; i <= 3; i++) {
        if (completedSteps[i]) {
          highestCompleted = i
        }
      }
      
      return highestCompleted > 0 ? highestCompleted.toString() : "1"
    },
    [],
  )

  useEffect(() => {
    // Sync template name between component state and migration state
    // Skip synchronization if we're in the process of creating a new template
    if (!newTemplate) {
      if (migrationState.templateName && migrationState.templateName !== "Name of the Template") {
        setTemplateName(migrationState.templateName)
      } else if (templateName && templateName !== "Name of the Template") {
        setMigrationState((prevState) => ({
          ...prevState,
          templateName: templateName,
        }))
      }
    }
  }, [migrationState.templateName, templateName, newTemplate])

  useEffect(() => {
    if (planId && email) {
      const savedPlanData = getCompletedStepsForPlan(planId, email)

      if (savedPlanData) {
        setCompletedSteps(savedPlanData.state)

        if (savedPlanData.templateName) {
          setTemplateName(savedPlanData.templateName)
          setMigrationState((prev) => ({
            ...prev,
            templateName: savedPlanData.templateName,
          }))
        }

        if (isEditMode && !tab) {
          const initialTab = determineInitialTab(savedPlanData)
          setSelectedTab(initialTab)
        }

        setLastActiveStep(savedPlanData.lastActiveStep || 1)
      }

      const url = new URL(window.location.href)
      url.searchParams.set("plan_id", planId)
      url.searchParams.delete("planId")
      window.history.replaceState({}, "", url.toString())
    }
  }, [planId, email, isEditMode, tab, determineInitialTab, getCompletedStepsForPlan])

  const handleResetAll = useCallback(() => {
    // Reset migration state
    setMigrationState({
      sourceObjData: {},
      targetData: {},
      dataTypeData: {},
      dataMappingData: {},
      selectedObjectData: {},
      selectedEntityData: {},
      sourceExeRes: [],
      targetExeRes: [],
      sourceMapRes: [],
      sourceResAtt: [],
      workflowDetails: {},
      templateName: "Name of the Template", // Set default template name
      lastCompletedStep: 0,
    })

    // Reset component state
    setTemplateName("Name of the Template")
    setMigrationSource([])
    setMigrationTarget([])
    setPlanId("")
    setIsEditMode(false)
    setDependentFields([])
    setSelectedTab("1")
    setCompletedSteps({ 1: false, 2: false, 3: false, 4: false, 5: false })
    setLastActiveStep(1)
    hasFetched.current = false

    // Clean up localStorage if needed
    if (planId && email) {
      const allSteps = getAllCompletedSteps()
      const updatedSteps = allSteps.filter((item) => !(item.plan_id === planId && item.user === email))
      localStorage.setItem("completedSteps", JSON.stringify(updatedSteps))
    }
  }, [planId, email, getAllCompletedSteps])

  useEffect(() => {
    if (tracking) {
      console.log("Tracking changed, resetting state...", tracking)
      handleResetAll()
    }
  }, [tracking, handleResetAll])

  // Handle new template creation when navigating from sidebar or home
  useEffect(() => {
    if (newTemplate) {
      console.log("Creating new template, resetting state...")

      // If clearAll flag is set, clear additional localStorage items
      if (location.state?.clearAll) {
        console.log("Clearing all migration-related data...")
        // Clear any remaining migration-related localStorage items
        const keysToRemove = [
          'completedSteps',
          'lastActiveStep',
          'migrationState',
          'sourceConnectionData',
          'targetConnectionData'
        ];
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
        });
      }

      // Reset the state to create a clean new template
      setMigrationState({
        sourceObjData: {},
        targetData: {},
        dataTypeData: {},
        dataMappingData: {},
        selectedObjectData: {},
        selectedEntityData: {},
        sourceExeRes: [],
        targetExeRes: [],
        sourceMapRes: [],
        sourceResAtt: [],
        workflowDetails: {},
        templateName: "Name of the Template",
        lastCompletedStep: 0,
      })

      // Reset component state
      setTemplateName("Name of the Template")
      setMigrationSource([])
      setMigrationTarget([])
      setPlanId("")
      setIsEditMode(false)
      setDependentFields([])
      setSelectedTab("1")
      setCompletedSteps({ 1: false, 2: false, 3: false, 4: false, 5: false })
      setLastActiveStep(1)

      // Clear URL parameters to avoid confusion
      const url = new URL(window.location.href)
      url.search = ""
      window.history.replaceState({}, "", url.toString())

      // Reset fetch flag to allow loading new data
      hasFetched.current = false
    }
  }, [newTemplate, location.state, setMigrationState, setTemplateName, setMigrationSource, setMigrationTarget, setPlanId, setIsEditMode, setDependentFields, setSelectedTab, setCompletedSteps, setLastActiveStep])

  // Handle plan ID changes - reset state when switching between different plans
  useEffect(() => {
    if (initialPlanId && previousPlanId.current && initialPlanId !== previousPlanId.current) {
      console.log("Plan ID changed from", previousPlanId.current, "to", initialPlanId, "- resetting state...")

      // Reset the hasFetched flag to allow fetching new plan data
      hasFetched.current = false

      // Reset migration state to prevent old plan data from showing
      setMigrationState({
        sourceObjData: {},
        targetData: {},
        dataTypeData: {},
        dataMappingData: {},
        selectedObjectData: {},
        selectedEntityData: {},
        sourceExeRes: [],
        targetExeRes: [],
        sourceMapRes: [],
        sourceResAtt: [],
        workflowDetails: {},
        templateName: "Name of the Template",
        lastCompletedStep: 0,
      })

      // Reset component state
      setTemplateName("Name of the Template")
      setMigrationSource([])
      setMigrationTarget([])
      setPlanId(initialPlanId)
      setIsEditMode(!!initialPlanId)
      setDependentFields([])
      setSelectedTab("1")
      setCompletedSteps({ 1: false, 2: false, 3: false, 4: false, 5: false })
      setLastActiveStep(1)

      // Update the previous plan ID reference
      previousPlanId.current = initialPlanId
    } else if (initialPlanId && !previousPlanId.current) {
      // First time setting a plan ID
      previousPlanId.current = initialPlanId
    } else if (!initialPlanId && previousPlanId.current) {
      // Plan ID was cleared (e.g., creating new template)
      previousPlanId.current = ""
    }
  }, [initialPlanId, setMigrationState])

  useEffect(() => {
    // Fetch existing plan if available and we're not creating a new template
    if (initialPlanId && !hasFetched.current && !newTemplate) {
      setIsEditMode(true)
      hasFetched.current = true
      const fetchMigrationPlan = async () => {
        try {
          const res = await getMigrationPlanById(email, initialPlanId)
          if (res.response && res.response.length > 0) {
            const final = res.response[0].additional_details
            const lastStep = final.lastCompletedStep || 0

            // First set the migration state
            setMigrationState((prevState) => ({
              ...prevState,
              sourceObjData: final.sourceObjData || {},
              targetData: final.targetData || {},
              dataTypeData: final.dataTypeData || {},
              dataMappingData: final.dataMappingData || {},
              selectedObjectData: final.selectedObjectData || {},
              selectedEntityData: final.selectedEntityData || {},
              sourceExeRes: final.sourceExeRes || [],
              targetExeRes: final.targetExeRes || [],
              sourceMapRes: final.sourceMapRes || [],
              sourceResAtt: final.sourceResAtt || [],
              templateName: res.response[0].plan_name,
              lastCompletedStep: lastStep,
            }))
            setTemplateName(res.response[0].plan_name)

            setMigrationSource([res.response[0].migration_source[0].id])
            setMigrationTarget([res.response[0].migration_target[0].id])

            const newCompletedSteps = {
              1: Object.keys(final.sourceObjData || {}).length > 0,
              2: Object.keys(final.targetData || {}).length > 0,
              3: Object.keys(final.dataTypeData || {}).length > 0,
              4: Object.keys(final.dataMappingData || {}).length > 0,
              5: final.reviewCompleted || false,
            }

            setCompletedSteps(newCompletedSteps)            // Find highest completed step up to step 3
            let initialStep = 1
            for (let i = 1; i <= 3; i++) {
              if (newCompletedSteps[i]) {
                initialStep = i
              }
            }

            // Save completed steps first
            saveCompletedSteps(initialPlanId, email, newCompletedSteps)

            // Set both states synchronously if no tab is specified in URL
            if (!tab) {
              // Use a single batch update for both states
              Promise.resolve().then(() => {
                setLastActiveStep(initialStep)
                setSelectedTab(initialStep.toString())
              })
            }
          }
        } catch (error) {
          console.error("Error fetching migration plan:", error)
        }
      }
      fetchMigrationPlan()
    }
  }, [initialPlanId, email, tab, saveCompletedSteps, newTemplate])

  useEffect(() => {
    const newCompletedSteps = { ...completedSteps }

    if (Object.keys(migrationState.sourceObjData).length > 0) {
      newCompletedSteps[1] = true
    }

    if (Object.keys(migrationState.targetData).length > 0) {
      newCompletedSteps[2] = true
    }

    if (Object.keys(migrationState.dataTypeData).length > 0) {
      newCompletedSteps[3] = true
    }

    if (Object.keys(migrationState.dataMappingData).length > 0) {
      newCompletedSteps[4] = true
    }

    if (JSON.stringify(newCompletedSteps) !== JSON.stringify(completedSteps)) {
      setCompletedSteps(newCompletedSteps)

      if (planId && email) {
        saveCompletedSteps(planId, email, newCompletedSteps)
      }

      const completedStepNumbers = Object.keys(newCompletedSteps)
        .filter((step) => newCompletedSteps[step])
        .map(Number)

      const highestCompleted = completedStepNumbers.length > 0 ? Math.max(...completedStepNumbers) : 0

      if (highestCompleted > lastActiveStep) {
        setLastActiveStep(highestCompleted)
      }
    }
  }, [migrationState, completedSteps, planId, email, lastActiveStep, saveCompletedSteps])

  const handleChange = (event, newValue) => {
    if (enabledTabs[newValue]) {
      setSelectedTab(newValue)
      const numericValue = Number.parseInt(newValue, 10)
      if (numericValue > lastActiveStep) {
        setLastActiveStep(numericValue)
      }
    }
  }

  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    return localStorage.getItem("isSidebarCollapsed") === "true"
  })

  const handleEditClick = () => {
    setIsEditing(true)
  }

  const handleTemplateChange = (event) => {
    const { value } = event.target
    setTemplateName(value)
    setMigrationState((prevState) => ({
      ...prevState,
      templateName: value,
    }))

    // Save the template name to localStorage for immediate persistence
    if (planId) {
      const savedPlanData = getCompletedStepsForPlan(planId, email)
      if (savedPlanData) {
        savedPlanData.templateName = value
        saveCompletedSteps(planId, email, savedPlanData.state, value)
      }
    }

    // Trigger debounced save to server
    triggerDebouncedSave(value)
  }

  const handleBlur = () => {
    setIsEditing(false)
    // Immediately save to server when user finishes editing
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
      debouncedSaveTemplateName(templateName)
    }
  }

  const handleShareClick = () => {
    setShowShareModal(true)
  }

  const handleCloseShareModal = () => {
    setShowShareModal(false)
  }

  const renderHeader = () => {
    return (
      <div className={styles.stepHeader} style={{ height: "45px" }}>
        <div
          className={styles.dFlex}
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            flex: "1 0 0",
            alignSelf: "stretch",
            paddingTop: "0px",
            height: "100%"
          }}
        >
          <div className={styles.dFlex} style={{ alignItems: "center", gap: "20px" }}>
            <button
              className={styles.backButton}
              onClick={() => {
                if (selectedTab === "1") {
                  navigate("/home")
                } else {
                  setSelectedTab((prevTab) => (Number.parseInt(prevTab, 10) - 1).toString())
                }
              }}
            >
              <ArrowLeftIcon className={styles.arrowIcon} /> Back
            </button>
            <div className={styles.templateNameContainer}>
              <div className={styles.templateNameSection}>
                {isEditing ? (
                  <input
                    type="text"
                    value={templateName}
                    onChange={handleTemplateChange}
                    onKeyDown={(event) => {
                      if (event.key === "Enter") {
                        handleBlur()
                      }
                    }}
                    onBlur={handleBlur}
                    className={styles.templateInput}
                    autoFocus
                  />
                ) : (
                  <div
                    className={`${globalStyles.headerStyle} ${styles.dFlex}`}
                    onClick={handleEditClick}
                    style={{ fontSize: "16px" }}
                  >
                    <PencilIcon className={styles.arrowIcon} /> {templateName}
                  </div>
                )}
                {isSavingTemplate && (
                  <div style={{ 
                    marginLeft: "8px",
                    display: "flex",
                    alignItems: "center"
                  }}>
                    <div className={styles.spinner}></div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className={styles.templateIdText}>{planId ? `Template ID #${planId}` : ""}</div>
          <div style={{ display: "flex" }}>
            {/* <button className={`${styles.backButton} ${globalStyles.interStyle}`} onClick={handleShareClick}>
              <ShareIcon className={styles.arrowIcon} /> Share
            </button> */}
            {/* <button className={`${styles.backButton} ${globalStyles.interStyle}`}>
              <HiArrowDownTray className={styles.arrowIcon} /> Download
            </button> */}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div style={{padding:"0px 45px 0px 0px"}}>
      <Sidebar isCollapsed={isSidebarCollapsed} setIsCollapsed={setIsSidebarCollapsed} />
      <div className={`${styles["main-section"]} ${isSidebarCollapsed ? styles.expanded : ""}`}>
        <div className={styles.dFlex} style={{ paddingLeft: "24px", paddingRight: "24px", justifyContent: "space-between" }}>
          <div className={globalStyles.headerStyle}>Create a template</div>
          <div className={globalStyles.searchBarContainer}>
            <div className={globalStyles.searchBar}>
              {/* Search functionality temporarily hidden
              <div className={globalStyles.searchWrapper}>
                <SearchIcon className={globalStyles.searchIcon} />
                <input
                  type="text"
                  placeholder="Search..."
                  className={globalStyles.searchInput}
                  onFocus={(e) => {
                    e.target.style.width = "200px"
                    e.target.placeholder = "Typing..."
                  }}
                  onBlur={(e) => {
                    e.target.style.width = "80px"
                    e.target.placeholder = "Search..."
                  }}
                />
              </div>
              */}

              <div className={globalStyles.searchWrapper}style={{ marginRight: 0 }}>
                <GlobeIcon className={globalStyles.searchIcon}/>
                <input
                  type="text"
                  placeholder="Eng"
                  className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                  readOnly
                />
              </div>
            </div>
          </div>
        </div>
        <div>
          <TabContext value={selectedTab}>
            <Tabs
              sx={{
                "& .Mui-selected": { backgroundColor: "#EFEEED" },
                "& .MuiTabs-indicator": { display: "none" },
              }}
              style={{ paddingLeft: "24px" }}
              className={globalStyles.tab}
              value={selectedTab}
              onChange={handleChange}
            >
              {[1, 2, 3, 4, 5].map((step) => (
                <Tab
                  key={step}
                  label={
                    <div className="tab-label">
                      <div
                        className={styles.stepNo}
                        style={{
                          fontWeight: 600,
                          color:
                            selectedTab === step.toString() ? "#170903" : enabledTabs[step] ? "#B9B5B3" : "#D6D3D1",
                        }}
                      >
                        {" "}
                        STEP {step}
                      </div>
                      <div
                        className={styles.stepNo}
                        style={{
                          fontSize: "16px",
                          paddingTop: "5px",
                          textTransform: "none",
                          color:
                            selectedTab === step.toString() ? "#170903" : enabledTabs[step] ? "#B9B5B3" : "#D6D3D1",
                          fontWeight: selectedTab === step.toString() ? "600" : "400",
                          opacity: enabledTabs[step] ? 1 : 0.6,
                        }}
                      >
                        {" "}
                        {step === 1 && "Connect to Source"}
                        {step === 2 && "Connect to Target"}
                        {step === 3 && "Select Data Type"}
                        {step === 4 && "Map Data"}
                        {step === 5 && "Migrate Data"}
                      </div>
                    </div>
                  }
                  style={{
                    flexGrow: 1,
                    borderRight: "1px solid #EFEEED",
                    cursor: enabledTabs[step] ? "pointer" : "not-allowed",
                  }}
                  value={step.toString()}
                  disabled={!enabledTabs[step]}
                />
              ))}
            </Tabs>
            <TabPanel value="1" sx={{ padding: "0 0 0 24px" }}>
              {renderHeader()}
              <div>
                <SourceSelect
                  setSelectedTab={setSelectedTab}
                  templateName={templateName}
                  planId={planId}
                  setPlanId={setPlanId}
                  setMigrationSource={setMigrationSource}
                  migrationSource={migrationSource}
                  migrationTarget={migrationTarget}
                  markStepCompleted={() => markStepCompleted(1)}
                />
              </div>
            </TabPanel>
            <TabPanel value="2" sx={{ padding: "0 0 0 24px" }}>
              {renderHeader()}
              <div>
                <TargetConnection
                  setSelectedTab={setSelectedTab}
                  templateName={templateName}
                  planId={planId}
                  setPlanId={setPlanId}
                  migrationTarget={migrationTarget}
                  setMigrationTarget={setMigrationTarget}
                  markStepCompleted={() => markStepCompleted(2)}
                />
              </div>
            </TabPanel>
            <TabPanel value="3" sx={{ padding: "0 0 0 24px" }}>
              {renderHeader()}
              <div>
                <Datatype
                  setSelectedTab={setSelectedTab}
                  dependentFields={dependentFields}
                  setDependentFields={setDependentFields}
                  templateName={templateName}
                  planId={planId}
                  setPlanId={setPlanId}
                  markStepCompleted={() => markStepCompleted(3)}
                />
              </div>
            </TabPanel>
            <TabPanel value="4" sx={{ padding: "0 0 0 24px" }}>
              {renderHeader()}
              <div>
                <DataMapping
                  setSelectedTab={setSelectedTab}
                  dependentFields={dependentFields}
                  templateName={templateName}
                  planId={planId}
                  setPlanId={setPlanId}
                  markStepCompleted={() => markStepCompleted(4)}
                  completedSteps={completedSteps}
                />
              </div>
            </TabPanel>
            <TabPanel value="5" sx={{ padding: "0 0 0 24px" }}>
              {renderHeader()}
              <div>
                <Review
                  isSample={isSample}
                  paymentSuccess={paymentSuccess}
                  templateName={templateName}
                  planId={planId}
                  setPlanId={setPlanId}
                  markStepCompleted={() => markStepCompleted(5)}
                />
              </div>
            </TabPanel>
          </TabContext>
        </div>
      </div>

      {showShareModal && (
        <ShareYourTemplate
          onClose={handleCloseShareModal}
          templateName={templateName || "(Template name)"}
          templateId={planId || "(Template ID)"}
        />
      )}
    </div>
  )
}
