import React, { useState } from "react";
import "../global_styles.css";
import styles from "./Sign-up.module.css";
import { CheckIcon, XIcon } from '@heroicons/react/solid';
import { useNavigate } from "react-router-dom";
import Header from "../Header/Header";
import { postActivity, signUpOrSignInUser } from "../apiService";
import CryptoJS from "crypto-js";
import { FormControl, MenuItem, Select } from "@mui/material";
import globalStyles from "../globalStyles.module.css";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Intercom from "@intercom/messenger-js-sdk";
import config from '../../Config/config.json';

export default function SignUp() {
    const [formSubmitted, setFormSubmitted] = useState(false);
    const [password, setPassword] = useState("");
    const [email, setEmail] = useState("");
    const [name, setName] = useState("");
    const navigate = useNavigate();
    const [nextClicked, setNextClicked] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const SESSION_DURATION_MINUTES = 720; // 12 hours
    const SESSION_DURATION_MS = SESSION_DURATION_MINUTES * 60 * 1000; // minutes in milliseconds
    const trustedLogos = config.TRUSTED_BY_LOGOS
    const signInClick = () => {
        navigate('/');
    }
    const [strongPassword, setStrongPassword] = useState(false);
    const checkStrongPassword = (e) => {
        const passwordValue = e.target.value;
        setPassword(passwordValue);
        setStrongPassword(passwordValue);

        const strongPasswordPattern = /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/;

        if (strongPasswordPattern.test(passwordValue)) {
            setStrongPassword(true);
        } else {
            setStrongPassword(false);
        }
    };
    // const [company, setCompany] = useState('');
    // const [designation, setDesignation] = useState('');
    // const [code, setCode] = useState("+91");
    // const [phoneNumber, setPhoneNumber] = useState('');

    const encrypt = (text) => {
        console.log(text);
        const secretKey = CryptoJS.enc.Utf8.parse("sgmg22025sgmg220");
        const iv = CryptoJS.enc.Utf8.parse("1234567890123456");
        return CryptoJS.AES.encrypt(text, secretKey, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }).toString();
    };
    const goToNext = () => {
        if (email && name && password && strongPassword && /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email)) {
            setNextClicked(true);
        }
    }

    const getUserFun = async (e) => {
        e.preventDefault();
        setFormSubmitted(true);
        if (email && name && password && strongPassword && /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email)) {
            try {
                setIsLoading(true);
                const payload = {
                    "name": name,
                    "email": email,
                    "password": encrypt(password),
                    // "company": company,
                    // "designation": designation,
                    // "country_code": code,
                    // "phone_number": phoneNumber
                }

                const res = await signUpOrSignInUser('signup', payload);
                if (res.status === "User created") {
                    const activityPayload = {
                        email: email,
                        activity: 'User Logged In',
                        // timestamp: Date.now(),
                    }
                    postActivity(activityPayload);
                    setIsLoading(false);
                    localStorage.setItem('email', email);
                    const expiryTime = Date.now() + SESSION_DURATION_MS;
                    localStorage.setItem("sessionExpiry", expiryTime.toString());
                    // Intercom initialization
                    console.log("User Email:", email);
                    const secretKey = config.INTERCOM_KEY;
                    const hash = CryptoJS.HmacSHA256(email, secretKey).toString(CryptoJS.enc.Hex);
                    const intercomConfig = {
                        app_id: config.INTERCOM_APP_ID,
                        user_id: email,
                        name: email,
                        email: email,
                        user_hash: hash
                    };
                    Intercom(intercomConfig);
                    navigate('/home', { state: { newUser: true } });
                }

            } catch (error) {
                setIsLoading(false);
                toast.error(error?.response.data.status || "Something went wrong!", {
                    position: "top-right",
                });
            }
        }
    }

    // const countryCodes = [
    //     { code: "+91", name: "India" },
    //     { code: "+1", name: "United States" },
    //     { code: "+44", name: "United Kingdom" },
    //     { code: "+61", name: "Australia" },
    //     { code: "+1", name: "Canada" },
    // ];

    return (
        <div className="d-flex flex-column">
            <div>
                <Header />
            </div>

            <div className={styles.mainContainer}>
                <div className={styles.signInContainer}>
                    <div className={styles.leftContent}>
                        <div className={globalStyles.headerStyle}>
                            <div style={{ fontSize: '45px' }}>Effortless Data Migration</div>
                            <div style={{ fontSize: '45px' }}>Automated!</div>
                        </div>
                        <div className={globalStyles.interSummaryStyle} style={{ padding: '20px' }}>
                            With <b>migrateGenie</b>, move your data between platforms with confidence.
                            Automate mapping, track progress and ensure secure transactions - no headaches.
                        </div>
                        <img src="/assets/Login Animation.gif" alt="image" className={styles.loginImage} />
                    </div>
                    <div className={styles.rightSide}>
                        <div className={styles.container}>
                            <div className={styles.buttonContainer}>
                                <button className="no-color-button" onClick={signInClick}>Sign-in instead</button>
                            </div>
                            <div className="form-container">
                                <p className={styles.description}>Enter details to Sign-up</p>
                                <form onSubmit={getUserFun}>
                                    {!nextClicked ? (
                                        <div>
                                            <div className="form-group" style={{ paddingTop: "5px" }}>
                                                <input
                                                    type="text"
                                                    className={`form-control ${formSubmitted && (!name) ? 'input-error' : ''}`}
                                                    style={{ width: "100%" }}
                                                    id="exampleInputname1"
                                                    placeholder="Your full name"
                                                    value={name}
                                                    onChange={(e) => {
                                                        setName(e.target.value);
                                                        setFormSubmitted(false);
                                                    }}
                                                />
                                                {formSubmitted && !name &&
                                                    <p className="error-message">Name cannot be empty</p>}
                                            </div>
                                            <div className="form-group">
                                                <input
                                                    type="text"
                                                    className={`form-control ${formSubmitted && (!email || !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email)) ? 'input-error' : ''}`}
                                                    style={{ width: "100%" }}
                                                    id="exampleInputEmail1"
                                                    aria-describedby="emailHelp"
                                                    placeholder="Your email"
                                                    value={email}
                                                    onChange={(e) => {
                                                        setEmail(e.target.value);
                                                        setFormSubmitted(false);
                                                    }}
                                                />
                                                {formSubmitted && !email &&
                                                    <p className="error-message">Email is required</p>}
                                                {formSubmitted && email && !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email) &&
                                                    <p className="error-message">Please enter a valid email address</p>}
                                            </div>

                                            <div className={styles.passwordContainer}>
                                                <input
                                                    type="password"
                                                    className={`${styles.passwordInput} ${formSubmitted && (!password) ? 'input-error' : ''}`}
                                                    placeholder="Your password"
                                                    value={password}
                                                    onChange={(e) => {
                                                        checkStrongPassword(e);
                                                        setFormSubmitted(false);
                                                    }}
                                                />
                                                <button
                                                    type="button"
                                                    className={styles.passwordToggle}
                                                >
                                                    {!strongPassword && password.length > 0 ? (
                                                        <XIcon className="eye-icon" style={{ color: "red" }} />
                                                    ) : (
                                                        strongPassword ? (
                                                            <CheckIcon className="eye-icon" style={{ color: "green" }} />
                                                        ) : null
                                                    )}
                                                </button>
                                                {formSubmitted && !password &&
                                                    <p className="error-message">Password cannot be empty</p>}
                                                {formSubmitted && password && !strongPassword &&
                                                    <p className="error-message">Password must be strong</p>}
                                            </div>
                                            <p className={styles.passwordHelp}>Password must be 8 characters
                                                long with 1 captial letter, 1 number, 1 symbol</p>

                                            <button className={styles.signupButton} type="submit">
                                                {isLoading ? (
                                                    <div className={globalStyles.loaderContainer}>
                                                        <div className={globalStyles.loader}></div>
                                                    </div>
                                                ) : (
                                                    "Sign-up"
                                                )}
                                            </button>
                                        </div>
                                    ) : (
                                        <div>
                                        </div>
                                    )}
                                </form>
                            </div>
                            <div className={styles.buttonContainerNew}>
                                <button className="no-color-button" style={{ marginBottom: "15px", width: "100%" }}>
                                    <img src="/assets/img.png" alt="Google Icon" className={styles.buttonIcon} />Sign-in
                                    with Google
                                </button>
                                <button className="no-color-button">
                                    <img src="/assets/img_1.png" alt="Google Icon" className={styles.buttonIcon} />
                                    Sign-in with Microsoft
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div className={styles.featuresSection}>
                    <div className={`${globalStyles.bottomContainer} ${globalStyles.poppinsStyle}`} style={{ fontWeight: "600" }}>
                        <div className={styles.featureItem}>
                            <div className={globalStyles.imageContainer}>
                                <img src="/assets/nano.png" alt="Smart auto-mapping" className={globalStyles.imageBottom} />
                            </div>
                            <span>Smart auto-mapping</span>
                        </div>
                        <div className={styles.featureItem}>
                            <div className={globalStyles.imageContainer}>
                                <img src="/assets/lock.png" alt="Secure with encryption" className={globalStyles.imageBottom} />
                            </div>
                            <span>Secure with encryption</span>
                        </div>
                        <div className={styles.featureItem}>
                            <div className={globalStyles.imageContainer}>
                                <img src="/assets/lightstrike.png" alt="No-code configurations" className={globalStyles.imageBottom} />
                            </div>
                            <span>No-code configurations</span>
                        </div>
                        <div className={styles.featureItem}>
                            <div className={globalStyles.imageContainer}>
                                <img src="/assets/analytics.png" alt="Migration reports" className={globalStyles.imageBottom} />
                            </div>
                            <span>Migration reports</span>
                        </div>
                    </div>
                    <div className={styles.trustedBySection}>
                        <hr className={globalStyles.signupHr} />
                        <div className={styles.trustedByContainer}>
                            <div className={styles.trustedByTitle}>Trusted by</div>
                            <div className={globalStyles.trustedLogos}>
                                {trustedLogos.map((logo, idx) =>
                                    logo.url ? (
                                        <a
                                            key={logo.alt}
                                            href={logo.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className={styles.trustedLogo}
                                            style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                                        >
                                            <img
                                                src={logo.src}
                                                alt={logo.alt}
                                                style={{ width: 100, height: 80, objectFit: "contain" }}
                                            />
                                        </a>
                                    ) : (                                            <div
                                            key={logo.alt}
                                            className={styles.trustedLogo}
                                            style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                                        >
                                            <img
                                                src={logo.src}
                                                alt={logo.alt}
                                                style={{ width: 100, height: 80, objectFit: "contain" }}
                                            />
                                        </div>
                                    )
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ToastContainer
                position="top-right"
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="light"
                style={{
                    fontFamily: "Inter",
                }}
                toastStyle={{
                    fontFamily: "Inter",
                    fontWeight: "bold",
                }}
            />
        </div>
    );
}
