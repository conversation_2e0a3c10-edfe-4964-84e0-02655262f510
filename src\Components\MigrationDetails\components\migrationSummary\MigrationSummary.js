import React, { useState, useEffect, useContext } from "react";
import PropTypes from 'prop-types';
import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import { useLocation, useNavigate } from "react-router-dom";
import { RefreshIcon, StopIcon, EyeIcon } from "@heroicons/react/solid";
import { Dialog, DialogContent, Tooltip } from "@mui/material";
import styles from "../../MigrationDetails.module.css";
import globalStyles from "../../../globalStyles.module.css";
import bseStyles from "./BSE.module.css";
import LoaderSpinner from "../../../loaderspinner";
import RecordsTable from "./Table/RecordsTable";
import { getTransformation, migration, migrationTemplate, postToQueue, terminateMigration } from "../../../apiService";
import { tr } from "date-fns/locale";
import { MigrationContext } from "../../../Data-Migration/Data-Migration";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ConfirmationModal from "../ConfirmationModal/ConfirmationModal";
import { displayArticle } from "../../../../Helper/helper";

const MigrationSummary = ({ migrationId, isPolling = false, isSample = false, showLiveButton, migrationData }) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { migrationState } = useContext(MigrationContext);

    const [availableEntities, setAvailableEntities] = useState([]);
    const [executionData, setExecutionData] = useState({});
    const [sampleRecords, setSampleRecords] = useState([]);
    const [entityTables, setEntityTables] = useState({});
    const [migrationEntities, setMigrationEntities] = useState(["tickets"]);
    const [migrationCount, setMigrationCount] = useState(0);
    const [entityDataLoading, setEntityDataLoading] = useState({});
    const [pollingIntervals, setPollingIntervals] = useState(new Map());
    const stopAllPolling = () => {
        setPollingIntervals(prev => {
            prev.forEach((interval, entityName) => {
                clearInterval(interval);
            });
            return new Map();
        });
    };

    const startPolling = (entityName, id, page = 1, pageSize) => {
        if (!isPolling) return;

        setPollingIntervals(prev => {
            if (prev.has(entityName)) {
                clearInterval(prev.get(entityName));
            }
            return new Map(prev);
        });

        const interval = setInterval(async () => {
            try {
                const migrationStatusData = await fetchMigrationStatus(id);

                if (migrationStatusData?.status === "COMPLETED") {
                    setPollingIntervals(prev => {
                        const newMap = new Map(prev);
                        if (newMap.has(entityName)) {
                            clearInterval(newMap.get(entityName));
                            newMap.delete(entityName);
                        }
                        return newMap;
                    });
                    return;
                }

                const response = await migration.filter({
                    migrationId: id,
                    page: page,
                    pageSize: pageSize,
                    entityName: entityName
                });

                if (response?.data?.entity && typeof response.data.entity === 'object') {
                    const transformedRecords = transformNewDataFormat(response.data.entity);

                    setEntityTables(prev => ({
                        ...prev,
                        [entityName]: transformedRecords.filter(record => record.entityType === entityName)
                    }));

                    if (response.pagination) {
                        const entityPaginationKey = `${entityName}_pagination`;
                        setEntityTableStates(prev => ({
                            ...prev,
                            [entityName]: {
                                ...prev[entityName],
                                currentPage: response.pagination.page > 0 ? response.pagination.page - 1 : 0,
                                rowsPerPage: response.pagination.pageSize || rowsPerPage,
                                totalItems: response.pagination.totalItems
                            }
                        }));
                    }
                }
            } catch (error) {
                console.error(`Error in polling ${entityName} data:`, error);
            }
        }, isSample ? 30000 : 60000);

        setPollingIntervals(prev => {
            const newMap = new Map(prev);
            newMap.set(entityName, interval);
            return newMap;
        });
    };

    const [terminateLoading, setTerminateLoading] = useState(false);

    const handleTerminate = async () => {
        if (!migrationId) return;
        setTerminateLoading(true);
        try {
            const res = await terminateMigration(migrationId);
            toast.success("Migration terminated successfully!", {
                position: "top-right",
                style: { background: "#000", color: "#fff" },
            });
            // Optionally, refresh details or update status here
        } catch (err) {
            toast.error("Failed to terminate migration.", {
                position: "top-right",
                style: { background: "#000", color: "#fff" },
            });
        } finally {
            setTerminateLoading(false);
        }
    };

    const fetchEntityData = async (entityName, migrationId, page = 1, pageSize = rowsPerPage) => {
        setEntityDataLoading(prev => ({ ...prev, [entityName]: true }));

        try {
            const response = await migration.filter({
                migrationId: migrationId,
                page: page,
                pageSize: pageSize,
                entityName: entityName
            });

            if (response?.data?.entity && typeof response.data.entity === 'object') {
                const transformedRecords = transformNewDataFormat(response.data.entity);

                setEntityTables(prev => ({
                    ...prev,
                    [entityName]: transformedRecords.filter(record => record.entityType === entityName)
                }));

                if (response.pagination) {
                    const entityPaginationKey = `${entityName}_pagination`;
                    setEntityTableStates(prev => ({
                        ...prev,
                        [entityName]: {
                            ...prev[entityName],
                            currentPage: response.pagination.page > 0 ? response.pagination.page - 1 : 0,
                            rowsPerPage: response.pagination.pageSize || rowsPerPage,
                            totalItems: response.pagination.totalItems
                        }
                    }));
                }
            }
        } catch (error) {
            console.error(`Error fetching ${entityName} data:`, error);
        } finally {
            setEntityDataLoading(prev => ({ ...prev, [entityName]: false }));
        }
    };

    const fetchMigrationStatus = async (migrationId) => {
        setIsLoading(true);
        try {
            const response = await migration.getStatus({
                migrationId: migrationId
            });


            const migrationData = response?.data || {};

            let totalPassed = 0;
            let totalFailed = 0;
            if (migrationData.count_status && typeof migrationData.count_status === 'object') {
                Object.entries(migrationData.count_status).forEach(([key, value]) => {
                    if (key !== 'passed_records' && key !== 'failed_records' && typeof value === 'object') {
                        totalPassed += value.passed || 0;
                        totalFailed += value.failed || 0;
                    }
                });
            }
            migrationData.totalPassedRecords = totalPassed;
            migrationData.totalFailedRecords = totalFailed;

            setExecutionData(migrationData);

            if (migrationData.entity && typeof migrationData.entity === 'object') {
                const availableEntities = Object.keys(migrationData.entity);
                if (availableEntities.length > 0) {
                    setMigrationEntities(availableEntities);

                    initializeEntityTableStates(availableEntities);

                    setAvailableEntities(availableEntities);

                    for (const entityName of availableEntities) {
                        await fetchEntityData(entityName, migrationId);
                    }
                }
            }

            setIsLoading(false);
            return migrationData;
        } catch (error) {
            console.error("Error fetching migration status:", error);
            setIsLoading(false);
            return null;
        }
    };

    // Group records by entity type
    const groupRecordsByEntity = (records) => {
        const grouped = {};
        records.forEach(record => {
            const entityType = record.entityType || 'unknown';
            if (!grouped[entityType]) {
                grouped[entityType] = [];
            }
            grouped[entityType].push(record);
        });
        return grouped;
    };

    // Initialize entity table states
    const initializeEntityTableStates = (entityTypes) => {
        const states = {};
        entityTypes.forEach(entityType => {
            states[entityType] = {
                currentFilter: "all",
                currentPage: 0,
                rowsPerPage: 100,
                totalItems: 0
            };
        });
        setEntityTableStates(states);
    };

    // Get entity-specific table state
    const getEntityTableState = (entityType) => {
        return entityTableStates[entityType] || {
            currentFilter: "all",
            currentPage: 0,
            rowsPerPage: 100,
            totalItems: 0
        };
    };

    // Update entity-specific table state
    const updateEntityTableState = (entityType, updates) => {
        setEntityTableStates(prev => ({
            ...prev,
            [entityType]: {
                ...prev[entityType],
                ...updates
            }
        }));
    };

    // Transform the new data format into the format expected by the component
    const transformNewDataFormat = (entityData) => {
        if (!entityData || typeof entityData !== 'object') return [];

        const transformedRecords = [];

        // Iterate through each entity type (tickets, changes, etc.)
        Object.keys(entityData).forEach(entityType => {
            const entitiesOfType = entityData[entityType];

            // Iterate through each record ID for this entity type
            Object.keys(entitiesOfType).forEach(recordId => {
                const entity = entitiesOfType[recordId];

                // Transform dependents to the expected format
                const transformedDependents = [];
                if (entity.dependents && Array.isArray(entity.dependents)) {
                    entity.dependents.forEach(dependentGroup => {
                        Object.keys(dependentGroup).forEach(dependentType => {
                            const dependentRecords = dependentGroup[dependentType];
                            const dependentArray = Object.keys(dependentRecords).map(depId => ({
                                source_id: dependentRecords[depId].source_id,
                                sourceId: dependentRecords[depId].source_id,
                                id: depId,
                                key_id: depId,
                                target_id: dependentRecords[depId].target_id,
                                start_time: dependentRecords[depId].start_time,
                                startTime: dependentRecords[depId].start_time,
                                end_time: dependentRecords[depId].end_time,
                                endTime: dependentRecords[depId].end_time,
                                status: dependentRecords[depId].status,
                                batchId: depId,
                                entityType: dependentType,
                                dependents: dependentRecords[depId].dependents || []
                            }));

                            transformedDependents.push({
                                [dependentType]: dependentArray
                            });
                        });
                    });
                }

                // Map entity properties to the expected format
                const recordData = {
                    sourceId: entity.source_id,
                    source_id: entity.source_id,
                    id: recordId,
                    key_id: recordId,
                    startTime: entity.start_time,
                    start_time: entity.start_time,
                    endTime: entity.end_time,
                    end_time: entity.end_time,
                    status: entity.status,
                    batchId: recordId,
                    target_id: entity.target_id,
                    entityType: entityType,
                    dependents: transformedDependents
                };

                transformedRecords.push(recordData);
            });
        });

        return transformedRecords;
    };

    const handleRetry = async () => {
        setIsLoading(true);
        try {
            const serviceBusResponse = await postToQueue({ migration_id: migrationId.toString() })

            if (serviceBusResponse?.isSuccess === false) {
                throw new Error("Failed to send message to queue")
            }
            console.log("Message sent to Service Bus queue successfully")
            toast.success("Migration retry queued successfully", {
                position: "top-right",
            })
        } catch (error) {
            console.error("Error sending message to Service Bus queue:", error)
            toast.error("Failed to queue migration task", {
                position: "top-right",
            })
        } finally {
            setIsLoading(false);
        }
    }

    const [isLoading, setIsLoading] = useState(false);
    const [showRetryConfirm, setShowRetryConfirm] = useState(false);
    const [showTerminateConfirm, setShowTerminateConfirm] = useState(false);
    const [isDownloading, setIsDownloading] = useState(false);
    const [currentFilter, setCurrentFilter] = useState("all");
    const [currentPage, setCurrentPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [expandedRows, setExpandedRows] = useState({});
    const [subIsLoading, setSubIsLoading] = useState({});
    const [subTableData, setSubTableData] = useState({});
    const [activeDependentTabs, setActiveDependentTabs] = useState({});

    // Separate state management for each entity table
    const [entityTableStates, setEntityTableStates] = useState({});
    
    // State for query params
    const [sourceQueryParams, setSourceQueryParams] = useState({});
    const [showQueryParams, setShowQueryParams] = useState(false);

    const getEntityData = async (migrationId) => {
        try {
            const migrationData = await getTransformation(migrationId);

            const planId = migrationData[0]?.template_id

            const planData = await migrationTemplate.get({ id: planId });
            const metadata = JSON.parse(planData.metadata || "{}");
            
            let queryParams = {};
            try {
                if (metadata?.additional_details?.sourceObjData?.queryParam) {
                    queryParams = metadata.additional_details.sourceObjData.queryParam;
                }
                else if (planData?.additional_details?.sourceObjData?.queryParam) {
                    queryParams = planData.additional_details.sourceObjData.queryParam;
                }
                else if (metadata?.sourceObjData?.queryParam) {
                    queryParams = metadata.sourceObjData.queryParam;
                }
                else if (planData?.sourceObjData?.queryParam) {
                    queryParams = planData.sourceObjData.queryParam;
                }
                else {
                    if (planData && planData.migration_source && typeof planData.migration_source === 'string') {
                        try {
                            const sourceObj = JSON.parse(planData.migration_source);
                            
                            if (sourceObj.queryParam) {
                                queryParams = sourceObj.queryParam;
                            } else if (sourceObj.queryParams) {
                                queryParams = sourceObj.queryParams;
                            } else if (sourceObj.query_param) {
                                queryParams = sourceObj.query_param;
                            } else if (sourceObj.additional_details?.queryParam) {
                                queryParams = sourceObj.additional_details.queryParam;
                            } else if (sourceObj.sourceObjData?.queryParam) {
                                queryParams = sourceObj.sourceObjData.queryParam;
                            }
                        } catch (e) {
                            // Error parsing migration_source
                        }
                    }
                    
                    if (Object.keys(queryParams).length === 0 && planData?.migration_objects && typeof planData.migration_objects === 'string') {
                        try {
                            const objectsData = JSON.parse(planData.migration_objects);
                            
                            if (objectsData.sourceObjData?.queryParam) {
                                queryParams = objectsData.sourceObjData.queryParam;
                            } else if (objectsData.queryParam) {
                                queryParams = objectsData.queryParam;
                            } else if (objectsData.queryParams) {
                                queryParams = objectsData.queryParams;
                            } else if (objectsData.additional_details?.sourceObjData?.queryParam) {
                                queryParams = objectsData.additional_details.sourceObjData.queryParam;
                            }
                        } catch (e) {
                            // Error parsing migration_objects
                        }
                    }
                }
            } catch (e) {
                console.error("Error extracting query params:", e);
            }
            
            if (Object.keys(queryParams).length === 0 && migrationState?.sourceObjData?.queryParam) {
                queryParams = migrationState.sourceObjData.queryParam;
            }
            
            setSourceQueryParams(queryParams);
            
            if (migrationData?.entities && Array.isArray(migrationData.entities)) {
                const entities = migrationData.entities.map(e => e.name || e.type);
                setMigrationEntities(entities);

                initializeEntityTableStates(entities);
            }
        } catch (error) {
            console.error("Error fetching migration data:", error);
        }
    };

    useEffect(() => {
        const actualMigrationId = migrationId;
        if (actualMigrationId) {
            getEntityData(actualMigrationId);
            fetchMigrationStatus(actualMigrationId);
        }
    }, [migrationId]);

    useEffect(() => {
        const actualMigrationId = migrationId;

        if (isPolling && migrationEntities.length > 0 && actualMigrationId) {
            migrationEntities.forEach(entityName => {
                startPolling(entityName, actualMigrationId, 1, rowsPerPage);
            });
        } else if (!isPolling) {
            setPollingIntervals(prev => {
                prev.forEach((interval, entityName) => {
                    clearInterval(interval);
                });
                return new Map();
            });
        }

        return () => {
            setPollingIntervals(prev => {
                prev.forEach((interval, entityName) => {
                    clearInterval(interval);
                });
                return new Map();
            });
        };
    }, [isPolling, migrationEntities.length, rowsPerPage]);

    useEffect(() => {
        const actualMigrationId = migrationId;

        if (actualMigrationId && migrationEntities.length > 0) {
            migrationEntities.forEach(entityName => {
                fetchEntityData(entityName, actualMigrationId);
            });
        }
    }, [migrationEntities]);

    useEffect(() => {
        if (executionData?.status === "COMPLETED") {
            stopAllPolling();

            const planId = migrationState?.planId || new URLSearchParams(location.search).get("plan_id") || new URLSearchParams(location.search).get("planId");
            if (planId) {
                const planKey = `completedSampleMigration_${planId}`;
                localStorage.setItem(planKey, 'true');
            }
            if (isPolling && showLiveButton) {
                showLiveButton();
            }
        }
    }, [executionData?.status, migrationState?.planId, location.search, isPolling, showLiveButton]); // Watch for changes in status

    // No need to update sampleRecords separately since we're handling it in fetchMigrationStatus

    const toggleTab = (recordKey, tabIndex) => {
        setActiveDependentTabs(prev => ({
            ...prev,
            [recordKey]: tabIndex
        }));

        // Update the sub-table data based on the selected tab
        if (expandedRows[recordKey]) {
            updateSubTableDataFromTab(recordKey, tabIndex);
        }
    };

    const updateSubTableDataFromTab = (recordKey, tabIndex) => {
        // Extract entityType and sourceId from the recordKey
        const [entityType, sourceId] = recordKey.split('_');        // Find the record with the given sourceId and entityType
        const entityRecords = entityTables[entityType] || [];
        const record = entityRecords.find(record =>
        (record.source_id === parseInt(sourceId) || record.source_id === sourceId ||
            record.sourceId === parseInt(sourceId) || record.sourceId === sourceId)
        );

        if (record && record.dependents && record.dependents.length > tabIndex) {
            const dependent = record.dependents[tabIndex];
            const dependentType = Object.keys(dependent)[0];
            const dependentData = dependent[dependentType];

            if (Array.isArray(dependentData)) {
                setSubTableData(prev => ({
                    ...prev,
                    [recordKey]: dependentData
                }));
            }
        }
    };

    const formatTimestamp = (timestamp) => {
        if (!timestamp) return "-";
        // Convert Unix timestamp (seconds) to milliseconds if needed
        const date = timestamp > 1000000000000 ? new Date(timestamp) : new Date(timestamp * 1000);
        return date.toLocaleString();
    };

    // Handler functions for RecordsTable
    const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
    };

    const handleRowsPerPageChange = (newRowsPerPage) => {
        setRowsPerPage(newRowsPerPage);
        setCurrentPage(0);
    };
    const handleFilterChange = (filter) => {
        setCurrentFilter(filter);
        setCurrentPage(0);
    };

    // Function to handle search for a specific entity
    const handleEntitySearch = async (entityType, searchTerm) => {
        try {
            const actualMigrationId = migrationId;
            const entityState = getEntityTableState(entityType);

            const response = await migration.filter({
                migrationId: actualMigrationId,
                page: 1, // Reset to first page for search
                pageSize: entityState.rowsPerPage,
                entityName: entityType,
                searchQuery: searchTerm
            });

            if (response?.data?.entity && typeof response.data.entity === 'object') {
                const transformedRecords = transformNewDataFormat(response.data.entity);

                // Update entity-specific data with search results
                setEntityTables(prev => ({
                    ...prev,
                    [entityType]: transformedRecords
                }));

                // Reset to first page
                updateEntityTableState(entityType, {
                    currentPage: 0,
                    totalItems: response.data.total || transformedRecords.length
                });
            }
        } catch (error) {
            console.error(`Error searching ${entityType}:`, error);
        }
    };

    // Entity-specific handler functions
    const createEntityHandlers = (entityType) => ({
        onPageChange: async (newPage) => {
            updateEntityTableState(entityType, { currentPage: newPage });

            const actualMigrationId = migrationId;
            const entityState = getEntityTableState(entityType);

            // Check if there's an active filter and handle accordingly
            if (entityState.currentFilter === "passed") {
                try {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: true }));
                    const response = await migration.filter({
                        migrationId: actualMigrationId,
                        status: "SUCCESS",
                        page: newPage + 1,
                        pageSize: entityState.rowsPerPage,
                        entityName: entityType
                    });

                    if (response?.data?.entity) {
                        const filteredRecords = transformNewDataFormat(response.data.entity);
                        setEntityTables(prev => ({
                            ...prev,
                            [entityType]: filteredRecords.filter(record => record.entityType === entityType)
                        }));

                        if (response.pagination) {
                            updateEntityTableState(entityType, {
                                currentPage: response.pagination.page > 0 ? response.pagination.page - 1 : 0,
                                totalItems: response.pagination.totalItems || 0
                            });
                        }
                    }
                } catch (error) {
                    console.error(`Error fetching filtered success records for ${entityType}:`, error);
                } finally {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: false }));
                }
            } else if (entityState.currentFilter === "failed") {
                try {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: true }));
                    const response = await migration.filter({
                        migrationId: actualMigrationId,
                        status: "FAILED",
                        page: newPage + 1,
                        pageSize: entityState.rowsPerPage,
                        entityName: entityType
                    });

                    if (response?.data?.entity) {
                        const filteredRecords = transformNewDataFormat(response.data.entity);
                        setEntityTables(prev => ({
                            ...prev,
                            [entityType]: filteredRecords.filter(record => record.entityType === entityType)
                        }));

                        if (response.pagination) {
                            updateEntityTableState(entityType, {
                                currentPage: response.pagination.page > 0 ? response.pagination.page - 1 : 0,
                                totalItems: response.pagination.totalItems || 0
                            });
                        }
                    }
                } catch (error) {
                    console.error(`Error fetching filtered failed records for ${entityType}:`, error);
                } finally {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: false }));
                }
            } else {
                // No filter applied, fetch all records
                fetchEntityData(entityType, actualMigrationId, newPage + 1, entityState.rowsPerPage);
            }
        }, onSearch: (searchTerm) => {
            if (searchTerm.trim() === '') {
                // If search is cleared, reset to original data
                const actualMigrationId = migrationId;
                fetchEntityData(entityType, actualMigrationId, 1, getEntityTableState(entityType).rowsPerPage);
                return;
            }

            // Call the search function with entity type and search term
            handleEntitySearch(entityType, searchTerm);
        }, onRowsPerPageChange: async (newRowsPerPage) => {
            updateEntityTableState(entityType, {
                rowsPerPage: newRowsPerPage,
                currentPage: 0
            });

            const actualMigrationId = migrationId;
            const entityState = getEntityTableState(entityType);

            // Check if there's an active filter and handle accordingly
            if (entityState.currentFilter === "passed") {
                try {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: true }));
                    const response = await migration.filter({
                        migrationId: actualMigrationId,
                        status: "SUCCESS",
                        page: 1,
                        pageSize: newRowsPerPage,
                        entityName: entityType
                    });

                    if (response?.data?.entity) {
                        const filteredRecords = transformNewDataFormat(response.data.entity);
                        setEntityTables(prev => ({
                            ...prev,
                            [entityType]: filteredRecords.filter(record => record.entityType === entityType)
                        }));

                        if (response.pagination) {
                            updateEntityTableState(entityType, {
                                currentPage: response.pagination.page > 0 ? response.pagination.page - 1 : 0,
                                totalItems: response.pagination.totalItems || 0
                            });
                        }
                    }
                } catch (error) {
                    console.error(`Error fetching filtered success records for ${entityType}:`, error);
                } finally {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: false }));
                }
            } else if (entityState.currentFilter === "failed") {
                try {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: true }));
                    const response = await migration.filter({
                        migrationId: actualMigrationId,
                        status: "FAILED",
                        page: 1,
                        pageSize: newRowsPerPage,
                        entityName: entityType
                    });

                    if (response?.data?.entity) {
                        const filteredRecords = transformNewDataFormat(response.data.entity);
                        setEntityTables(prev => ({
                            ...prev,
                            [entityType]: filteredRecords.filter(record => record.entityType === entityType)
                        }));

                        if (response.pagination) {
                            updateEntityTableState(entityType, {
                                currentPage: response.pagination.page > 0 ? response.pagination.page - 1 : 0,
                                totalItems: response.pagination.totalItems || 0
                            });
                        }
                    }
                } catch (error) {
                    console.error(`Error fetching filtered failed records for ${entityType}:`, error);
                } finally {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: false }));
                }
            } else {
                // No filter applied, fetch all records
                fetchEntityData(entityType, actualMigrationId, 1, newRowsPerPage);
            }
        },
        onFilterChange: async (filter) => {
            updateEntityTableState(entityType, {
                currentFilter: filter,
                currentPage: 0
            });

            const actualMigrationId = migrationId;

            // Make API request for filtered records
            if (filter === "passed") {
                try {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: true }));
                    const response = await migration.filter({
                        migrationId: actualMigrationId,
                        status: "SUCCESS",
                        page: 1,
                        pageSize: getEntityTableState(entityType).rowsPerPage,
                        entityName: entityType
                    });

                    if (response?.data?.entity) {
                        const filteredRecords = transformNewDataFormat(response.data.entity);
                        setEntityTables(prev => ({
                            ...prev,
                            [entityType]: filteredRecords.filter(record => record.entityType === entityType)
                        }));

                        // Update pagination if available
                        if (response.pagination) {
                            updateEntityTableState(entityType, {
                                currentPage: response.pagination.page > 0 ? response.pagination.page - 1 : 0,
                                totalItems: response.pagination.totalItems || 0
                            });
                        }
                    }
                } catch (error) {
                    console.error(`Error fetching filtered success records for ${entityType}:`, error);
                } finally {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: false }));
                }
            } else if (filter === "failed") {
                try {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: true }));
                    const response = await migration.filter({
                        migrationId: actualMigrationId,
                        status: "FAILED",
                        page: 1,
                        pageSize: getEntityTableState(entityType).rowsPerPage,
                        entityName: entityType
                    });

                    if (response?.data?.entity) {
                        const filteredRecords = transformNewDataFormat(response.data.entity);
                        setEntityTables(prev => ({
                            ...prev,
                            [entityType]: filteredRecords.filter(record => record.entityType === entityType)
                        }));

                        // Update pagination if available
                        if (response.pagination) {
                            updateEntityTableState(entityType, {
                                currentPage: response.pagination.page > 0 ? response.pagination.page - 1 : 0,
                                totalItems: response.pagination.totalItems || 0
                            });
                        }
                    }
                } catch (error) {
                    console.error(`Error fetching filtered error records for ${entityType}:`, error);
                } finally {
                    setEntityDataLoading(prev => ({ ...prev, [entityType]: false }));
                }
            } else if (filter === "all") {
                // Reset to original data by refetching all records for this entity
                fetchEntityData(entityType, actualMigrationId);
            }
        }
    });

    const getRowClass = (status) => {
        return (status === "success" || status === "SUCCESS") ? bseStyles.successRow : bseStyles.errorRow;
    };

    const getStatusClass = (status) => {
        return (status === "success" || status === "SUCCESS") ? bseStyles.successStatus : bseStyles.errorStatus;
    };

    const retryBatch = () => {
        setIsLoading(true);
        // Simulate retry operation
        setTimeout(() => {
            setIsLoading(false);
            alert("Batch retry initiated!");
        }, 2000);
    };
    const startLiveMigration = () => {
        // Get the plan ID from the migration state or search params
        const planId = migrationState.planId || new URLSearchParams(location.search).get("plan_id") || new URLSearchParams(location.search).get("planId")

        // Create a new URL with the plan_id parameter, ensuring no duplicate parameters
        let newUrl = `/data-migration`
        if (planId) {
            newUrl += `?plan_id=${planId}`
        }

        navigate(newUrl, {
            state: {
                tab: "5",
                isSample: false,
                forceStep5: true
            },
        })
    };

    const generatePieChartData = () => {
        // Use normalizedExecutionData for consistent data structure across all migration states
        const passedRecords = normalizedExecutionData.passed || 0;
        const failedRecords = normalizedExecutionData.failed || 0;
        
        // If both values are 0, show a default placeholder
        if (passedRecords === 0 && failedRecords === 0) {
            return [{ name: "No Data", value: 1, color: "#E0E0E0" }];
        }

        return [
            { name: "Passed", value: passedRecords, color: "#EF8963" },
            { name: "Failed", value: failedRecords, color: "#A43E18" }
        ].filter(item => item.value > 0); // Only show items with values > 0
    };

    const toggleRowExpand = (batchId, sourceId, recordId, entityType) => {
        const key = `${entityType || 'unknown'}_${sourceId}`;

        if (expandedRows[key]) {
            setExpandedRows(prev => ({ ...prev, [key]: false }));
        } else {
            setExpandedRows(prev => ({ ...prev, [key]: true }));
            setSubIsLoading(prev => ({ ...prev, [key]: true }));

            // Find the record with the given sourceId and entityType
            const entityRecords = entityTables[entityType] || [];
            const parentRecord = entityRecords.find(record =>
                (record.source_id === sourceId || record.sourceId === sourceId)
            );

            // Initialize active tab if dependents exist
            if (parentRecord && parentRecord.dependents && parentRecord.dependents.length > 0) {
                setActiveDependentTabs(prev => ({
                    ...prev,
                    [key]: 0
                }));

                // Get the first dependent type
                const firstDependent = parentRecord.dependents[0];
                const firstDependentType = Object.keys(firstDependent)[0];
                const dependentData = firstDependent[firstDependentType];

                if (Array.isArray(dependentData)) {
                    setSubTableData(prev => ({
                        ...prev,
                        [key]: dependentData
                    }));
                }
            } else if (parentRecord && parentRecord.children && parentRecord.children.length > 0) {
                // Use the children from our hierarchical data for backward compatibility
                setSubTableData(prev => ({
                    ...prev,
                    [key]: parentRecord.children
                }));
            }

            // Always set loading to false when done
            setSubIsLoading(prev => ({ ...prev, [key]: false }));
        }
    };

    // Extract execution data according to new format structure
    const normalizedExecutionData = {
        totalRecords: (executionData?.totalPassedRecords || 0) + (executionData?.totalFailedRecords || 0),
        passed: executionData?.count_status?.passed_records || executionData?.totalPassedRecords || 0,
        recordsFailed: executionData?.totalFailedRecords || 0,
        migrated: (executionData?.count_status?.passed_records || executionData?.totalPassedRecords || 0) + (executionData?.count_status?.failed_records || executionData?.totalFailedRecords || 0),
        failed: executionData?.count_status?.failed_records || executionData?.totalFailedRecords || 0,
        name: 'Execution',
        startTime: executionData.started_at,
        endTime: executionData.updated_at,
        status: executionData.status || 'COMPLETED'
    };

    // Function to manually refresh data
    const handleManualRefresh = async () => {
        const actualMigrationId = migrationId;
        if (actualMigrationId) {
            setIsLoading(true);
            try {
                // Fetch migration status and entities
                await fetchMigrationStatus(actualMigrationId);

                // Fetch data for all entities
                if (migrationEntities.length > 0) {
                    for (const entityName of migrationEntities) {
                        await fetchEntityData(entityName, actualMigrationId);
                    }
                }
            } catch (error) {
                console.error('Error during manual refresh:', error);
            } finally {
                setIsLoading(false);
            }
        }
    }; return (
        <div style={{ padding: "10px" }}>
            {/* Add CSS animation for refresh icon */}
            <style>
                {`
                    @keyframes spin {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }
                `}
            </style>

            {isLoading ? (
                <LoaderSpinner />
            ) : (
                <>
                    {/* Moved help (How to resolve errors) section to top */}
                    <div style={{ display: "flex", justifyContent: "flex-end", padding: "0 12px", marginBottom: "10px" }}>
                        <div className={styles.dFlex} style={{ gap: "6px", alignItems: "center", cursor: "pointer" }} onClick={() => displayArticle("How to resolve errors")}>
                            <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                            <span className={globalStyles.guideName}>How to resolve errors</span>
                        </div>
                    </div>

                    <div className={styles.dFlex} style={{ gap: "20px", marginBottom: "30px", padding: "0 12px" }}>
                        <div className={styles.statsCard}>
                            <div className={globalStyles.selectionName}>EXECUTION STATS</div>
                            <div className={styles.statsContent}>
                                <div className={styles.metrics}>
                                    <div
                                        className={styles.statusBadge}
                                        style={{
                                            backgroundColor:
                                                normalizedExecutionData.failed > 0 ? "#A43E18" : "#EF8963",
                                            color: normalizedExecutionData.failed > 0 ? "#ffffff" : "#000000",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            gap: "8px",
                                            minWidth: normalizedExecutionData.status === "IN_PROGRESS" ? "140px" : "auto",
                                            padding: "8px 12px",
                                            whiteSpace: "nowrap"
                                        }}
                                    >
                                        {normalizedExecutionData.status === "IN_PROGRESS" && (
                                            <div
                                                style={{
                                                    width: "14px",
                                                    height: "14px",
                                                    border: "2px solid rgba(255, 255, 255, 0.3)",
                                                    borderTop: "2px solid #ffffff",
                                                    borderRadius: "50%",
                                                    animation: "spin 1s linear infinite",
                                                    flexShrink: 0
                                                }}
                                            />
                                        )}
                                        {normalizedExecutionData.status || "UNKNOWN"}
                                    </div>



                                    <div className={styles.metricItem}>
                                        <div className={styles.metricNumber}>
                                            {String(normalizedExecutionData.totalRecords).padStart(2, "0")}
                                        </div>
                                        <div className={styles.metricLabel}>
                                            <span className={`${styles.dot} ${styles.migrationDot}`}></span>
                                            <span className={styles.dotName}>Total</span>
                                            {availableEntities && availableEntities.length > 0 && (
                                                <span className={styles.dotName} style={{ margin: "0 2px", fontWeight: 500, color: "#333" }}>
                                                    {availableEntities.map(e => e.charAt(0).toUpperCase() + e.slice(1)).join(", ")}
                                                </span>
                                            )}
                                            <span className={styles.dotName}>Processed</span>
                                        </div>
                                    </div>

                                    <div className={styles.metricItem}>
                                        <div className={styles.metricNumber}>
                                            {String(normalizedExecutionData.recordsFailed).padStart(2, "0")}
                                        </div>
                                         <div className={styles.metricLabel}>
                                            <span className={`${styles.dot} ${styles.templateDot}`}></span>
                                            <span className={styles.dotName}>Total </span>
                                            {availableEntities && availableEntities.length > 0 && (
                                                <span className={styles.dotName} style={{ margin: "0 2px", fontWeight: 500, color: "#333" }}>
                                                    {availableEntities.map(e => e.charAt(0).toUpperCase() + e.slice(1)).join(", ")}
                                                </span>
                                            )}
                                            <span className={styles.dotName}>Failed</span>
                                        </div>
                                    </div>
                                </div>

                                <div className={styles.chartContainer}>
                                    <ResponsiveContainer width="100%">
                                        <PieChart>
                                            <Pie
                                                data={generatePieChartData()}
                                                cx="50%"
                                                cy="50%"
                                                innerRadius={60}
                                                outerRadius={80}
                                                paddingAngle={0}
                                                dataKey="value"
                                                startAngle={90}
                                                endAngle={-270}
                                            >
                                                {generatePieChartData().map((entry, index) => (
                                                    <Cell key={`cell-${index}`} fill={entry.color} />
                                                ))}
                                            </Pie>
                                        </PieChart>
                                    </ResponsiveContainer>
                                </div>
                            </div>
                        </div>

                        <div className={styles.summaryCard}>
                            <div className={styles.dFlex} style={{ justifyContent: "space-between", marginBottom: "20px" }}>
                                <div className={globalStyles.selectionName}>EXECUTION SUMMARY</div>
                            </div>

                            <div className={styles.summaryContent}>
                                {migrationData && (
                                    <>
                                        <div className={styles.summaryRow}>
                                            <span className={globalStyles.poppinsHeaderStyle}>Migration name</span>
                                            <span className={globalStyles.interSummaryStyle}>{migrationData.migrationName}</span>
                                        </div>
                                        <div className={styles.summaryRow}>
                                            <span className={globalStyles.poppinsHeaderStyle}>Migration ID</span>
                                            <span className={globalStyles.interSummaryStyle}>{migrationData.migrationId}</span>
                                        </div>
                                        <div className={styles.summaryRow}>
                                            <span className={globalStyles.poppinsHeaderStyle}>Template used</span>
                                            <span className={globalStyles.interSummaryStyle}>{migrationData.templateUsed}</span>
                                        </div>
                                        <div className={styles.summaryRow}>
                                            <span className={globalStyles.poppinsHeaderStyle}>Source to Target</span>
                                            <span className={globalStyles.interSummaryStyle}>{migrationData.sourcePlatform} → {migrationData.targetPlatform}</span>
                                        </div>
                                    </>
                                )}
                                <div className={styles.summaryRow}>
                                    <span className={globalStyles.poppinsHeaderStyle}> Processed Records</span>
                                    <span className={globalStyles.interSummaryStyle}>{normalizedExecutionData.migrated}</span>
                                </div>
                                {/* <div className={styles.summaryRow}>
                                    <span className={globalStyles.poppinsHeaderStyle}>Total Processed </span>
                                    <span className={globalStyles.interSummaryStyle}>{normalizedExecutionData.totalRecords}</span>
                                </div> */}
                                <div className={styles.summaryRow}>
                                    <span className={globalStyles.poppinsHeaderStyle}> Failed Records</span>
                                    <span className={globalStyles.interSummaryStyle}>
                                        {normalizedExecutionData.failed}
                                    </span>
                                </div>
                                <div className={styles.summaryRow}>
                                    <span className={globalStyles.poppinsHeaderStyle}>Start time</span>
                                    <span className={globalStyles.interSummaryStyle}>
                                        {formatTimestamp(normalizedExecutionData.startTime)}
                                    </span>
                                </div>
                                <div className={styles.summaryRow}>
                                    <span className={globalStyles.poppinsHeaderStyle}>End time</span>
                                    <span className={globalStyles.interSummaryStyle}>
                                        {formatTimestamp(normalizedExecutionData.endTime)}
                                    </span>
                                </div>
                                {/* New Tag Name row */}
                                <div className={styles.summaryRow}>
                                    <span className={globalStyles.poppinsHeaderStyle}>Tag name</span>
                                    <span className={globalStyles.interSummaryStyle}>
                                        {(() => {
                                            // First try migrationData
                                            if (migrationData?.tagName) return migrationData.tagName;
                                            
                                            // Then try localStorage sampleMigrationData
                                            try {
                                                const sampleData = localStorage.getItem('sampleMigrationData');
                                                if (sampleData) {
                                                    const parsed = JSON.parse(sampleData);
                                                    if (parsed.tag) return parsed.tag;
                                                }
                                            } catch (e) {
                                                // Failed to parse localStorage data
                                            }
                                            
                                            const directTag = localStorage.getItem('migrationTag');
                                            return directTag || '-';
                                        })()}
                                    </span>
                                </div>
                                {Object.keys(sourceQueryParams).length > 0 && (
                                    <div className={styles.summaryRow}>
                                        <span className={globalStyles.poppinsHeaderStyle}>Query Params</span>
                                        <span className={globalStyles.interSummaryStyle}>
                                            <Tooltip 
                                                title={
                                                    <div style={{ fontSize: "12px", maxWidth: "400px", padding: "8px" }}>
                                                        <div>
                                                            <div style={{ fontWeight: "bold", marginBottom: "8px", color: "#EA5822" }}>
                                                                Source Query Parameters ({Object.keys(sourceQueryParams).length}):
                                                            </div>
                                                            {Object.entries(sourceQueryParams).map(([key, value]) => (
                                                                <div key={key} style={{ 
                                                                    marginBottom: "6px", 
                                                                    padding: "4px 8px", 
                                                                    backgroundColor: "rgba(234, 88, 34, 0.1)", 
                                                                    borderRadius: "4px",
                                                                    borderLeft: "3px solid #EA5822"
                                                                }}>
                                                                    <strong style={{ color: "#EA5822" }}>{key}:</strong> 
                                                                    <span style={{ marginLeft: "8px", fontFamily: "monospace" }}>
                                                                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                                                    </span>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                }
                                                arrow
                                                placement="left"
                                                enterDelay={0}
                                                enterNextDelay={0}
                                                leaveDelay={200}
                                                componentsProps={{
                                                    tooltip: {
                                                        sx: {
                                                            bgcolor: 'rgba(0, 0, 0, 0.95)',
                                                            color: 'white',
                                                            fontSize: '12px',
                                                            maxWidth: '450px !important',
                                                            '& .MuiTooltip-arrow': {
                                                                color: 'rgba(0, 0, 0, 0.95)',
                                                            },
                                                        },
                                                    },
                                                }}
                                            >
                                                <div 
                                                    style={{ display: "flex", alignItems: "center", gap: "6px", cursor: "pointer" }}
                                                    onClick={() => setShowQueryParams(!showQueryParams)}
                                                >
                                                    <EyeIcon style={{ width: "16px", height: "16px", color: "#EA5822" }} />
                                                    <span style={{ fontSize: "14px", color: "#333" }}>
                                                        {`${Object.keys(sourceQueryParams).length} params`}
                                                    </span>
                                                </div>
                                            </Tooltip>
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div style={{ padding: "20px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                        {/* Refresh button on the left */}
                        <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                            <Tooltip title="Refresh" arrow>
                                <button
                                    onClick={handleManualRefresh}
                                    disabled={isLoading}
                                    className={globalStyles.mainButton}
                                    style={{
                                        padding: "12px 24px",
                                        fontSize: "16px",
                                        fontWeight: "500",
                                        opacity: isLoading ? 0.7 : 1,
                                        cursor: isLoading ? "not-allowed" : "pointer",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        gap: "8px",
                                        margin: "0"
                                    }}
                                >
                                    <RefreshIcon
                                        style={{
                                            width: "16px",
                                            height: "16px",
                                            animation: isLoading ? "spin 1s linear infinite" : "none"
                                        }}
                                    />
                                    {isLoading ? "Refreshing..." : "Refresh"}
                                </button>
                            </Tooltip>
                            <span style={{ 
                                fontSize: "16px", 
                                color: "#666", 
                                fontStyle: "inter",
                                marginLeft: "10px",
                                display: normalizedExecutionData.status === "IN_PROGRESS" ? "inline" : "none"
                            }}>
                                AUTO-REFRESHES - CLICK ONLY IF NEEDED
                            </span>
                        </div>

                        {/* Terminate and Retry buttons on the right */}
                        <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                            <Tooltip title="Terminate" arrow>
                                <button
                                    className={globalStyles.mainButton}
                                    onClick={() => setShowTerminateConfirm(true)}
                                    disabled={terminateLoading}
                                    style={{
                                        padding: "12px 24px",
                                        fontSize: "16px",
                                        fontWeight: "500",
                                        opacity: terminateLoading ? 0.7 : 1,
                                        cursor: terminateLoading ? "not-allowed" : "pointer",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        gap: "8px",
                                        margin: "0",
                                        border: "none",
                                        borderRadius: 4,
                                        background: "#A43E18",
                                        color: "#fff",
                                        transition: "background 0.2s"
                                    }}
                                    onMouseOver={e => { if (!terminateLoading) e.currentTarget.style.background = "#7a2d13"; }}
                                    onMouseOut={e => { if (!terminateLoading) e.currentTarget.style.background = "#A43E18"; }}
                                >
                                    <StopIcon
                                        style={{
                                            width: "16px",
                                            height: "16px"
                                        }}
                                    />
                                    {terminateLoading ? "Terminating..." : "Terminate"}
                                </button>
                            </Tooltip>

                            <Tooltip title="Retry" arrow>
                                <button
                                    onClick={() => setShowRetryConfirm(true)}
                                    disabled={isLoading}
                                    className={globalStyles.mainButton}
                                    style={{
                                        padding: "12px 24px",
                                        fontSize: "16px",
                                        fontWeight: "500",
                                        opacity: isLoading ? 0.7 : 1,
                                        cursor: isLoading ? "not-allowed" : "pointer",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        gap: "8px",
                                        margin: "0"
                                    }}
                                >
                                    <svg
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                    {isLoading ? "Retrying..." : "Retry"}
                                </button>
                            </Tooltip>
                        </div>

                        {/* Retry Confirmation Dialog */}
                        {showRetryConfirm && (
                            <Dialog open={showRetryConfirm} maxWidth="md" onClose={() => setShowRetryConfirm(false)}>
                                <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>
                                    <div
                                        style={{
                                            display: "flex",
                                            width: "450px",
                                            padding: "20px 30px 30px 30px",
                                            flexDirection: "column",
                                            alignItems: "flex-start",
                                            gap: "15px",
                                            borderRadius: "10px",
                                            background: "#170903",
                                            position: "relative",
                                        }}
                                    >
                                        {/* Close button */}
                                        <button
                                            onClick={() => setShowRetryConfirm(false)}
                                            style={{
                                                position: "absolute",
                                                top: "20px",
                                                right: "20px",
                                                background: "none",
                                                border: "none",
                                                color: "#fff",
                                                fontSize: "24px",
                                                cursor: "pointer",
                                                padding: "0",
                                                width: "30px",
                                                height: "30px",
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                            }}
                                        >
                                            ×
                                        </button>

                                        {/* Header */}
                                        <h2
                                            style={{
                                                color: "#EA5822",
                                                fontFamily: "Poppins",
                                                fontSize: "14px",
                                                fontStyle: "normal",
                                                fontWeight: "700",
                                                lineHeight: "24px",
                                                margin: "0",
                                                textTransform: "uppercase",
                                                letterSpacing: "0.5px",
                                            }}
                                        >
                                            CONFIRM RETRY
                                        </h2>

                                        {/* Message */}
                                        <p
                                            style={{
                                                color: "#F8F8F7",
                                                fontFamily: "Inter",
                                                fontSize: "14px",
                                                fontStyle: "normal",
                                                fontWeight: "400",
                                                lineHeight: "24px",
                                                margin: "0",
                                            }}
                                        >
                                            Are you sure you want to retry this migration?
                                        </p>

                                        {/* Buttons */}
                                        <div
                                            style={{
                                                display: "flex",
                                                gap: "10px",
                                                alignSelf: "flex-end",
                                                marginTop: "5px",
                                            }}
                                        >
                                            <button
                                                onClick={() => setShowRetryConfirm(false)}
                                                style={{
                                                    display: "flex",
                                                    height: "40px",
                                                    padding: "8px 20px",
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    gap: "10px",
                                                    minWidth: "100px",
                                                    borderRadius: "3px",
                                                    background: "transparent",
                                                    color: "#F8F8F7",
                                                    textAlign: "center",
                                                    fontFamily: "Inter",
                                                    fontSize: "16px",
                                                    fontStyle: "normal",
                                                    fontWeight: "500",
                                                    lineHeight: "24px",
                                                    border: "1px solid #F8F8F7",
                                                    cursor: "pointer",
                                                    transition: "all 0.3s ease",
                                                }}
                                                onMouseEnter={(e) => {
                                                    e.target.style.backgroundColor = "#F8F8F7";
                                                    e.target.style.color = "#170903";
                                                }}
                                                onMouseLeave={(e) => {
                                                    e.target.style.backgroundColor = "transparent";
                                                    e.target.style.color = "#F8F8F7";
                                                }}
                                            >
                                                Cancel
                                            </button>

                                            <button
                                                onClick={() => {
                                                    setShowRetryConfirm(false);
                                                    handleRetry();
                                                }}
                                                disabled={isLoading}
                                                style={{
                                                    display: "flex",
                                                    height: "40px",
                                                    padding: "8px 20px",
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    gap: "10px",
                                                    minWidth: "100px",
                                                    borderRadius: "3px",
                                                    background: "#EF8963",
                                                    color: "#170903",
                                                    textAlign: "center",
                                                    fontFamily: "Inter",
                                                    fontSize: "16px",
                                                    fontStyle: "normal",
                                                    fontWeight: "500",
                                                    lineHeight: "24px",
                                                    border: "none",
                                                    cursor: isLoading ? "not-allowed" : "pointer",
                                                    opacity: isLoading ? 0.7 : 1,
                                                    transition: "background-color 0.3s ease",
                                                }}
                                                onMouseEnter={(e) => {
                                                    if (!isLoading) {
                                                        e.target.style.backgroundColor = "#EFEEED";
                                                    }
                                                }}
                                                onMouseLeave={(e) => {
                                                    if (!isLoading) {
                                                        e.target.style.backgroundColor = "#EF8963";
                                                    }
                                                }}
                                            >
                                                {isLoading ? "Retrying..." : "Yes, Retry"}
                                            </button>
                                        </div>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        )}

                        {/* Terminate Confirmation Dialog */}
                        {showTerminateConfirm && (
                            <Dialog open={showTerminateConfirm} maxWidth="md" onClose={() => setShowTerminateConfirm(false)}>
                                <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>
                                    <div
                                        style={{
                                            display: "flex",
                                            width: "450px",
                                            padding: "20px 30px 30px 30px",
                                            flexDirection: "column",
                                            alignItems: "flex-start",
                                            gap: "15px",
                                            borderRadius: "10px",
                                            background: "#170903",
                                            position: "relative",
                                        }}
                                    >
                                        {/* Close button */}
                                        <button
                                            onClick={() => setShowTerminateConfirm(false)}
                                            style={{
                                                position: "absolute",
                                                top: "20px",
                                                right: "20px",
                                                background: "none",
                                                border: "none",
                                                color: "#fff",
                                                fontSize: "24px",
                                                cursor: "pointer",
                                                padding: "0",
                                                width: "30px",
                                                height: "30px",
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                            }}
                                        >
                                            ×
                                        </button>

                                        {/* Header */}
                                        <h2
                                            style={{
                                                color: "#EA5822",
                                                fontFamily: "Poppins",
                                                fontSize: "14px",
                                                fontStyle: "normal",
                                                fontWeight: "700",
                                                lineHeight: "24px",
                                                margin: "0",
                                                textTransform: "uppercase",
                                                letterSpacing: "0.5px",
                                            }}
                                        >
                                            CONFIRM TERMINATE
                                        </h2>

                                        {/* Message */}
                                        <p
                                            style={{
                                                color: "#F8F8F7",
                                                fontFamily: "Inter",
                                                fontSize: "14px",
                                                fontStyle: "normal",
                                                fontWeight: "400",
                                                lineHeight: "24px",
                                                margin: "0",
                                            }}
                                        >
                                            Are you sure you want to terminate this migration?
                                        </p>

                                        {/* Buttons */}
                                        <div
                                            style={{
                                                display: "flex",
                                                gap: "10px",
                                                alignSelf: "flex-end",
                                                marginTop: "5px",
                                            }}
                                        >
                                            <button
                                                onClick={() => setShowTerminateConfirm(false)}
                                                style={{
                                                    display: "flex",
                                                    height: "40px",
                                                    padding: "8px 20px",
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    gap: "10px",
                                                    minWidth: "100px",
                                                    borderRadius: "3px",
                                                    background: "transparent",
                                                    color: "#F8F8F7",
                                                    textAlign: "center",
                                                    fontFamily: "Inter",
                                                    fontSize: "16px",
                                                    fontStyle: "normal",
                                                    fontWeight: "500",
                                                    lineHeight: "24px",
                                                    border: "1px solid #F8F8F7",
                                                    cursor: "pointer",
                                                    transition: "all 0.3s ease",
                                                }}
                                                onMouseEnter={(e) => {
                                                    e.target.style.backgroundColor = "#F8F8F7";
                                                    e.target.style.color = "#170903";
                                                }}
                                                onMouseLeave={(e) => {
                                                    e.target.style.backgroundColor = "transparent";
                                                    e.target.style.color = "#F8F8F7";
                                                }}
                                            >
                                                Cancel
                                            </button>

                                            <button
                                                onClick={() => {
                                                    setShowTerminateConfirm(false);
                                                    handleTerminate();
                                                }}
                                                disabled={terminateLoading}
                                                style={{
                                                    display: "flex",
                                                    height: "40px",
                                                    padding: "8px 20px",
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    gap: "10px",
                                                    minWidth: "100px",
                                                    borderRadius: "3px",
                                                    background: "#EF8963",
                                                    color: "#170903",
                                                    textAlign: "center",
                                                    fontFamily: "Inter",
                                                    fontSize: "16px",
                                                    fontStyle: "normal",
                                                    fontWeight: "500",
                                                    lineHeight: "24px",
                                                    border: "none",
                                                    cursor: terminateLoading ? "not-allowed" : "pointer",
                                                    opacity: terminateLoading ? 0.7 : 1,
                                                    transition: "background-color 0.3s ease",
                                                }}
                                                onMouseEnter={(e) => {
                                                    if (!terminateLoading) {
                                                        e.target.style.backgroundColor = "#EFEEED";
                                                    }
                                                }}
                                                onMouseLeave={(e) => {
                                                    if (!terminateLoading) {
                                                        e.target.style.backgroundColor = "#EF8963";
                                                    }
                                                }}
                                            >
                                                {terminateLoading ? "Terminating..." : "Yes, Terminate"}
                                            </button>
                                        </div>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        )}


                        <style jsx>{`
                            @keyframes spin {
                                0% { transform: rotate(0deg); }
                                100% { transform: rotate(360deg); }
                            }
                        `}</style>
                    </div>

                    {/* Render separate table for each entity type */}
                    {migrationEntities.length > 0 ? (
                        migrationEntities.map((entityType) => {
                            const entityState = getEntityTableState(entityType);
                            const entityHandlers = createEntityHandlers(entityType);
                            const isEntityLoading = entityDataLoading[entityType];
                            const entityData = entityTables[entityType] || [];

                            return (
                                <div
                                    key={entityType}
                                    className={bseStyles.entityTableContainer}
                                    data-entity={entityType}
                                    style={{ marginBottom: "40px" }}
                                >
                                    {isEntityLoading ? (
                                        <div style={{ textAlign: 'center', padding: '20px' }}>
                                            <LoaderSpinner />
                                        </div>
                                    ) : (<div className={bseStyles.entityTableContent}>                                            <RecordsTable
                                        records={entityData}
                                        currentPage={entityState.currentPage}
                                        rowsPerPage={entityState.rowsPerPage}
                                        currentFilter={entityState.currentFilter}
                                        expandedRows={expandedRows}
                                        toggleRowExpand={toggleRowExpand}
                                        subIsLoading={subIsLoading}
                                        subTableData={subTableData}
                                        formatTimestamp={formatTimestamp}
                                        getRowClass={getRowClass}
                                        getStatusClass={getStatusClass}
                                        activeDependentTabs={activeDependentTabs}
                                        toggleTab={toggleTab} onRetryBatch={retryBatch}
                                        onPageChange={entityHandlers.onPageChange}
                                        onRowsPerPageChange={entityHandlers.onRowsPerPageChange}
                                        onFilterChange={entityHandlers.onFilterChange}
                                        onSearch={entityHandlers.onSearch}
                                        entityType={entityType}
                                        totalItems={entityState.totalItems}
                                        migrationId={migrationId}
                                    />
                                    </div>
                                    )}
                                </div>
                            );
                        })
                    ) : (
                        <div style={{ textAlign: "center", padding: "40px 20px" }}>
                            <div style={{
                                fontSize: "18px",
                                color: "#666",
                                marginBottom: "10px",
                                fontFamily: "Inter, sans-serif"
                            }}>
                                No Entities Found
                            </div>
                            <span className={globalStyles.interStyle} style={{ color: "#999" }}>
                                No entities found for the selected migration.
                            </span>
                        </div>
                    )}                </>
            )}
            <ToastContainer />
        </div>
    );
};

// PropTypes validation
MigrationSummary.propTypes = {
    migrationId: PropTypes.string.isRequired,
    isPolling: PropTypes.bool
};

export default MigrationSummary;