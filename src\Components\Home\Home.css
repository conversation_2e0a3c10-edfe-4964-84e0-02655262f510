#root {
    /*height: 100vh;*/
    overflow: scroll;
  }

.banner-container {
  position: relative;
  width: 100%;
  height: 35vh;
  min-height: 140px;
  max-height: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover; 
  object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
}

.welcome {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  position: relative;
  z-index: 1;
  padding: 40px 0;
}

.welcome-msg {
  color: #EA5822;
  line-height: 24px;
  font-size: 48px;
  font-family: Poppins;
  font-weight: 600;
}

.main-section {
  margin-left: 220px;
  padding: 0;
  flex: 1;
  transition: margin-left 0.3s ease;
  background-color: #ffff;
  height: 100vh;
}

.main-section.expanded {
  margin-left: 85px;
}

.main-section.guide-bg {
  position: relative;
}

.main-section.guide-bg::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  pointer-events: none;
}

.welcome-text {
  color: #170903;
  text-align: center;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  margin-top: 15px;
}

.main-button {
  margin-top: 15px;
  width: 300px;
  padding: 10px 20px;
  background-color: #EF8963;
  color: #170903;
  border: none;
  border-radius: 5px;
  font-family: "Inter";
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}
.main-button:hover{
    background-color: #ec9d7f;
}

.divider {
  border: 0.5px solid #dcdad9;
  margin-bottom: 20px;
}

.search-bar-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding: 10px;
}

.search-bar {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  margin-right: 20px;
}

.search-icon {
  position: absolute;
  left: 10px;
  width: 20px;
  height: 20px;
  color: gray;
  padding-bottom: 10px;
}

.search-input {
  width: 80px;
  padding: 10px 12px 10px 40px;
  border-radius: 7px;
  background-color: transparent;
  font-size: 14px;
  box-shadow: none;
}
