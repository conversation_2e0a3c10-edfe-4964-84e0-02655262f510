import axios from "axios"
import { useContext, useEffect, useState } from "react"
import styles from "./Target-connection.module.css"
import globalStyles from "../../globalStyles.module.css"
import SourceTargetSelector from "../Source-target-selector/Source-target-selector"
import { CheckIcon, EyeIcon, EyeOffIcon } from "@heroicons/react/solid"
import { FormControl, MenuItem, Select } from "@mui/material"
import { Dialog, DialogContent } from "@mui/material"
import {
  getMigrationProviders,
  getTargetConnection,
  saveMigrationPlan,
  postActivity,
  updateConnDetails,
  validateSource,
} from "../../apiService"
import { MigrationContext } from "../Data-Migration"
import { ToastContainer, toast } from "react-toastify"
import { displayArticle, executeApi } from "../../../Helper/helper"
import LoaderSpinner from "../../loaderspinner"
import config from "../../../Config/config.json"
import { parseJsonFields } from "../../../Helper/helper"

const BUSINESS_LOGIC_API_URL = config.BUSINESS_LOGIC_API_URL

const httpOptions = {
  headers: {
    "Content-Type": "application/json",
    Authorization: "Basic YWRtaW5zZzpTZ3VzZXJANTQz",
    "Access-Control-Allow-Origin": "*",
    "ngrok-skip-browser-warning": "true", // To be removed when switching to actual middleware
  },
}

// Toast styles
const successToastStyle = { color: "#16A34A", fontWeight: 500 }
const errorToastStyle = { color: "#DC2626", fontWeight: 500 }

export default function TargetConnection({
  setSelectedTab,
  planId,
  setPlanId,
  templateName,
  migrationTarget,
  setMigrationTarget,
}) {
  const [email, setEmail] = useState(localStorage.getItem("email"))
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const sourceObjData = migrationState.sourceObjData
  const targetData = migrationState.targetData
  const [data, setData] = useState([])
  const [dropDownData, setDropDownData] = useState([])
  const [dropdownOptions, setDropdownOptions] = useState([])
  const [selectedSource, setSelectedSource] = useState(null)
  const [showModel, setShowModel] = useState(false)
  const [formData, setFormData] = useState({})
  const [isConnected, setIsConnected] = useState(false)
  const [openDialog, setOpenDialog] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isConfirming, setIsConfirming] = useState(false)
  const [connectionError, setConnectionError] = useState(false)
  const [visibleFields, setVisibleFields] = useState({})
  const [formValid, setFormValid] = useState(false)
  const [credentialsModified, setCredentialsModified] = useState(false)
  const [isTargetLoading, setIsTargetLoading] = useState(false)
  const [isEditable, setIsEditable] = useState(true)
  const [initialDataLoaded, setInitialDataLoaded] = useState(false)
  const [checked, setChecked] = useState(false)
  const [showResetDialog, setShowResetDialog] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState(null)

  const toggleVisibility = (fieldName) => {
    setVisibleFields((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }))
  }

  useEffect(() => {
    if (!targetData || Object.keys(targetData).length === 0) {
      setSelectedSource(null)
      setFormData({})
      setInitialDataLoaded(false)
      setIsConnected(false)
      setCredentialsModified(true)
      setIsEditable(true)
      setChecked(false)
      setConnectionStatus(null)
    } else if (targetData && targetData.target && !initialDataLoaded) {
      setSelectedSource(targetData.target)
      setFormData(targetData.formData || {})
      setIsConnected(true)
      setIsEditable(false)
      setCredentialsModified(false)
      setChecked(targetData.checked || false)
      setConnectionStatus("success")
      setInitialDataLoaded(true)
      if (targetData.target?.name) {
        getTargetConnectionFields(targetData.target.name)
      }
    }

    const fetchProviders = async () => {
      try {
        const configPlatforms = Object.entries(config.PLATFORMS_SUPPORTED).map(([key, platform]) => ({
          configKey: key,
          name: platform.name,
          url: platform.url
        }))
        
        const apiData = await getMigrationProviders()
        const mergedData = []
        
        configPlatforms.forEach(configPlatform => {
          const matchingApiData = apiData.find(apiItem => {
            if (apiItem.name === configPlatform.configKey || apiItem.name === configPlatform.name) {
              return true
            }
            
            const normalizedApiName = apiItem.name.toLowerCase()
              .replace(/\s+json$/i, '')
              .replace(/\s+api$/i, '')
              .replace(/\s+connector$/i, '')
              .replace(/\s+/g, '')
              .trim()
            
            const normalizedConfigKey = configPlatform.configKey.toLowerCase()
              .replace(/\s+/g, '')
              .trim()
            
            const normalizedConfigName = configPlatform.name.toLowerCase()
              .replace(/\s+/g, '')
              .trim()
            
            if (normalizedConfigKey === normalizedApiName || normalizedConfigName === normalizedApiName) {
              return true
            }
            
            return false
          })
          
          if (matchingApiData && matchingApiData.isTarget) {
            mergedData.push({
              ...matchingApiData,
              name: configPlatform.configKey,
              displayName: configPlatform.name,
              imgUrl: configPlatform.url,
            })
          }
        })
        
        const configKeys = configPlatforms.map(cp => cp.configKey.toLowerCase())
        const configNames = configPlatforms.map(cp => cp.name.toLowerCase())
        
        apiData.forEach(apiItem => {
          if (!apiItem.isTarget) {
            return
          }
          
          const normalizedApiName = apiItem.name.toLowerCase()
            .replace(/\s+json$/i, '')
            .replace(/\s+api$/i, '')
            .replace(/\s+connector$/i, '')
            .replace(/\s+/g, '')
            .trim()
          
          const isAlreadyIncluded = configKeys.includes(normalizedApiName) || 
                                   configNames.includes(normalizedApiName) ||
                                   mergedData.some(item => item.name === apiItem.name) ||
                                   mergedData.some(item => {
                                     const itemNormalized = item.name.toLowerCase().replace(/\s+/g, '').trim()
                                     return itemNormalized === normalizedApiName
                                   })
          
          if (!isAlreadyIncluded) {
            mergedData.push({
              ...apiItem,
              displayName: apiItem.name,
              imgUrl: apiItem.imgUrl || '/placeholder.svg'
            })
          }
        })
        
        setData(mergedData)
      } catch (error) {
        console.error("Failed to load migration providers:", error)
        const configPlatforms = Object.entries(config.PLATFORMS_SUPPORTED).map(([key, platform]) => ({
          name: key,
          displayName: platform.name,
          imgUrl: platform.url,
          isSource: false,
          isTarget: true,
          fields: []
        }))
        setData(configPlatforms)
      }
    }

    fetchProviders()
  }, [targetData, initialDataLoaded])

  useEffect(() => {
    if (selectedSource?.name) {
      getTargetConnectionFields(selectedSource?.name)
    }
  }, [selectedSource])

  useEffect(() => {
    if (isConnected && dropDownData.length > 0) {
      fetchOptions()
    }
    if (targetData?.dropdownOptions && dropDownData.length > 0) {
      setDropdownOptions(targetData.dropdownOptions)
    }
  }, [isConnected, dropDownData, targetData?.dropdownOptions])

  useEffect(() => {
    validateForm()
  }, [formData, selectedSource])

  const validateForm = () => {
    if (!selectedSource) {
      setFormValid(false)
      return
    }
    const requiredFields = selectedSource.fields?.filter((field) => !field.optional) || []
    const allRequiredFieldsFilled = requiredFields.every(
      (field) => formData[field.name] && formData[field.name].trim() !== "",
    )

    let dropdownsValid = true
    if (isConnected && dropDownData.length > 0) {
      dropDownData[0]?.fields.forEach((dropdown) => {
        if (!dropdown.optional && (!formData[dropdown.key] || formData[dropdown.key] === "")) {
          dropdownsValid = false
        }
      })
    }

    setFormValid(allRequiredFieldsFilled && (!isConnected || dropdownsValid))
  }

  const openSelector = () => {
    setIsTargetLoading(true)
    setTimeout(() => {
      setShowModel(true)
      setIsTargetLoading(false)
    }, 500)
  }

  const getConfigKey = (targetName) => {
    if (!targetName) return null
    
    const normalizedName = targetName.toLowerCase()
      .replace(/\s+json$/i, '')
      .replace(/\s+api$/i, '')
      .replace(/\s+connector$/i, '')
      .replace(/\s+/g, '')
      .trim()
    
    if (config.PLATFORMS_SUPPORTED[normalizedName]) {
      return normalizedName
    }
    
    if (config.PLATFORMS_SUPPORTED[targetName]) {
      return targetName
    }
    
    return normalizedName
  }

  const handleSelect = (selectedValue) => {
    const selectedData = data.find((item) => item.name === selectedValue)
    
    const configKey = getConfigKey(selectedValue)
    const configTarget = config.PLATFORMS_SUPPORTED[configKey]
    
    setMigrationTarget([selectedData.id])
    setSelectedSource(selectedData)
    localStorage.setItem("targetPlatform", selectedData.displayName || selectedData.name)
    getTargetConnectionFields(selectedData.name)
    setFormData({})
    setDropdownOptions([])
    setIsConnected(false)
    setConnectionError(false)
    setCredentialsModified(true)
    setIsEditable(true)
    setChecked(false)
    setVisibleFields({})
    setConnectionStatus(null)
    setMigrationState((prevState) => ({
      ...prevState,
      dataTypeData: {},
      dataMappingData: {},
      selectedObjectData: {},
      selectedEntityData: {},
      sourceExeRes: [],
      targetExeRes: [],
      sourceMapRes: [],
      sourceResAtt: [],
    }))
  }

  const getTargetConnectionFields = async (target) => {
    try {
      const data = await getTargetConnection(target)
      setDropDownData([data])
    } catch (error) {
      console.error("Failed to get data:", error)
    }
  }

  const extractSubdomain = (url) => {
    if (!url) return null
    const subdomainMatch = url.match(/^(?:https?:\/\/)?([^./]+)\./)
    return subdomainMatch ? subdomainMatch[1] : null
  }

  const extractSourceDomain = (url) => {
    try {
      const trimmedUrl = url.trim()
      const parsedUrl = new URL(trimmedUrl)
      return parsedUrl.hostname
    } catch (error) {
      console.error("Invalid URL:", error)
      return null
    }
  }

  const fetchOptions = async () => {
    if (
      !formData ||
      !formData.instance_url ||
      (selectedSource?.fields?.some((field) => field.name === "apikey") && !formData.apikey)
    ) {
      return
    }

    const domain = extractSourceDomain(formData.instance_url)
    if (!domain) {
      return
    }

    for (const [index, field] of dropDownData[0].fields.entries()) {
      if (!field?.dependent?.includes(index + 1)) {
        const queryParams = field.fieldExecutor.queryParams.map((param) => {
          if (param.key.startsWith("domain")) {
            if (dropDownData[0].domainType === "subDomain") {
              return { ...param, value: extractSubdomain(formData.instance_url) }
            } else if (dropDownData[0].domainType === "sourceDomain") {
              return { ...param, value: extractSourceDomain(formData.instance_url) }
            }
          } else if (param.key === "apikey") {
            return { ...param, value: formData.apikey }
          }
          return param
        })

        const domainUrl = queryParams.find((item) => item.key === "domain_url")?.value
        const apiKey = queryParams.find((item) => item.key === "apikey")?.value

        if (!domainUrl || !apiKey) {
          continue
        }

        try {
          const response = await axios.get(
            `${field.fieldExecutor.apiUrl}?domainUrl=${domainUrl}&apikey=${apiKey}`,
            httpOptions,
          )
          const parsedResponse = parseJsonFields(response.data)
          handleDropdownData(parsedResponse, index)
        } catch (err) {
          console.error("Error fetching workspace options:", err)
          handleDropdownData([], index)
        }
      }
    }
  }

  const handleDropdownData = (data, index) => {
    setDropdownOptions((prevState) => {
      const updatedState = [...prevState]
      if (Array.isArray(data)) {
        updatedState[index] = data
      } else if (typeof data === "object" && data !== null) {
        for (const key in data) {
          if (Array.isArray(data[key])) {
            updatedState[index] = data[key]
            break
          }
        }
      } else {
        updatedState[index] = []
      }
      return updatedState
    })
  }

  const connectToTarget = async (event) => {
    event.stopPropagation()
    event.preventDefault()

    const requiredFields = selectedSource.fields?.filter((field) => !field.optional) || []
    const missingFields = requiredFields.filter((field) => !formData[field.name] || formData[field.name].trim() === "")

    if (missingFields.length > 0) {
      toast.error("Please fill all required fields", {
        position: "top-right",
        style: {
          "--toastify-icon-color-error": "black",
          "--toastify-color-progress-error": "black",
        },
      })
      return
    }

    setIsLoading(true)
    try {
      const domain = extractSourceDomain(formData.instance_url)
      if (!domain) {
        throw new Error("Invalid URL format")
      }

      const tempKey = String(formData.apikey || "")
      const payload = {
        sourceName: selectedSource.name,
        domain: domain,
        domainUrl: domain,
        username: tempKey,
        queryParams: [],
      }

      if (formData.username && formData.password) {
        payload["username"] = formData.username
        payload["password"] = formData.password
      }

      const connPayload = {
        domain: domain,
        accountEmail: formData.username || formData.accountEmail || "",
        userEmail: email,
        providerName: selectedSource.name,
        isSource: false,
        isTarget: true,
      }

      await updateConnDetails(connPayload)

      setMigrationState((prevState) => ({
        ...prevState,
        targetData: {
          ...prevState.targetData,
          formData: formData,
          connectionStatus: "success",
        },
      }))

      await fetchOptions()
      setIsConnected(true)
      setConnectionError(false)
      setCredentialsModified(false)
      setIsEditable(false)
      setConnectionStatus("success")

      toast.success("Connected successfully!", {
        position: "top-right",
        style: successToastStyle,
      })
    } catch (error) {
      toast.error("Connection Failed! Please check your credentials and try again.", {
        position: "top-right",
        style: errorToastStyle,
      })
      setIsConnected(false)
      setConnectionError(true)
      setConnectionStatus("failed")
      setMigrationState((prevState) => ({
        ...prevState,
        targetData: {
          ...prevState.targetData,
          formData: formData,
          connectionStatus: "failed",
        },
      }))
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (e, fieldName) => {
    let newValue = e.target.value
    // Apply character limits without notifications
    if (fieldName === "instance_url" && newValue.length > 100) {
      newValue = newValue.slice(0, 100)
    } else if (fieldName === "username" && newValue.length > 50) {
      newValue = newValue.slice(0, 50)
    }

    const oldValue = formData[fieldName] || ""
    // Check if credentials are being cleared or modified
    if (["username", "password", "apikey", "instance_url"].includes(fieldName)) {
      if (newValue !== oldValue) {
        setCredentialsModified(true)
        setIsConnected(false)
        setConnectionStatus(null)
        setMigrationState((prevState) => ({
          ...prevState,
          targetData: {
            ...prevState.targetData,
            connectionStatus: null,
          },
        }))
      }
      // If URL or API key is cleared, reset dropdowns
      if ((fieldName === "instance_url" || fieldName === "apikey") && !newValue.trim()) {
        setDropdownOptions([]) // Clear dropdown options
        // Clear all dropdown values in formData
        setFormData((prevData) => {
          const newData = { ...prevData }
          if (dropDownData[0]?.fields) {
            dropDownData[0].fields.forEach((field) => {
              delete newData[field.key]
            })
          }
          return {
            ...newData,
            [fieldName]: newValue,
          }
        })
        return
      }
    }

    setFormData((prevData) => ({
      ...prevData,
      [fieldName]: newValue,
    }))
  }

  const handleReset = () => {
    setShowResetDialog(true)
  }

  const confirmReset = () => {
    setFormData({})
    setDropdownOptions([])
    setIsConnected(false)
    setConnectionError(false)
    setCredentialsModified(true)
    setIsEditable(true) // Enable editing after reset
    setConnectionStatus(null)
    setShowResetDialog(false)
    toast.info("All form fields have been reset", {
      position: "top-right",
      style: {
        "--toastify-icon-color-info": "black",
        "--toastify-color-progress-info": "black",
      },
    })
  }

  const cancelReset = () => {
    setShowResetDialog(false)
  }

  const handleCancelEdit = (event) => {
    // Prevent any default behavior and event propagation
    if (event) {
      event.preventDefault()
      event.stopPropagation()
    }
    // Restore previous state
    setIsEditable(false)
    setCredentialsModified(false)
    setIsConnected(true)
    setConnectionError(false)
    setConnectionStatus("success")
    // Restore form data to the last successfully connected state
    if (targetData && targetData.formData) {
      setFormData(targetData.formData)
    }
    // Restore dropdown options if available
    if (targetData && targetData.dropdownOptions) {
      setDropdownOptions(targetData.dropdownOptions)
    }
    // Show confirmation message
    toast.info("Changes cancelled, restored to previous connection", {
      position: "top-right",
      style: {
        "--toastify-icon-color-info": "black",
        "--toastify-color-progress-info": "black",
      },
    })
  }

  const moveToNextStep = async (event) => {
    event.preventDefault()
    setIsConfirming(true)

    if (credentialsModified || connectionStatus !== "success") {
      toast.error("Please validate your credentials before proceeding.", {
        position: "top-right",
        style: errorToastStyle,
      })
      setIsConfirming(false)
      return
    }

    if (dropDownData.length > 0) {
      const requiredDropdowns = dropDownData[0].fields.filter((field) => !field.optional)
      const missingDropdowns = requiredDropdowns.filter(
        (dropdown) => !formData[dropdown.key] || formData[dropdown.key] === "",
      )
      if (missingDropdowns.length > 0) {
        toast.error("Please select all required dropdown fields", {
          position: "top-right",
          style: errorToastStyle,
        })
        setIsConfirming(false)
        return
      }
    }

    setOpenDialog(true)
    // Show success toast notification
    toast.success(`Connected to ${selectedSource.name} profile!`, {
      position: "top-right",
      autoClose: 3000,
      style: successToastStyle,
    })

    // Save complete state for restoration when navigating back
    const obj = {
      target: selectedSource,
      formData: formData,
      isConnected: true,
      connectionStatus: "success",
      domainType: dropDownData[0]?.domainType,
      checked: checked,
      dropdownOptions: dropdownOptions,
    }

    setMigrationState((prevState) => ({
      ...prevState,
      targetData: obj,
    }))

    const activityPayload = {
      email: email,
      activity: "Source and Target connected",
    }
    postActivity(activityPayload)
    await saveMigration(obj)
    setTimeout(() => {
      setSelectedTab("3")
      setIsConfirming(false)
    }, 2000)
  }

  const saveMigration = async (obj) => {
    try {
      const payload = {
        plan_name: templateName,
        migration_target: migrationTarget,
        migration_objects: [],
        updatedAt: Date.now(),
        email_id: email,
        additional_details: {
          sourceObjData: sourceObjData,
          targetData: obj,
          dataTypeData: migrationState.dataTypeData,
          dataMappingData: migrationState.dataMappingData,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
        },
      }
      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
      }
      const res = await saveMigrationPlan(payload)
      if (res?.message === "plan created successfully" && res?.response?.id) {
        setPlanId(res.response.id)
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Something went wrong!", {
        position: "top-right",
        style: errorToastStyle,
      })
    }
  }

  const handleDependentData = async (index, selectedValue, dependent) => {
    for (let j = 0; j < dependent.length; j++) {
      const dependentIndex = dependent[j] - 1
      const fieldExecutor = dropDownData[0].fields[dependentIndex].fieldExecutor
      const queryParams = fieldExecutor.queryParams.map((param) => {
        if (param.key.startsWith("domain")) {
          if (dropDownData[0].domainType === "subDomain") {
            return { ...param, value: extractSubdomain(formData.instance_url) }
          } else if (dropDownData[0].domainType === "sourceDomain") {
            return { ...param, value: extractSourceDomain(formData.instance_url) }
          }
        } else if (param.key === "apikey") {
          return { ...param, value: formData.apikey }
        } else if (param.key.startsWith(dropDownData[0].fields[index].fieldName.toLowerCase())) {
          param.value = selectedValue
        }
        return param
      })
      const updatedExecutor = {
        ...fieldExecutor,
        queryParams,
      }
      try {
        const res = await executeApi(updatedExecutor)
        handleDropdownData(res, dependentIndex)
      } catch (err) {
        console.error("API execution failed:", err)
      }
    }
  }

  const handleEditMode = () => {
    setIsEditable(true)
    setConnectionError(false)
    setIsConnected(false)
    setCredentialsModified(true)
    setConnectionStatus(null)
    setDropdownOptions([]) // Clear only dropdown options, keep form data
  }

  return (
    <div>
      {isConfirming && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
            borderRadius: "8px",
          }}
        >
          <LoaderSpinner fullpage={false} />
        </div>
      )}
      <div className={styles.dFlex}>
        <div className={styles.section} style={{ width: "35%" }}>
          <div className={styles.targetGraphic}>
            <div className={styles.targetConnectorContainer}>
              {selectedSource ? (
                <div style={{ position: "relative" }}>
                  <img src="/assets/Target-desktop.png" alt="Target Connector" className={styles.targetGraphicImage} />
                  <div className={styles.selectedTargetLogoOverlay}>
                    <div className={styles.selectedTargetLogo}>
                      <img src={selectedSource.imgUrl || "/placeholder.svg"} alt={selectedSource.name} />
                    </div>
                  </div>
                </div>
              ) : (
                <img src="/assets/Targetconnector.png" alt="Target Connector" className={styles.targetGraphicImage} />
              )}
            </div>
          </div>
        </div>
        <div className={styles.section} style={{ width: "65%" }}>
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: "0" }}>
            <div className={globalStyles.selectionName}>SELECT A TARGET PLATFORM</div>
            <div
              style={{ display: "flex", alignItems: "center", cursor: "pointer" }}
              onClick={() => {
                displayArticle("What target platforms are supported?")
              }}
            >
              <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
              <span className={globalStyles.guideName}>What target platforms are supported?</span>
            </div>
          </div>
          {selectedSource ? (
            <div>
              <button
                className={globalStyles.mainButton}
                style={{
                  width: "100%",
                  marginTop: "20px",
                  backgroundColor: "#FFFFFF",
                  color: "#514742",
                  border: "1px solid #DCDAD9",
                  boxShadow: "2px 2px 5px 0 rgba(0, 0, 0, 0.10)",
                }}
                onClick={openSelector}
              >
                Reselect a target platform{" "}
              </button>
              <Dialog
                open={showModel}
                onClose={() => setShowModel(false)}
                PaperProps={{
                  sx: { width: "705px", maxWidth: "none", backgroundColor: "#170903", marginBottom: "20px" },
                }}
              >
                <DialogContent
                  sx={{ backgroundColor: "#170903", padding: "0", marginBottom: "30px", marginTop: "5px" }}
                >
                  <SourceTargetSelector
                    showModel={showModel}
                    setShowModel={setShowModel}
                    data={data
                      .filter((target) => target.isTarget && target.name.toLowerCase() !== "csv")
                      .map((target) => ({
                        src: target.imgUrl,
                        name: target.displayName,
                        value: target.name,
                      }))}
                    name="TARGET"
                    onSelect={handleSelect}
                  />
                </DialogContent>
              </Dialog>
              <div style={{ marginTop: "20px" }}>
                <div className={globalStyles.selectionName}>
                  CONNECT TO A {selectedSource.name.toUpperCase()} TARGET PROFILE
                </div>
                <div style={{ marginTop: "10px" }} className={styles.dFlex}>
                  <span className={globalStyles.poppinsHeaderStyle}>Enter the details</span>
                  <div
                    style={{ display: "flex", alignItems: "center", marginLeft: "auto", cursor: "pointer" }}
                    onClick={() => {
                      displayArticle(`How do I find my instance URL & ${selectedSource.name} API key?`)
                    }}
                  >
                    <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                    <span className={globalStyles.guideName}>
                      How do I find my instance URL & {selectedSource.name} API key?
                    </span>
                  </div>
                </div>
                <div>
                  <form onSubmit={connectToTarget}>
                    <div>
                      {selectedSource?.fields?.map((field) => (
                        <div key={field.name} className={styles.inputContainer}>
                          <input
                            className={`${styles.formControl} ${field.type === "password" ? styles.passwordInput : ""}`}
                            type={field.type === "password" && visibleFields[field.name] ? "text" : field.type}
                            placeholder={
                              field.placeholder
                                ? `${field.placeholder}${!field.optional ? "*" : ""}`
                                : `Enter value${!field.optional ? "*" : ""}`
                            }
                            value={formData[field.name] || ""}
                            onChange={(e) => handleInputChange(e, field.name)}
                            maxLength={field.name === "instance_url" ? 100 : field.name === "username" ? 50 : undefined}
                            disabled={!isEditable}
                            style={{
                              backgroundColor: !isEditable ? "#f5f5f5" : "#fff",
                              cursor: !isEditable ? "not-allowed" : "text",
                            }}
                          />
                          {field.type === "password" && isEditable && (
                            <span className={styles.eyeIconContainer} onClick={() => toggleVisibility(field.name)}>
                              {visibleFields[field.name] ? (
                                <EyeIcon className={styles.eyeIcon} />
                              ) : (
                                <EyeOffIcon className={styles.eyeIcon} />
                              )}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                    {connectionStatus === "success" ? (
                      <div>
                        <button
                          className={globalStyles.connectedSuccess}
                          style={{ width: "100%", marginTop: "20px" }}
                          onClick={(e) => e.preventDefault()}
                        >
                          Connected Successfully!{" "}
                          <CheckIcon
                            className="eye-icon"
                            style={{
                              color: "green",
                              marginLeft: "10px",
                            }}
                          />
                        </button>
                        {dropDownData[0]?.fields.map((dropdown, index) => (
                          <FormControl key={dropdown.key} fullWidth className={styles.customDropdownContainer}>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "end",
                                marginTop: "20px",
                                cursor: "pointer",
                              }}
                              onClick={() => {
                                displayArticle("What is a workspace?")
                              }}
                            >
                              <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                              <span className={globalStyles.guideName}>What is a workspace?</span>
                            </div>
                            <Select
                              key={index}
                              className={styles.customDropdownSelect}
                              value={formData[dropdown.key] || ""}
                              displayEmpty
                              renderValue={(selected) => {
                                if (!selected)
                                  return (
                                    <span style={{ color: "#746B68" }}>
                                      Select {dropdown.fieldName}
                                      {!dropdown.optional ? "*" : ""}
                                    </span>
                                  )
                                const selectedOption = dropdownOptions[index]?.find(
                                  (option) => option[dropdown.accessor] === selected,
                                )
                                return <span style={{ color: "#746B68" }}>{selectedOption?.[dropdown.attribute]}</span>
                              }}
                              onChange={(e) => {
                                handleInputChange(e, dropdown.key)
                                if (dropdown.dependent?.length > 0) {
                                  handleDependentData(index, e.target.value, dropdown.dependent)
                                }
                              }}
                            >
                              <MenuItem value="">Select {dropdown.fieldName}</MenuItem>
                              {dropdownOptions[index]?.map((option) => (
                                <MenuItem key={option[dropdown.accessor]} value={option[dropdown.accessor]}>
                                  {option[dropdown.attribute]}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        ))}
                        <div style={{ display: "flex", gap: "10px", marginTop: "20px" }}>
                          {!isEditable && (
                            <button
                              type="button"
                              className={styles.resetButton}
                              style={{
                                padding: "10px 20px",
                                borderRadius: "4px",
                                border: "1px solid #EA5822",
                                background: "white",
                                color: "#EA5822",
                                cursor: "pointer",
                                fontWeight: "500",
                                flex: "1",
                              }}
                              onClick={handleEditMode}
                            >
                              Edit
                            </button>
                          )}
                          {isEditable &&
                            credentialsModified &&
                            targetData &&
                            targetData.formData &&
                            Object.keys(targetData.formData).length > 0 && (
                              <button
                                type="button"
                                className={styles.resetButton}
                                style={{
                                  padding: "10px 20px",
                                  borderRadius: "4px",
                                  border: "1px solid #6B7280",
                                  background: "white",
                                  color: "#6B7280",
                                  cursor: "pointer",
                                  fontWeight: "500",
                                  flex: "1",
                                }}
                                onClick={(e) => handleCancelEdit(e)}
                              >
                                Cancel
                              </button>
                            )}
                          <button
                            type="button"
                            className={styles.resetButton}
                            style={{
                              padding: "10px 20px",
                              borderRadius: "4px",
                              border: "1px solid #EA5822",
                              background: "white",
                              color: "#EA5822",
                              cursor: "pointer",
                              fontWeight: "500",
                              flex: "1",
                            }}
                            onClick={handleReset}
                          >
                            Reset
                          </button>
                          <button
                            className={globalStyles.mainButton}
                            style={{ flex: "3" }}
                            onClick={moveToNextStep}
                            disabled={connectionStatus !== "success" || credentialsModified}
                          >
                            Confirm & continue
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div style={{ display: "flex", gap: "10px", marginTop: "20px" }}>
                        {isEditable &&
                          credentialsModified &&
                          targetData &&
                          targetData.formData &&
                          Object.keys(targetData.formData).length > 0 && (
                            <button
                              type="button"
                              className={styles.resetButton}
                              style={{
                                padding: "10px 20px",
                                borderRadius: "4px",
                                border: "1px solid #6B7280",
                                background: "white",
                                color: "#6B7280",
                                cursor: "pointer",
                                fontWeight: "500",
                                flex: "1",
                              }}
                              onClick={(e) => handleCancelEdit(e)}
                            >
                              Cancel
                            </button>
                          )}
                        <button
                          type="button"
                          className={styles.resetButton}
                          style={{
                            padding: "10px 20px",
                            borderRadius: "4px",
                            border: "1px solid #EA5822",
                            background: "white",
                            color: "#EA5822",
                            cursor: "pointer",
                            fontWeight: "500",
                            flex: "1",
                          }}
                          onClick={handleReset}
                        >
                          Reset
                        </button>
                        <button
                          type="button"
                          className={globalStyles.mainButton}
                          style={{ flex: "3", marginTop: "0", width: "90%" }}
                          onClick={connectToTarget}
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <div className={styles.loaderContainer}>
                              <div className={styles.loader}></div>
                              <span>Connecting...</span>
                            </div>
                          ) : connectionStatus === "failed" ? (
                            "Reconnect"
                          ) : (
                            "Connect"
                          )}
                        </button>
                      </div>
                    )}
                  </form>
                </div>
              </div>
            </div>
          ) : (
            <div>
              <button
                className={globalStyles.mainButton}
                style={{ width: "100%", marginTop: "20px" }}
                onClick={openSelector}
              >
                {isTargetLoading ? (
                  <div className={globalStyles.loaderContainer}>
                    <div className={globalStyles.loader}></div>
                  </div>
                ) : (
                  "Select a target platform"
                )}
              </button>
              <Dialog
                open={showModel}
                onClose={() => setShowModel(false)}
                PaperProps={{
                  sx: { width: "705px", maxWidth: "none", backgroundColor: "#170903", marginBottom: "20px" },
                }}
              >
                <DialogContent
                  sx={{ backgroundColor: "#170903", padding: "0", marginBottom: "30px", marginTop: "5px" }}
                >
                  <SourceTargetSelector
                    showModel={showModel}
                    setShowModel={setShowModel}
                    data={data
                      .filter((target) => target.isTarget && target.name.toLowerCase() !== "csv")
                      .map((target) => ({
                        src: target.imgUrl,
                        name: target.displayName,
                        value: target.name,
                      }))}
                    name="TARGET"
                    onSelect={handleSelect}
                  />
                </DialogContent>
              </Dialog>
            </div>
          )}
        </div>
      </div>
      <Dialog
        open={showResetDialog}
        onClose={cancelReset}
        PaperProps={{
          sx: {
            width: "400px",
            padding: "0px",
            overflow: "hidden !important",
            backgroundColor: "#1F1611",
            borderRadius: "8px",
          },
        }}
      >
        <DialogContent sx={{ overflow: "hidden", padding: "0px", backgroundColor: "#1F1611" }}>
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              backgroundColor: "#1F1611",
              color: "#ffffff",
              padding: "30px",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "20px",
              }}
            >
              <h3
                style={{
                  margin: 0,
                  fontSize: "14px",
                  fontFamily: "Poppins",
                  color: "#EA5822",
                  fontWeight: "700",
                  fontStyle: "normal",
                  lineHeight: "24px",
                }}
              >
                CONFIRM RESET
              </h3>
              <button
                onClick={cancelReset}
                style={{
                  background: "none",
                  border: "none",
                  color: "#ffffff",
                  fontSize: "20px",
                  cursor: "pointer",
                  padding: "0",
                  width: "24px",
                  height: "24px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                ✕
              </button>
            </div>

            <p
              style={{
                margin: "0 0 30px 0",
                fontSize: "16px",
                color: "#F8F8F7",
                fontFamily: "Inter",
                fontStyle: "normal",
                fontWeight: "400",
                lineHeight: "24px",
              }}
            >
              Are you sure you want to reset all form fields?
            </p>

            <div style={{ display: "flex", justifyContent: "flex-end", gap: "10px" }}>
              <button
                onClick={cancelReset}
                style={{
                  padding: "8px 16px",
                  borderRadius: "4px",
                  border: "1px solid #666",
                  background: "#1F1611",
                  color: "#ffffff",
                  cursor: "pointer",
                  fontWeight: "500",
                }}
              >
                Cancel
              </button>
              <button
                onClick={confirmReset}
                style={{
                  padding: "8px 16px",
                  borderRadius: "4px",
                  border: "none",
                  background: "#EF8963",
                  color: "#170903",
                  cursor: "pointer",
                  fontWeight: "500",
                }}
              >
                Reset
              </button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      <ToastContainer />
    </div>
  )
}
