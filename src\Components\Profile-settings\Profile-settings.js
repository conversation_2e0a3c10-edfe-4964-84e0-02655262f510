import React, { useEffect, useState, useRef } from "react";
import styles from "./Profile-settings.module.css";
import Sidebar from "../Sidebar/Sidebar";
import globalStyles from "../globalStyles.module.css";
import { CheckIcon, GlobeIcon, SearchIcon, XIcon, EyeIcon, EyeOffIcon } from "@heroicons/react/solid";
import { Dialog, DialogContent, FormControl, MenuItem, Select } from "@mui/material";
import OverrideValues from "../Data-Migration/Data-mapping/OverrideValues/OverrideValues";
import ProfilePictureUpdate from "./ProfilePicture/profilePicture";
import Phonenumber from "./PhoneNumber/PhoneNumber";
import ChangePassword from "./ChangePaassword/ChangePassword";
import { deleteAccount, getUser, updateUser } from "../apiService";
import { ToastContainer, toast } from "react-toastify";
import CryptoJS from "crypto-js";
import LoaderSpinner from "../loaderspinner";
import DefaultValue from "../Data-Migration/Data-mapping/Default-value/DefaultValue";
// import { shutdown } from "@intercom/messenger-js-sdk";
import { displayArticle } from "../../Helper/helper";


export default function ProfileSettings() {
    const [email, setEmail] = useState(localStorage.getItem('email'));
    const [isLoading, setIsLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const [imageUrl, setImageUrl] = useState('/assets/profile-orange.png');
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
        return localStorage.getItem('isSidebarCollapsed') === 'true';
    });
    const [curPassword, setCurPassword] = useState("");
    const [strongPassword, setStrongPassword] = useState(false);
    const countryCodes = [
        { code: "+91", name: "India" },
        { code: "+1", name: "United States" },
        { code: "+44", name: "United Kingdom" },
        { code: "+61", name: "Australia" },
        { code: "+1", name: "Canada" },
    ];
    const [openProfile, setOpenProfile] = useState(false);
    const [openPhone, setOpenPhone] = useState(false);
    const [openChangePassword, setOpenChangePassword] = useState(false);
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [samePassword, setSamePassword] = useState(false)
    const apiCalledRef = useRef(null);
    const [data, setData] = useState({
        "name": "",
        "email": "",
        "password": "",
        "company_name": "",
        "designation": "",
        "country_code": "",
        "phone_number": "",
    });

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const checkStrongPassword = (e) => {
        const passwordValue = e.target.value;
        setCurPassword(passwordValue);
        if (passwordValue === strongPassword) {
            setSamePassword(true);
        } else {
            setSamePassword(false);
        }
    };

    useEffect(() => {
        setIsLoading(true);

        const fetchUser = async () => {
            if (!apiCalledRef.current) {
                apiCalledRef.current = true;
                try {
                    const res = await getUser(email);
                    setIsLoading(false);
                    setData((prevState) => ({
                        ...prevState,
                        name: res.name,
                        password: res.password,
                        email: res.email,
                        company_name: res.company_name || "",
                        designation: res.designation || "",
                        country_code: res.country_code || "",
                        phone_number: res.phone_number || "",

                    }));
                    const defaultPicture = '/assets/profile-orange.png';
                    setImageUrl(res.profile_image || defaultPicture);
                    setStrongPassword(decrypt(res.password));


                } catch (error) {
                    setIsLoading(false);
                    throw error;
                }
            }

        };

        fetchUser();
    }, []);


    const handleProfile = () => {
        setOpenProfile(true);

    }
    const closeDialog = () => {
        setOpenProfile(false)
    }
    const openPhonenUmber = () => {
        setOpenPhone(true);
    }
    const closePhone = () => {
        setOpenPhone(false);
    }

    const handlePassword = () => {
        if (!curPassword) {
            toast.error("Enter your current password to change it.", {
                position: "top-right"
            });
        } else if (!samePassword) {
            toast.error("Your current password is wrong.", {
                position: "top-right"
            });
        } else {
            setOpenChangePassword(true);
        }
    }

    const closeChangePassword = () => {
        setOpenChangePassword(false);
    }

    const handleOpenDeleteDialog = () => {
        setOpenDeleteDialog(true);
    }

    const closeDeleteDialog = () => {
        setOpenDeleteDialog(false);
    }

    const deleteAccountCall = async () => {
        setIsLoading(true);
        setOpenDeleteDialog(false);
        const res = await deleteAccount(email);
        if (res) {
            setIsLoading(false);
            localStorage.removeItem('email');
            localStorage.removeItem('sessionExpiry');
            //  shutdown();
            window.location.reload();
        }
    }

    const decrypt = (ciphertext) => {
        const secretKey = CryptoJS.enc.Utf8.parse("sgmg22025sgmg220");
        const iv = CryptoJS.enc.Utf8.parse("****************");

        const bytes = CryptoJS.AES.decrypt(ciphertext, secretKey, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });

        return bytes.toString(CryptoJS.enc.Utf8);
    };

    const save = async (url) => {
        const payload = {
            profile_image: url
        }
        setImageUrl(url);
        try {
            const res = await updateUser(email, payload);
        } catch (error) {
            console.log(error);
        }
    }

    return (
        <div>
            {isLoading ? (<LoaderSpinner />) : (
                <div>
                    <Sidebar
                        isCollapsed={isSidebarCollapsed}
                        setIsCollapsed={setIsSidebarCollapsed}
                    />
                    <div className={`${styles["main-section"]} ${isSidebarCollapsed ? styles.expanded : ''}`}>
                        <div className={styles.dFlex} style={{ justifyContent: "space-between" }}>
                            <div className={globalStyles.headerStyle} style={{paddingLeft:0}} >Your Profile & Settings</div>
                            <div className={globalStyles.searchBarContainer} style={{ paddingRight: 0 }}>
                                <div className={globalStyles.searchBar}>
                                    {/* Search functionality temporarily hidden
                                    <div className={globalStyles.searchWrapper}>
                                        <SearchIcon className={globalStyles.searchIcon} />
                                        <input
                                            type="text"
                                            placeholder="Search..."
                                            className={globalStyles.searchInput}
                                            onFocus={(e) => {
                                                e.target.style.width = '200px';
                                                e.target.placeholder = 'Typing...';
                                            }}
                                            onBlur={(e) => {
                                                e.target.style.width = '80px';
                                                e.target.placeholder = 'Search...';
                                            }}
                                        />
                                    </div>
                                    */}

                                    <div className={globalStyles.searchWrapper} style={{ marginRight: 0 }}>
                                        <GlobeIcon className={globalStyles.searchIcon} />
                                        <input type="text" placeholder="Eng"
                                            className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                                            readOnly />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className={styles.dFlex} style={{ justifyContent: "space-between" }}>
                            <div className={styles.section1}>
                                <div className={styles.topAvatarSection}>
                                    <div className={styles.avatarContainer}>
                                        <img src={imageUrl} alt="Profile Avatar" className={styles.avatar} />
                                    </div>
                                    <button className={globalStyles.plainButton} style={{ width: "50%" }} onClick={handleProfile}>
                                        Edit profile picture
                                    </button>
                                </div>

                            </div>
                            <Dialog open={openProfile} maxWidth="md" >
                                <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>

                                    <ProfilePictureUpdate close={closeDialog} save={save} imageUrl={imageUrl} />
                                </DialogContent>
                            </Dialog>

                            <div className={styles.section2} >
                                <div style={{
                                    display: "flex",
                                    alignItems: "center",
                                    marginLeft: "auto",
                                    marginTop: "10px",
                                    // paddingRight: "20px",
                                    justifyContent: "flex-end"
                                }}>
                                    <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} onClick={() => { displayArticle("What happens once my account is delete?") }} />
                                    <span className={globalStyles.guideName}>What happens once my account is delete?</span>
                                </div>

                                <div style={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "flex-end",
                                    justifyContent: "flex-end",
                                    width: "95%",
                                    // padding: "20px",
                                    margin: "20px 0 20px 0 20px",

                                }}>
                                    <div className={styles.dFlex}
                                        style={{
                                            display: "flex",
                                            alignItems: "center",
                                            width: "100%",
                                            marginBottom: "20px",
                                            marginTop: "20px"
                                        }}>
                                        <label className={globalStyles.poppinsHeaderStyle}
                                            style={{ width: "150px", textAlign: "left" }}>
                                            User name
                                        </label>
                                        <input
                                            style={{ flex: "1", width: "100%" }}
                                            className={`form-control ${styles.profileInput}`}
                                            type="text"
                                            value={data.name}
                                            disabled
                                        />
                                    </div>

                                    <div className={styles.dFlex}
                                        style={{ display: "flex", alignItems: "center", width: "100%", marginBottom: "20px" }}>
                                        <label className={globalStyles.poppinsHeaderStyle}
                                            style={{ width: "150px", textAlign: "left" }}>
                                            Email
                                        </label>
                                        <input
                                            style={{ flex: "1", width: "60%" }}
                                            className={`form-control ${styles.profileInput}`}
                                            type="email"
                                            value={data.email}
                                            disabled
                                        />
                                    </div>

                                    {/*<div className={styles.dFlex}*/}
                                    {/*     style={{display: "flex", alignItems: "center", width: "100%", marginBottom: "20px",}}>*/}
                                    {/*    <label className={globalStyles.poppinsHeaderStyle}*/}
                                    {/*           style={{width: "150px", textAlign: "left"}}>*/}
                                    {/*        Company Name*/}
                                    {/*    </label>*/}
                                    {/*    <input*/}
                                    {/*        style={{flex: "1", width: "60%"}}*/}
                                    {/*        className={`form-control ${styles.profileInput}`}*/}
                                    {/*        type="text"*/}
                                    {/*        value={data.company_name}*/}
                                    {/*        placeholder="Enter your company name"*/}
                                    {/*    />*/}
                                    {/*</div>*/}
                                    {/*<div className={styles.dFlex}*/}
                                    {/*     style={{display: "flex", alignItems: "center", width: "100%", marginBottom: "20px",}}>*/}
                                    {/*    <label className={globalStyles.poppinsHeaderStyle}*/}
                                    {/*           style={{width: "150px", textAlign: "left"}}>*/}
                                    {/*        Designation*/}
                                    {/*    </label>*/}
                                    {/*    <input*/}
                                    {/*        style={{flex: "1", width: "60%"}}*/}
                                    {/*        className={`form-control ${styles.profileInput}`}*/}
                                    {/*        type="text"*/}
                                    {/*        value={data.designation}*/}
                                    {/*        placeholder="Enter your Designation"*/}
                                    {/*    />*/}
                                    {/*</div>*/}
                                    {/*<div className={styles.dGrid}*/}
                                    {/*     style={{display: "grid", gridTemplateColumns: "2fr 2fr", marginBottom: "20px"}}>*/}
                                    {/*    <div></div>*/}
                                    {/*    /!* First empty grid column *!/*/}
                                    {/*    <div style={{display: "flex", justifyContent: "flex-end", alignItems: "center"}}>*/}
                                    {/*        <button className={globalStyles.plainButton}>Update</button>*/}
                                    {/*    </div>*/}
                                    {/*</div>*/}
                                    {/*<div className={styles.dFlex}*/}
                                    {/*     style={{display: "flex", alignItems: "center", width: "100%", marginBottom: "20px",}}>*/}
                                    {/*    <label className={globalStyles.poppinsHeaderStyle}*/}
                                    {/*           style={{width: "150px", textAlign: "left"}}>*/}

                                    {/*    </label>*/}
                                    {/*    <div style={{*/}
                                    {/*        display: "flex",*/}
                                    {/*        justifyContent: "flex-end",*/}
                                    {/*        alignItems: "center",*/}
                                    {/*        marginLeft: "20px"*/}
                                    {/*    }}>*/}
                                    {/*        <button className={globalStyles.plainButton}>Update Profile</button>*/}
                                    {/*    </div>*/}
                                    {/*</div>*/}

                                    <div className={globalStyles.selectionName} style={{ marginTop: "20px" }}>PASSWORD SETTINGS
                                    </div>
                                    <d iv className={styles.dFlex}
                                        style={{ display: "flex", alignItems: "center", width: "100%", marginBottom: "20px", }}>
                                        <label className={globalStyles.poppinsHeaderStyle}
                                            style={{ width: "240px", textAlign: "left" }}>
                                            Current password
                                        </label>
                                        <div className={styles.passwordContainer}>
                                            <input
                                                style={{ flex: "1", width: "60%" }}
                                                type={showPassword ? "text" : "password"}
                                                className={styles.passwordInput}
                                                placeholder="Enter your current password"
                                                value={curPassword}
                                                onChange={(e) => {
                                                    checkStrongPassword(e);
                                                }}
                                            />
                                            <div className={styles.passwordIconsContainer}>
                                                {/* Eye icon to toggle password visibility */}
                                                <button
                                                    type="button"
                                                    className={styles.eyeButton}
                                                    onClick={togglePasswordVisibility}
                                                >
                                                    {showPassword ? (
                                                        <EyeOffIcon className={styles.eyeIcon} />
                                                    ) : (
                                                        <EyeIcon className={styles.eyeIcon} />
                                                    )}
                                                </button>

                                                {/* Validation icon */}
                                                {/* {curPassword.length > 0 && (
                                                    <span className={styles.validationIcon}>
                                                        {samePassword ? (
                                                            <CheckIcon className={styles.checkIcon} />
                                                        ) : (
                                                            <XIcon className={styles.xIcon} />
                                                        )}
                                                    </span> 
                                                )} */}
                                            </div>
                                            {/*{formSubmitted && !password &&*/}
                                            {/*    <p className="error-message">Password cannot be empty</p>}*/}
                                            {/*{formSubmitted && password && !strongPassword &&*/}
                                            {/*    <p className="error-message">Password must be strong</p>}*/}
                                        </div>
                                    </d>
                                    <div className={styles.dFlex}
                                        style={{ display: "flex", alignItems: "center", width: "100%", marginBottom: "20px", }}>
                                        <label className={globalStyles.poppinsHeaderStyle}
                                            style={{ width: "150px", textAlign: "left" }}>

                                        </label>
                                        <div style={{
                                            display: "flex",
                                            justifyContent: "flex-end",
                                            alignItems: "center",
                                            marginLeft: "25px"
                                        }}>
                                            <button className={globalStyles.plainButton} data-toggle="tooltip" title="Disabled tooltip" onClick={handlePassword}>Change Password</button>
                                        </div>
                                    </div>
                                    <Dialog open={openChangePassword} maxWidth="md" >
                                        <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>

                                            <ChangePassword close={closeChangePassword} />
                                        </DialogContent>
                                    </Dialog>
                                    
                                    {/* Delete Account Confirmation Dialog */}
                                    <Dialog open={openDeleteDialog} maxWidth="md" onClose={closeDeleteDialog}>
                                        <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>
                                            <div style={{
                                                display: "flex",
                                                width: "705px",
                                                padding: "30px 45px 45px 45px",
                                                flexDirection: "column",
                                                alignItems: "flex-start",
                                                gap: "25px",
                                                borderRadius: "10px",
                                                background: "#170903",
                                                position: "relative"
                                            }}>
                                                {/* Close button */}
                                                <button 
                                                    onClick={closeDeleteDialog}
                                                    style={{
                                                        position: "absolute",
                                                        top: "20px",
                                                        right: "20px",
                                                        background: "none",
                                                        border: "none",
                                                        color: "#fff",
                                                        fontSize: "24px",
                                                        cursor: "pointer",
                                                        padding: "0",
                                                        width: "30px",
                                                        height: "30px",
                                                        display: "flex",
                                                        alignItems: "center",
                                                        justifyContent: "center"
                                                    }}
                                                >
                                                    ×
                                                </button>
                                                
                                                {/* Header */}
                                                <h2 style={{
                                                    color: "#EA5822",
                                                    fontFamily: "Poppins",
                                                    fontSize: "14px",
                                                    fontStyle: "normal",
                                                    fontWeight: "700",
                                                    lineHeight: "24px",
                                                    margin: "0",
                                                    textTransform: "uppercase",
                                                    letterSpacing: "0.5px"
                                                }}>
                                                    DO YOU WANT TO DELETE ACCOUNT #{data.name || "001"}
                                                </h2>
                                                
                                                {/* Warning message */}
                                                <p style={{
                                                    color: "#F8F8F7",
                                                    fontFamily: "Inter",
                                                    fontSize: "16px",
                                                    fontStyle: "normal",
                                                    fontWeight: "400",
                                                    lineHeight: "24px",
                                                    margin: "0"
                                                }}>
                                                    Deleting this account is permanent.
                                                </p>
                                                
                                                {/* Description */}
                                                <p style={{
                                                    color: "#F8F8F7",
                                                    fontFamily: "Inter",
                                                    fontSize: "16px",
                                                    fontStyle: "normal",
                                                    fontWeight: "400",
                                                    lineHeight: "24px",
                                                    margin: "0"
                                                }}>
                                                    All associated mappings, API keys, and configuration details will be removed and cannot be recovered. Do you want to proceed?
                                                </p>
                                                
                                                {/* Delete button */}
                                                <button 
                                                    onClick={deleteAccountCall}
                                                    style={{
                                                        display: "flex",
                                                        height: "40px",
                                                        padding: "10px 45px",
                                                        justifyContent: "center",
                                                        alignItems: "center",
                                                        gap: "10px",
                                                        alignSelf: "stretch",
                                                        borderRadius: "3px",
                                                        background: "#EF8963",
                                                        color: "#170903",
                                                        textAlign: "center",
                                                        fontFamily: "Inter",
                                                        fontSize: "16px",
                                                        fontStyle: "normal",
                                                        fontWeight: "500",
                                                        lineHeight: "24px",
                                                        border: "none",
                                                        cursor: "pointer",
                                                        marginTop: "10px",
                                                        transition: "background-color 0.3s ease"
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.backgroundColor = "#EFEEED";
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.backgroundColor = "#EF8963";
                                                    }}
                                                >
                                                    Delete permanently
                                                </button>
                                            </div>
                                        </DialogContent>
                                    </Dialog>
                                    {/*<div className={globalStyles.selectionName} style={{marginTop: "20px"}}>NOTIFICATION*/}
                                    {/*    SETTINGS*/}
                                    {/*</div>*/}
                                    {/*<div className={styles.dFlex}*/}
                                    {/*     style={{*/}
                                    {/*         display: "flex",*/}
                                    {/*         alignItems: "center",*/}
                                    {/*         width: "100%",*/}
                                    {/*         marginBottom: "20px",*/}
                                    {/*         marginTop: "15px"*/}
                                    {/*     }}>*/}
                                    {/*    <label className={globalStyles.poppinsHeaderStyle}*/}
                                    {/*           style={{width: "250px", textAlign: "left"}}>*/}
                                    {/*        Updates sent to*/}
                                    {/*    </label>*/}
                                    {/*    <div style={{display: "flex", gap: "10px", width: "100%"}}>*/}
                                    {/*        <FormControl*/}
                                    {/*            className={globalStyles.customDropdownContainer}*/}
                                    {/*            style={{width: "25%"}}>*/}
                                    {/*            <Select displayEmpty*/}
                                    {/*                    value={data.country_code}*/}
                                    {/*                    className={globalStyles.customDropdownSelect}*/}
                                    {/*                    style={{backgroundColor: "#DCDAD9"}}*/}
                                    {/*                    renderValue={(selected) => (*/}
                                    {/*                        <span className={globalStyles.guideName}*/}
                                    {/*                              style={{fontSize: "14px"}}>*/}
                                    {/*                                {data.country_code || "Select Code"}*/}
                                    {/*                              </span>*/}
                                    {/*                    )}>*/}
                                    {/*                {countryCodes.map((country) => (*/}
                                    {/*                    <MenuItem key={country.code}*/}
                                    {/*                              value={country.code}>*/}
                                    {/*                        {country.code} ({country.name})*/}
                                    {/*                    </MenuItem>*/}
                                    {/*                ))}*/}
                                    {/*            </Select>*/}
                                    {/*        </FormControl>*/}
                                    {/*        <input*/}
                                    {/*            className="form-control"*/}
                                    {/*            style={{width: "100%", marginTop: "12px"}}*/}
                                    {/*            placeholder="Enter the phone number"*/}
                                    {/*            disabled*/}
                                    {/*            value={data.phone_number}*/}
                                    {/*        />*/}
                                    {/*    </div>*/}
                                    {/*</div>*/}
                                    {/*<div className={styles.dFlex}*/}
                                    {/*     style={{display: "flex", alignItems: "center", width: "100%", marginBottom: "20px",}}>*/}
                                    {/*    <label className={globalStyles.poppinsHeaderStyle}*/}
                                    {/*           style={{width: "150px", textAlign: "left"}}>*/}

                                    {/*    </label>*/}
                                    {/*    <div style={{*/}
                                    {/*        display: "flex",*/}
                                    {/*        justifyContent: "flex-end",*/}
                                    {/*        alignItems: "center",*/}
                                    {/*        marginLeft: "25px"*/}
                                    {/*    }}>*/}
                                    {/*        <button className={globalStyles.plainButton} onClick={openPhonenUmber}>Change phone number</button>*/}
                                    {/*    </div>*/}
                                    {/*</div>*/}
                                    {/*<Dialog open={openPhone} sx={{ "& .MuiDialog-paper": { width: "800px" } }}>*/}
                                    {/*    <DialogContent style={{padding: "0", overflow: "auto", backgroundColor: "#170903"}}>*/}

                                    {/*        <Phonenumber close={closePhone}/>*/}
                                    {/*    </DialogContent>*/}
                                    {/*</Dialog>*/}
                                    {/*<div className={styles.dFlex}*/}
                                    {/*     style={{display: "flex", alignItems: "center", width: "100%", marginBottom: "20px",}}>*/}
                                    {/*    <label className={globalStyles.poppinsHeaderStyle}*/}
                                    {/*           style={{width: "180px", textAlign: "left"}}>*/}
                                    {/*        Your preferences*/}
                                    {/*    </label>*/}
                                    {/*    <div className={styles.dFlex} style={{justifyContent: "center"}}>*/}
                                    {/*        <input*/}

                                    {/*            className={styles.formCheckInput}*/}
                                    {/*            type="checkbox"*/}
                                    {/*            id={`checkbox-1}`}*/}
                                    {/*            value=""*/}
                                    {/*            aria-label="..."*/}
                                    {/*            style={{padding: "7px", marginTop: "10px"}}*/}
                                    {/*            // checked={mapping.skip}*/}
                                    {/*            // onChange={() => handleCheckboxChange(value, (currentPage - 1) * itemsPerPage + index)}*/}
                                    {/*        />*/}
                                    {/*        <span className={globalStyles.interSummaryStyle} style={{paddingLeft: "20px"}}>Notify me with migration updates and errors.</span>*/}
                                    {/*    </div>*/}

                                    {/*</div>*/}
                                    {/*<div className={styles.dFlex}*/}
                                    {/*     style={{display: "flex", alignItems: "center", width: "100%", marginBottom: "20px",}}>*/}
                                    {/*    <label className={globalStyles.poppinsHeaderStyle}*/}
                                    {/*           style={{width: "180px", textAlign: "left"}}>*/}
                                    {/*        /!*Your preferences*!/*/}
                                    {/*    </label>*/}
                                    {/*    <div className={styles.dFlex} style={{justifyContent: "center"}}>*/}
                                    {/*        <input*/}

                                    {/*            className={styles.formCheckInput}*/}
                                    {/*            type="checkbox"*/}
                                    {/*            id={`checkbox-2}`}*/}
                                    {/*            value=""*/}
                                    {/*            aria-label="..."*/}
                                    {/*            style={{padding: "7px", marginTop: "10px"}}*/}
                                    {/*            // checked={mapping.skip}*/}
                                    {/*            // onChange={() => handleCheckboxChange(value, (currentPage - 1) * itemsPerPage + index)}*/}
                                    {/*        />*/}
                                    {/*        <span className={globalStyles.interSummaryStyle} style={{paddingLeft: "20px"}}>Notify me with changes and updates to templates.</span>*/}
                                    {/*    </div>*/}

                                    {/*</div>*/}
                                    {/*<div className={styles.dFlex}*/}
                                    {/*     style={{display: "flex", alignItems: "center", width: "100%", marginBottom: "20px",}}>*/}
                                    {/*    <label className={globalStyles.poppinsHeaderStyle}*/}
                                    {/*           style={{width: "180px", textAlign: "left"}}>*/}
                                    {/*        /!*Your preferences*!/*/}
                                    {/*    </label>*/}
                                    {/*    <div className={styles.dFlex} style={{justifyContent: "center"}}>*/}
                                    {/*        <input*/}

                                    {/*            className={styles.formCheckInput}*/}
                                    {/*            type="checkbox"*/}
                                    {/*            id={`checkbox-3}`}*/}
                                    {/*            value=""*/}
                                    {/*            aria-label="..."*/}
                                    {/*            style={{padding: "7px", marginTop: "10px"}}*/}
                                    {/*            // checked={mapping.skip}*/}
                                    {/*            // onChange={() => handleCheckboxChange(value, (currentPage - 1) * itemsPerPage + index)}*/}
                                    {/*        />*/}
                                    {/*        <span className={globalStyles.interSummaryStyle} style={{paddingLeft: "20px"}}>Receive product updates or tips.</span>*/}
                                    {/*    </div>*/}

                                    {/*</div>*/}
                                    <button className={globalStyles.mainButton} style={{ width: "100%", marginBottom: "30px" }} onClick={handleOpenDeleteDialog}>Delete account</button>

                                </div>


                            </div>

                        </div>
                    </div>
                    <ToastContainer />
                </div>
            )}
        </div>

    )
}