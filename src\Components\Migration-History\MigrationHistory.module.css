.container {
  display: flex;
  min-height: 100vh;
  background-color: #fff;
  overflow: hidden;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.mainSection {
  margin-left: 240px;
  padding: 20px 45px 20px 50px;
  flex: 1;
  transition: margin-left 0.3s ease;
  background-color: #fff;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  overflow-y: auto;
  overflow-x: hidden;
}


.mainSection.expanded {
  margin-left: 110px;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.headerControls {
  display: flex;
  gap: 10px;
}

.statsContainer {
  display: flex;
  gap: 15px;
  width: 100%;
  margin-bottom: 20px;
}

.statItem {
  flex: 1;
  height: 78px;
  border-radius: 5px;
  border: 1px solid #ddd;
  padding: 15px 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.statNumber {
  font-size: 24px;
  font-weight: bold;
  color: #746B68;
  font-family: Poppins;
}
.statContent {
  display: flex;
  align-items: center;
  gap: 10px; /* Adjust spacing */
}
.statLabel {
  font-size: 14px;
  color: #170903;
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
  white-space: nowrap; /* Prevents wrapping */
}


.listOfMigrations {
  display: flex;
  padding: 7px 15px;
  justify-content: space-between;
  align-items: center;
  background: #170903;
  color:#EA5822;

  margin-bottom: 15px;
  font-family: Poppins;
}

.listOfMigrations h2 {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.unableToFind {
  display: flex;
  align-items: center;
  gap: 3px;
  background-color: #170903;
  color: #DCDAD9;
  padding: 5px 10px;
  border-radius: 5px;
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}

.tableControls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
}

.filterContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #FFFFFF;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #514742;
}

.searchContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.searchInput {
  padding: 8px 12px;
  padding-right: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.helpIcon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

.tableWrapper {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 15px;
}

.migrationsTable {
  width: 100%;
  border-collapse: collapse;
  font-family: "Poppins";
}

.migrationsTable th {
  background-color: #f5f5f5;
  padding: 12px 16px;
  text-align: left;
  color: #746b68;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.migrationsTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.idCell {
  font-weight: 500;
}

.description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.statusBadge {
  display: inline-flex;
  padding: 5px 15px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.inProgress {
  background-color: #000;
  color: white;
}

.completed {
  background-color: #e1683b;
  color:white;
}

.partiallycompleted{
  background-color: #fe4c00;
  color:white;
}

.scheduled {
  background-color: grey;
  color: black;
}

.inProgress {
  background-color: #F59E0B;
  color: white;
}

.failed {
  background-color: #A43E18;
  color: white;
}

.unknown {
  background-color:#6B7280;
  color:white;
}

.detailsButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0 40px 0;
  gap: 60px;
}

.pageInfo,
.rowsPerPageContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.pageButtons,
.rowsButtons {
  display: flex;
  gap: 5px;
}

.pageButton,
.rowsButton {
  border: 1px solid #ddd;
  background-color: white;
  padding: 5px 10px;
  cursor: pointer;
  min-width: 30px;
  text-align: center;
  font-size: 14px;
}

.active {
  background-color: #333;
  color: white;
}
.downloadButton {
  margin-right: 15px; /* Adjust as needed */
}
.paginationText {
  color: #170903;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
}
.filterDropdownContainer {
  position: relative;
  display: inline-block;
}

.filterDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  min-width: 200px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filterOption {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #170903;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}


.filterOption:hover {
  background-color: #f5f5f5;
}

/* Ensure the filter button has proper styling */
.filterButton {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  color: #170903;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
}

.filterButton:hover {
  background-color: #f5f5f5;
}


.emptyState {
  background-color: #DCDAD9;
  padding: 7px 15px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  border-radius: 0;
  margin: 15px 0;
}

.emptyStateContent {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.emptyState h2 {
  color: #746B68;
text-align: center;
font-family: Inter;
font-size: 16px;
font-style: normal;
font-weight: 400;
line-height: 24px;
}

.noDataCell{
  text-align: center;
}

/* Animation for refresh icon */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Make the animation globally available */
:global(.spin) {
  animation: spin 1s linear infinite;
}
