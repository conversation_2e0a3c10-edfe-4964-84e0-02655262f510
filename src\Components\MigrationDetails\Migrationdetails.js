import React, { useState, useEffect, useRef } from "react"
import styles from "./MigrationDetails.module.css"
import { ArrowLeftIcon, GlobeIcon, SearchIcon } from "@heroicons/react/solid"
import globalStyles from "../globalStyles.module.css"
import Sidebar from "../Sidebar/Sidebar"
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer } from "recharts"
import { HiChevronDown, HiOutlineFunnel } from "react-icons/hi2"
import {
  getBatchesByMigration,
  getBatchesById,
  getRecordsByBatch,
  getRecordByBatchConversations,
  getMigrationById,
  checkBatchMetrics,
  getSchedulersDetailsBatches,
  retryRecordMigration,
  getRecordsByBatchId,
  retryBatchMigration,
  retryPlanMigration,
  // transformData,
  getWorkflowErrorLog,
} from "../apiService"
import { FaChevronLeft, FaChevronRight, FaAngleDoubleLeft, FaAngleDoubleRight } from "react-icons/fa"
import LoaderSpinner from "../loaderspinner"
import { useMongodb } from "../mongodbService"
import websocketService from "../websocketService"
import { interval, switchMap } from "rxjs"
import { Dialog, DialogContent } from "@mui/material"
import APITester from "../ApiTester/ApiTester"
import PayloadDialog from "../Payload/Payload"
import { HiChevronUp } from "react-icons/hi"
import { is } from "date-fns/locale"
import MigrationSummary from "./components/migrationSummary/MigrationSummary"

const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const MigrationDetails = ({ migration, onBack }) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    return localStorage.getItem("isSidebarCollapsed") === "true"
  })
  const { watchMigrations, migrationDataMongo, batchDataMongo, closeConnection } = useMongodb()

  const [activeSection, setActiveSection] = useState("batch")
  const [isLoading, setIsLoading] = useState(false)
  const [subIsLoading, setSubIsLoading] = useState({})

  const [migrationStats, setMigrationStats] = useState({
    totalRecords: 0,
    passed: 0,
    failed: 0,
    status: "",
    scheduled: 0,
  })

  const [batchDetails, setBatchDetails] = useState([])
  const [email, setEmail] = useState(localStorage.getItem("email"))
  const [retryRecordLoading, setRetryRecordLoading] = useState(false)
  const [retryRecordId, setRetryRecordId] = useState("")
  const [openApiResult, setOpenApiResult] = useState(false)
  const [executor, setExecutor] = useState(null)
  const [migrationData, setMigrationData] = useState({
    recordsMigrated: "0",
    amountPaid: "0$",
    migrationName: "",
    migrationId: "",
    migrationType: "",
    templateUsed: "",
    sourcePlatform: "",
    targetPlatform: "",
    createdBy: "",
    startedOn: "",
    completedBy: "",
    status: "",
  })

  const [selectedBatchId, setSelectedBatchId] = useState(null)
  const [showBatchExecution, setShowBatchExecution] = useState(false)
  const [batchExecutionData, setBatchExecutionData] = useState({
    migrationId: "",
    batchId: "",
    startTime: "",
    endTime: "",
    totalRecords: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    mainTargetId: "",
    errors: 0,
    migrated: 0,
  })

  const [batchRecords, setBatchRecords] = useState([])
  const [statusFilter, setStatusFilter] = useState("all")
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)

  const [currentPage, setCurrentPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [batchDetailsCurrentPage, setBatchDetailsCurrentPage] = useState(0)
  const [batchDetailsRowsPerPage, setBatchDetailsRowsPerPage] = useState(10)
  const [schedulerIds, setSchedulerIds] = useState([])
  const apiSubscriptionRef = useRef(null)
  const [expandedRows, setExpandedRows] = useState({})
  const [subTableData, setSubTableData] = useState({})

  const [migrationSummary, setMigrationSummary] = useState({
    name: "",
    id: "",
    createdOn: "",
    startTime: "",
    endTime: "",
    totalRecords: 0,
    migrated: 0,
    failed: 0,
  })
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedPayload, setSelectedPayload] = useState(null)
  const [errorLogData, setErrorLogData] = useState(null)

  const closePayloadDialog = () => {
    setIsDialogOpen(false)
    setSelectedPayload(null)
  }
 
  useEffect(() => {
    if (migration) {
      setMigrationData({
        recordsMigrated: migration.passed,
        amountPaid: migration.amountPaid || "N/A", migrationName: (migration.name || '').split(' -')[0],
        migrationId: migration.migrationId || "",
        migrationType: "Unknown",
        templateUsed: (migration.name || '').split(' -')[0] || "N/A",
        sourcePlatform: localStorage.getItem("sourcePlatform") || migration.sourcePlatform || "Unknown",
        targetPlatform: localStorage.getItem("targetPlatform") || migration.targetPlatform || "Unknown",
        createdBy: migration.executedBy || "Unknown",
        startedOn: formatTimestamp(migration.startTime) || "N/A",
        completedBy: formatTimestamp(migration.endTime) || "In progress",
        status: migration.status || "unknown",
      })

      const failed = migration.failed || 0
      const passed = migration.passed || 0
      const totalRecords = migration.totalRecords || 0

      setMigrationStats({
        totalRecords: totalRecords,
        passed: passed,
        failed: failed,
        status: migration.status || "started",
        scheduled: migration.scheduled,
      })

      setMigrationSummary({
        name: migration.name || "",
        id: migration.migrationId || "",
        createdOn: formatTimestamp(migration.startTime) || "N/A",
        startTime: formatTimestamp(migration.startTime) || "N/A",
        endTime: formatTimestamp(migration.endTime) || "N/A",
        totalRecords: totalRecords,
        migrated: passed,
        failed: failed,
      })
    }
  }, [migration])

  const toggleSection = (section) => {
    console.log("Toggling section:", section)
    if (activeSection === section) {
      setActiveSection(null)
    } else {
      setActiveSection(section)
      if (section === "batch" && migrationData.migrationId) {
        // fetchBatchDetails(migrationData.migrationId)
        // getWorkflowData(migrationData.migrationId)
      }
    }
  }

  const fetchBatchExecutionData = async (batchId) => {
    try {
      setIsLoading(true)
      const response = await getBatchesById(batchId, true)
      const data = response
      const failed = data.failed || 0
      const passed = data.passed || 0
      const totalRecords = data.totalRecords || 0
      console.log(data, "ddd")

      setBatchExecutionData({
        migrationId: data.migrationId || "",
        batchId: data.batchId || batchId,
        startTime: data.startTime || "",
        endTime: data.endTime || "",
        totalRecords: totalRecords,
        passed: passed,
        failed: failed,
        skipped: data.skipped || 0,
        mainTargetId: data.mainTargetId || "",
        errors: failed,
        migrated: passed,
        name: data.name,
      })

      setMigrationSummary({
        name: data.name || "",
        id: data.migrationId || "",
        createdOn: formatTimestamp(data.timestamp) || "N/A",
        startTime: formatTimestamp(data.startTime) || "N/A",
        endTime: formatTimestamp(data.endTime) || "N/A",
        totalRecords: totalRecords,
        migrated: passed,
        failed: failed,
      })
    } catch (error) {
      console.error("Error fetching batch execution data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchBatchRecords = async (batchId) => {
    try {
      setIsLoading(true)
      const response = await getRecordsByBatch(batchId)
      setBatchRecords(response)
    } catch (error) {
      console.error("Error fetching batch records:", error)
      setBatchRecords([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const initMongo = async () => {
      await watchMigrations()
    }

    initMongo()
  }, [])

  useEffect(() => {
    if (batchDataMongo) {
      updateBatchData(batchDataMongo)
    }
  }, [batchDataMongo])

  useEffect(() => {
    if (migrationDataMongo) {
      updateMigrationData(migrationDataMongo)
    }
  }, [migrationDataMongo])

  const getSchedulerDetailsforBatches = async () => {
    if (schedulerIds && schedulerIds.length > 0) {
      const payload = { scheduler_ids: this.schedulerIds }

      try {
        const res = await getSchedulersDetailsBatches(payload).toPromise()
        if (res && res.length > 0) {
          setSchedulerIds(res)
        }
      } catch (error) {
        console.error("Error fetching scheduler details:", error)
      }
    }
  }

  const listenToTopic = (migrationId) => {
    if (websocketService.isOpen()) {
      websocketService
        .listenToTopic(migrationId)
        .then((data) => {
          if (data) {
            const result = data // It's already parsed in the service
            if (result) {
              getWorkflowBatchDetails(migrationSummary.id)
            }
          }
        })
        .catch((error) => {
          console.error("Error in websocket topic:", error)
        })
    }
  }

  const getWorkflowData = async (id) => {
    try {
      const res = await getMigrationById(email, id)
      const temp = res
      if (temp) {
        getWorkflowBatchDetails(id)

        if (temp.status === "started") {
          if (websocketService.isOpen()) {
            listenToTopic("migration:" + temp.migrationId)
          } else {
            try {
              await websocketService.initializeWebSocketConnection()
              if (websocketService.isOpen()) {
                listenToTopic("migration:" + temp.migrationId)
              }
            } catch (error) {
              console.error("WebSocket connection failed:", error)
            }
          }

          if (apiSubscriptionRef.current) {
            apiSubscriptionRef.current.unsubscribe()
          }

          apiSubscriptionRef.current = interval(180000)
            .pipe(switchMap(() => checkBatchMetrics(temp.migrationId, temp.executedBy)))
            .subscribe({
              next: (data) => { },
              error: (error) => { },
            })
        }
      }
    } catch (err) { }
  }

  const getWorkflowBatchDetails = (id) => {
    setIsLoading(true)

    getBatchesByMigration(id)
      .then((res) => {
        const temp = res

        if (temp) {
          const newSchedulerIds = []
          const processedBatchData = temp.map((obj, index) => {
            if (obj.schedulerId && obj.schedulerId !== "") {
              if (!newSchedulerIds.includes(obj.schedulerId)) {
                newSchedulerIds.push(obj.schedulerId)
              }
            }
            return { ...obj, index: index + 1 }
          })

          setBatchDetails(processedBatchData)
          setIsLoading(false)

          if (newSchedulerIds.length > 0) {
            getSchedulerDetailsforBatches()
          }
        }
      })
      .catch((err) => {
        console.error("Error fetching batch details:", err)
      })
  }

  const updateMigrationData = (data) => {
    if (!data?.fullDocument?.migrationId || !migrationData?.migrationId) {
      return
    }

    if (data.fullDocument.migrationId === migrationData.migrationId) {
      const passed = data.fullDocument.passed || 0
      const failed = data.fullDocument.failed || 0
      const totalRecords = data.fullDocument.totalRecords || migrationStats.totalRecords
      const scheduled = data.fullDocument.scheduled

      setMigrationStats((prevState) => ({
        ...prevState,
        passed: passed,
        failed: failed,
        totalRecords: totalRecords,
        scheduled: scheduled,
      }))

      setMigrationSummary((prevState) => ({
        ...prevState,
        totalRecords: totalRecords,
        migrated: passed,
        failed: failed,
      }))
    }
  }

  const updateBatchData = (data) => {
    if (!data?.fullDocument?.batchId) {
      return
    }

    setBatchDetails((prevBatchDetails) => {
      const updatedBatchDetails = [...prevBatchDetails]

      const index = updatedBatchDetails.findIndex((batch) => batch.batchId === data.fullDocument.batchId)

      if (index === -1) {
        console.warn(`Batch with ID ${data.fullDocument.batchId} not found in current state`)
        return prevBatchDetails
      }
      const status = data.fullDocument.status || updatedBatchDetails[index].status
      const displayStatus = status === "started" || status === "in progress" ? "in progress" : status

      updatedBatchDetails[index] = {
        ...updatedBatchDetails[index],
        scheduled: data.fullDocument.scheduled || updatedBatchDetails[index].scheduled,
        passed: data.fullDocument.passed || updatedBatchDetails[index].passed,
        skipped: data.fullDocument.skipped || updatedBatchDetails[index].skipped,
        failed: data.fullDocument.failed || updatedBatchDetails[index].failed,
        status: displayStatus,
        total: data.fullDocument.totalRecords || updatedBatchDetails[index].total,
      }

      return updatedBatchDetails
    })
  }
  const getWorkflowBatchData = async () => {
    try {
      const response = await getRecordsByBatchId(selectedBatchId)
      const data = response

      if (!data) return

      const updatedRecords = await Promise.all(
        data.map(async (obj, index) => {
          obj.index = index + 1

          const isPending = !["success", "error", "skipped"].includes(obj.status)
          if (isPending) {
            if (websocketService.isOpen()) {
              listenToTopic(obj.subStepId)
            } else {
              try {
                await websocketService.initializeWebSocketConnection()
                if (websocketService.isOpen()) {
                  listenToTopic(obj.subStepId)
                }
              } catch (error) {
                console.error("WebSocket connection failed:", error)
              }
            }
          }

          return obj
        }),
      )
      const responseBatch = await getBatchesById(selectedBatchId, true)

      setBatchRecords(updatedRecords)
      console.log(responseBatch, "res")
      setBatchExecutionData((prevState) => ({
        ...prevState,
        totalRecords: responseBatch.totalRecords,
        passed: responseBatch.passed,
        failed: responseBatch.failed,
        skipped: responseBatch.skipped || 0,
        errors: responseBatch.failed,
        migrated: responseBatch.passed,
      }))
      const res = await getMigrationById(email, migrationData.migrationId)
      setMigrationStats((prevState) => ({
        ...prevState,
        totalRecords: res.totalRecords,
        passed: res.passed,
        failed: res.failed,
        status: res.status || "started",
        scheduled: res.scheduled,
      }))
    } catch (error) {
      console.error("Error while fetching workflow details", error)
    }
  }

  const retryRecord = (id) => {
    if (id) {
      const payload = { errorId: id }
      setRetryRecordLoading(true)
      setRetryRecordId(id)

      retryRecordMigration(payload)
        .then((res) => {
          const temp = res
          setRetryRecordLoading(false)
          setRetryRecordId(null)

          if (temp && temp.message === "success") {
            getWorkflowBatchData()
            // After a successful retry, fetch the conversations
            if (selectedBatchId) {
              const record = batchRecords.find((record) => record.errorId === id)
              if (record) {
                fetchSubTableData(record.sourceId, record.batchId, record.id)
              }
            }
          }
        })
        .catch((err) => {
          getWorkflowBatchData()
          setRetryRecordLoading(false)
          setRetryRecordId(null)
        })
    }
  }

  const retryBatch = () => {
    if (selectedBatchId) {
      const payload = {
        batchId: selectedBatchId,
      }

      retryBatchMigration(payload)
        .then((res) => {
          const temp = res
          setRetryRecordLoading(false)
          setRetryRecordId(null)

          if (temp && temp.message === "success") {
            getWorkflowBatchData()
          }
        })
        .catch((err) => {
          getWorkflowBatchData()
          setRetryRecordLoading(false)
          setRetryRecordId(null)
        })
    }
  }

  const fetchSubTableData = async (sourceId, batchId, targetId) => {
    if (!sourceId || !targetId) return

    setSubIsLoading((prev) => ({ ...prev, [sourceId]: true }))

    try {
      console.log(`Fetching conversations for sourceId: ${sourceId}, batchId: ${batchId}, targetId: ${targetId}`)
      const response = await getRecordByBatchConversations(batchId, targetId)
      console.log("Conversations API Response:", response)
      console.log("Response structure:", {
        hasConversations: !!response?.conversations,
        conversationsLength: response?.conversations?.length || 0,
        responseKeys: response ? Object.keys(response) : [],
      })

      if (response && response.conversations) {
        setSubTableData((prev) => ({
          ...prev,
          [sourceId]: response.conversations,
        }))
      } else {
        setSubTableData((prev) => ({
          ...prev,
          [sourceId]: [],
        }))
      }
    } catch (error) {
      console.error("Error fetching sub-table data:", error)
      setSubTableData((prev) => ({
        ...prev,
        [sourceId]: [],
      }))
    } finally {
      setSubIsLoading((prev) => ({ ...prev, [sourceId]: false }))
    }
  }

  const handleBatchNameClick = async (batchId) => {
    setSelectedBatchId(batchId)
    setShowBatchExecution(true)
    await fetchBatchExecutionData(batchId)
    await fetchBatchRecords(batchId)
  }

  const handleBackFromBatchExecution = () => {
    setShowBatchExecution(false)
    setSelectedBatchId(null)
  }

  const toggleFilterDropdown = () => {
    setShowFilterDropdown(!showFilterDropdown)
  }

  const handleFilterSelect = (filter) => {
    setStatusFilter(filter)
    setShowFilterDropdown(false)
    setCurrentPage(0)
    setBatchDetailsCurrentPage(0)
  }

  const getFilterDisplayText = () => {
    switch (statusFilter) {
      case "passed":
        return "Success"
      case "failed":
        return "Failed"
      default:
        return "See all"
    }
  }

  const getFilteredBatchRecords = () => {
    if (statusFilter === "all") return batchRecords

    return batchRecords.filter((record) => {
      if (statusFilter === "passed") {
        return record.status && record.status.toLowerCase() === "success"
      } else if (statusFilter === "failed") {
        return record.status && record.status.toLowerCase() != "success"
      }
      return true
    })
  }

  const getFilteredBatchDetails = () => {
    if (statusFilter === "all" || showBatchExecution) return batchDetails

    return batchDetails.filter((batch) => {
      if (statusFilter === "passed") {
        return batch.status && (batch.status.toLowerCase() === "passed" || batch.status.toLowerCase() === "completed")
      } else if (statusFilter === "failed") {
        return batch.status && batch.status.toLowerCase() !== "completed"
      }
      return true
    })
  }

  const getPaginatedBatchRecords = () => {
    const filteredRecords = getFilteredBatchRecords()
    const startIndex = currentPage * rowsPerPage
    const endIndex = startIndex + rowsPerPage
    return filteredRecords.slice(startIndex, endIndex)
  }

  const getPaginatedBatchDetails = () => {
    const filteredDetails = getFilteredBatchDetails()
    const startIndex = batchDetailsCurrentPage * batchDetailsRowsPerPage
    const endIndex = startIndex + batchDetailsRowsPerPage
    return filteredDetails.slice(startIndex, endIndex)
  }

  const getTotalPages = () => {
    return Math.ceil(getFilteredBatchRecords().length / rowsPerPage)
  }

  const getBatchDetailsTotalPages = () => {
    return Math.ceil(getFilteredBatchDetails().length / batchDetailsRowsPerPage)
  }

  const handlePageChange = (pageNumber) => {
    const totalPages = getTotalPages()
    if (pageNumber >= 0 && pageNumber < totalPages) {
      setCurrentPage(pageNumber)
    }
  }

  const handleRowsPerPageChange = (rows) => {
    setRowsPerPage(rows)
    setCurrentPage(0)
  }

  const handleBatchDetailsPageChange = (pageNumber) => {
    const totalPages = getBatchDetailsTotalPages()
    if (pageNumber >= 0 && pageNumber < totalPages) {
      setBatchDetailsCurrentPage(pageNumber)
    }
  }

  const handleBatchDetailsRowsPerPageChange = (rows) => {
    setBatchDetailsRowsPerPage(rows)
    setBatchDetailsCurrentPage(0)
  }

  const getVisiblePages = (currentPage, totalPages) => {
    const visiblePages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      for (let i = 0; i < totalPages; i++) {
        visiblePages.push(i)
      }
    } else {
      let startPage = Math.max(0, currentPage - Math.floor(maxVisiblePages / 2))
      let endPage = startPage + maxVisiblePages - 1

      if (endPage >= totalPages) {
        endPage = totalPages - 1
        startPage = Math.max(0, endPage - maxVisiblePages + 1)
      }

      if (startPage > 0) {
        visiblePages.push(0)
        if (startPage > 1) {
          visiblePages.push("ellipsis-start")
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        visiblePages.push(i)
      }

      if (endPage < totalPages - 1) {
        if (endPage < totalPages - 2) {
          visiblePages.push("ellipsis-end")
        }
        visiblePages.push(totalPages - 1)
      }
    }

    return visiblePages
  }
  const getFailedBatchIds = () => {
    const temp = []
    if (batchDetails) {
      for (const batch of batchDetails) {
        if (batch.failed > 0) {
          temp.push(batch.batchId)
        }
      }
    }
    return temp
  }
  const retryAllBatch = async () => {
    const payload = {
      batchIds: getFailedBatchIds(),
    }

    try {
      await retryPlanMigration(payload)
      getWorkflowBatchDetails(migrationSummary.id)
    } catch (error) {
      getWorkflowBatchDetails(migrationSummary.id)
    }
  }

  const toggleRowExpand = (batchId, sourceId, targetId) => {
    console.log(`Toggling row: batchId=${batchId}, sourceId=${sourceId}, targetId=${targetId}`)
    const willExpand = !expandedRows[sourceId]

    setExpandedRows((prev) => ({
      ...prev,
      [sourceId]: willExpand,
    }))

    if (willExpand) {
      // Always fetch fresh data when expanding
      fetchSubTableData(sourceId, batchId, targetId)
    }
  }

  const handleClose = (model) => {
    setOpenApiResult(false)

    // getWorkflowData(migrationData.migrationId)
    //   .then((res) => {
    //     const resPayload = res.data
    //     const payload = resPayload[0].payload

    //     payload.transformationPlanId = batchDetails.transformationPlanId
    //     payload.transformationId = batchDetails.transformationId
    //     payload.migrationId = batchDetails.migrationId
    //     payload.batchId = batchDetails.batchId
    //     payload.transformationModels[0].source[0].sourceExecutor = model

    //     transformData(payload)
    //   })
    //   .catch((err) => {
    //     console.error("Workflow fetch error:", err)
    //   })
  }
  const openPayloadDialog = async (log, text) => {
    if (log.errorId) {
      try {
        const errorData = await getWorkflowErrorLog(log.errorId)

        setErrorLogData(errorData.response[0].transformationInputModel.source)
        setSelectedPayload(log)
        setIsDialogOpen(true)
        console.log("Error Log Data:", errorData.response[0].transformationInputModel.source)
      } catch (error) {
        console.error("Error fetching error log:", error)
      }
    } else if (text === "batch") {
      setSelectedPayload("")
      setIsDialogOpen(true)
    } else {
      setSelectedPayload(log)
      setIsDialogOpen(true)
    }
  }

  const generatePieChartData = () => {
    const passed = Number.parseInt(migrationStats.passed) || 0
    const failed = Number.parseInt(migrationStats.scheduled) - Number.parseInt(migrationStats.passed) || 0
    const totalRecords = Number.parseInt(migrationStats.totalRecords) || 0
    const pending = Math.max(0, totalRecords - (passed + failed))

    const data = [
      { name: "Migrated", value: passed, color: "#EF8963" },
      { name: "Failed", value: failed, color: "#A43E18" },
      { name: "Pending", value: pending, color: "#E0E0E0" },
    ].filter((item) => item.value > 0)

    return data.length > 0 ? data : [{ name: "No Data", value: 1, color: "#E0E0E0" }]
  }

  const generateBatchPieChartData = () => {
    const passed = Number.parseInt(batchExecutionData.passed) || 0
    const failed = Number.parseInt(batchExecutionData.totalRecords) - Number.parseInt(batchExecutionData.passed) || 0
    const skipped = Number.parseInt(batchExecutionData.skipped) || 0

    const data = [
      { name: "Passed", value: passed, color: "#EF8963" },
      { name: "Failed", value: failed, color: "#A43E18" },
      { name: "Skipped", value: skipped, color: "#FFC107" },
    ].filter((item) => item.value > 0)

    return data.length > 0 ? data : [{ name: "No Data", value: 1, color: "#E0E0E0" }]
  }

  const getStatusBadgeClass = (status) => {
    if (!migration) {
      return { text: "Unknown", className: styles.unknown }
    }

    if (migration.passed === migration.totalRecords) {
      return { text: "Completed", className: styles.completed }
    } else if (migration.passed !== migration.totalRecords && migration.passed > 0) {
      return { text: "Partially Completed", className: styles.partiallycompleted }
    } else if (migration.passed === 0 && migration.status === "scheduled") {
      return { text: "Scheduled", className: styles.scheduled }
    } else if (migration.passed === 0 && migration.failed > 0) {
      return { text: "Failed", className: styles.failed }
    } else {
      return { text: "Unknown", className: styles.unknown }
    }
  }

  const getStatusClass = (status) => {
    if (!status) return styles.unknown

    switch (status.toLowerCase()) {
      case "completed":
      case "success":
      case "passed":
        return styles.completed
      case "failed":
        return styles.failed
      case "skipped":
      case "in progress":
        return styles.inProgress
      case "pending":
        return styles.unknown
      case "scheduled":
        return styles.scheduled
      default:
        return styles.unknown
    }
  }
  const getStatusDetails = (batch) => {
    if (!batch) {
      return { text: "Unknown", className: styles.unknown }
    }

    const status = batch.status?.toLowerCase() || ""
    const { passed = 0, failed = 0, totalRecords = 0 } = batch
    if (status === "in progress" || status === "started") {
      return { text: "In Progress", className: styles.inProgress }
    } else if (status === "scheduled") {
      return { text: "Scheduled", className: styles.scheduled }
    } else if (passed === totalRecords && totalRecords > 0) {
      return { text: "Completed", className: styles.completed }
    } else if (passed > 0 && passed !== totalRecords) {
      return { text: "Partially Completed", className: styles.partiallycompleted }
    } else if (passed === 0 && failed > 0) {
      return { text: "Failed", className: styles.failed }
    } else if (status === "pending") {
      return { text: "Pending", className: styles.unknown }
    } else {
      return { text: "Unknown", className: styles.unknown }
    }
  }

  const getRowClass = (status) => {
    if (!status) return ""

    switch (status.toLowerCase()) {
      case "completed":
      case "passed":
        return styles.successRow
      case "failed":
        return styles.errorRow
      default:
        return ""
    }
  }
  const renderPagination = (
    currentPage,
    totalPages,
    handlePageChange,
    rowsPerPage,
    handleRowsPerPageChange,
    rowsOptions = [10, 25, 50],
  ) => {
    return (
      <div className={styles.paginationContainer}>
        <div className={styles.pageInfo}>
          <span className={styles.paginationText}>Page no</span>
          <div className={styles.pageButtons}>
            <button className={styles.arrowButton} onClick={() => handlePageChange(0)} disabled={currentPage === 0}>
              <FaAngleDoubleLeft />
            </button>
            <button
              className={styles.arrowButton}
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 0}
            >
              <FaChevronLeft />
            </button>

            {getVisiblePages(currentPage, totalPages).map((page, index) => {
              if (page === "ellipsis-start" || page === "ellipsis-end") {
                return (
                  <span key={index} className={styles.pageEllipsis}>
                    ...
                  </span>
                )
              }
              return (
                <button
                  key={page}
                  className={`${styles.pageButton} ${currentPage === page ? styles.active : ""}`}
                  onClick={() => handlePageChange(page)}
                >
                  {(page + 1).toString().padStart(2, "0")}
                </button>
              )
            })}

            <button
              className={styles.arrowButton}
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages - 1}
            >
              <FaChevronRight />
            </button>
            <button
              className={styles.arrowButton}
              onClick={() => handlePageChange(totalPages - 1)}
              disabled={currentPage >= totalPages - 1}
            >
              <FaAngleDoubleRight />
            </button>
          </div>
        </div>
        <div className={styles.rowsPerPageContainer}>
          <span className={styles.paginationText}>Show</span>
          <div className={styles.rowsButtons}>
            {rowsOptions.map((rows) => (
              <button
                key={rows}
                className={`${styles.rowsButton} ${rowsPerPage === rows ? styles.active : ""}`}
                onClick={() => handleRowsPerPageChange(rows)}
              >
                {rows}
              </button>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const activityData = [
    { action: "Template created", timeline: "Date, time stamp", records: "", errors: "", retries: "" },
    {
      action: "Source & target connection successful",
      timeline: "Date, time stamp",
      records: "",
      errors: "",
      retries: "",
    },
    { action: "Field mapping completed", timeline: "Date, time stamp", records: "", errors: "", retries: "" },
    { action: "Mapping review confirmed", timeline: "Date, time stamp", records: "30", errors: "11", retries: "" },
    { action: "Sample migration started", timeline: "Date, time stamp", records: "", errors: "", retries: "" },
    { action: "Sample migration completed", timeline: "Date, time stamp", records: "30", errors: "03", retries: "05" },
    { action: "Full migration scheduled", timeline: "Date, time stamp", records: "500", errors: "12", retries: "" },
  ]

  const paymentData = {
    planSelected: "Pro-Migration",
    paymentDoneVia: "Master Debit/credit card",
    cardNumber: "42** **** **66",
    nameOnCard: "Name on card",
    paymentDoneOn: "15.03.2025; 6:00 PM",
  }

  return (
    <div className={styles.container}>
      {/* Use key prop with timestamp to ensure Sidebar re-renders and navigation works correctly */}
      <Sidebar
        key={`sidebar-details-${Date.now()}`}
        isCollapsed={isSidebarCollapsed}
        setIsCollapsed={setIsSidebarCollapsed}
      />

      <div className={`${styles.mainSection} ${isSidebarCollapsed ? styles.expanded : ""}`}>
        <div className={styles.headerContainer}>
          <div className={styles.titleWithBackButton}>
            <button className={styles.backButton} onClick={onBack}>
              <ArrowLeftIcon className={styles.backIcon} />
            </button>
            <h1 className={globalStyles.headerStyle}>Migration Details</h1>
          </div>
          <div className={styles.headerControls}>
            {/* Search functionality temporarily hidden
              <div className={globalStyles.searchWrapper}>
                <SearchIcon className={globalStyles.searchIcon} />
                <input
                  type="text"
                  placeholder="Search..."
                  className={globalStyles.searchInput}
                  onFocus={(e) => {
                    e.target.style.width = "200px"
                    e.target.placeholder = "Typing..."
                  }}
                  onBlur={(e) => {
                    e.target.style.width = "80px"
                    e.target.placeholder = "Search..."
                  }}
                />
              </div>
              */}

            <div className={globalStyles.searchWrapper}>
              <GlobeIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Eng"
                className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                readOnly
              />
            </div>
          </div>
        </div>

        {/* MIGRATION DETAILS - First section as requested */}
        <div className={styles.collapsibleSection}>
          <div className={styles.collapsibleHeader} onClick={() => toggleSection("batch")}>
            <h2>MIGRATION DETAILS</h2>
            <span className={`${styles.chevron} ${activeSection === "batch" ? styles.chevronUp : ""}`}>
              <img src="/assets/arrow-down-01.png" alt="Chevron Down" className="w-5 h-5" />
            </span>
          </div>          
          {activeSection === "batch" ? (
            <div className={styles.collapsibleContent}>
              {/* MIGRATION REPORT moved to top */}
              <div className={styles.migrationReportSection}>
                <div className={styles.detailsGrid}>
                  <div className={styles.detailRow}>
                    <div className={styles.detailColumn}>
                      <div className={styles.detailLabel}>Migration name</div>
                      <div className={styles.detailValue}>{migrationData.migrationName}</div>
                    </div>
                    <div className={styles.detailColumn}>
                      <div className={styles.detailLabel}>Migration ID</div>
                      <div className={styles.detailValue}>{migrationData.migrationId}</div>
                    </div>
                  </div>

                  <div className={styles.detailRow}>
                    <div className={styles.detailColumn}>
                      <div className={styles.detailLabel}>Template used</div>
                      <div className={styles.detailValue}>{migrationData.templateUsed}</div>
                    </div>
                    <div className={styles.detailColumn}>
                      <div className={styles.detailLabel}>Source to Target</div>
                      <div className={styles.detailValue}>{migrationData.sourcePlatform} → {migrationData.targetPlatform}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Migration Summary moved below Migration Report */}
              <MigrationSummary migrationId={migrationData.migrationId} isPolling={true} />
            </div>
          ) : (
            <div className={styles.seeMoreContainer}>
              <span className={styles.seeMore}>See more</span>
            </div>
          )}
        </div>

        {/* PAYMENT DETAILS - Second section as requested */}
        <div className={styles.collapsibleSection}>
          <div className={styles.collapsibleHeader} onClick={() => toggleSection("payment")}>
            <h2>PAYMENT DETAILS</h2>
            <span className={`${styles.chevron} ${activeSection === "payment" ? styles.chevronUp : ""}`}>
              <img src="/assets/arrow-down-01.png" alt="Chevron Down" className="w-5 h-5" />
            </span>
          </div>
          {activeSection === "payment" ? (
            <div className={styles.collapsibleContent}>
              <div className={styles.paymentDetailsContainer}>
                <div className={styles.paymentDetailsLeft}></div>
                <div className={styles.paymentDetailsRight}>
                  <div className={styles.paymentDetailRow}>
                    <div className={styles.paymentDetailLabel}>Plan selected</div>
                    <div className={styles.paymentDetailValue}>{paymentData.planSelected}</div>
                  </div>
                  <div className={styles.paymentDetailRow}>
                    <div className={styles.paymentDetailLabel}>Payment done via</div>
                    <div className={styles.paymentDetailValue}>{paymentData.paymentDoneVia}</div>
                  </div>
                  <div className={styles.paymentDetailRow}>
                    <div className={styles.paymentDetailLabel}>Card number</div>
                    <div className={styles.paymentDetailValue}>{paymentData.cardNumber}</div>
                  </div>
                  <div className={styles.paymentDetailRow}>
                    <div className={styles.paymentDetailLabel}>Name on card</div>
                    <div className={styles.paymentDetailValue}>{paymentData.nameOnCard}</div>
                  </div>
                  <div className={styles.paymentDetailRow}>
                    <div className={styles.paymentDetailLabel}>Payment done on</div>
                    <div className={styles.paymentDetailValue}>{paymentData.paymentDoneOn}</div>
                  </div>
                  <button className={styles.downloadInvoiceButton}>Download Invoice</button>
                </div>
              </div>
            </div>
          ) : (
            <div className={styles.seeMoreContainer}>
              <span className={styles.seeMore}>See more</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default MigrationDetails
