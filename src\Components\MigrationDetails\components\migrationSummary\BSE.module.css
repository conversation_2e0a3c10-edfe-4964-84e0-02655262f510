/* BSE.module.css - Styles for SimilarUI component */

/* Base styles with Inter font family */
:root {
  --font-inter: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Tab container styles */
.tabsContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  gap: 2px;
  margin: 1px 0;
  padding: 5px;
  position: relative;
  background-color: #f5f5f5;
  border-radius: 4px;
  width: fit-content;
}

/* Individual tab button styles */
.tabButton {
  padding: 8px 16px;
  border: none;
  background-color: transparent;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  color: #666;
  transition: all 0.2s ease;
  position: relative;
  letter-spacing: 0.5px;
  text-transform: capitalize;
  border-radius: 4px;
  white-space: nowrap;
  flex: 0 0 auto;
  font-family: var(--font-inter);
}

.tabButton:hover {
  color: #ef8963;
  background-color: rgba(48, 127, 226, 0.05);
}

/* Active tab styling */
.activeTab {
  color: white;
  background-color: #ef8963;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.activeTab:hover {
  color: white;
  background-color: #ef8963;
}

/* Status indicator styles */
.statusIndicator {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  font-family: var(--font-inter);
}

/* Status classes */
.successStatus {
  background-color: #2f855a;
  color: white;
}

.errorStatus {
  background-color: rgba(164, 62, 24, 0.2);
  color: #a43e18;
}

/* Row styling */
.successRow {
  background-color: rgba(47, 133, 90, 0.05);
}

.errorRow {
  background-color: rgba(164, 62, 24, 0.05);
}

/* Button styling */
.payloadButton {
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.payloadButton:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Expanded row/content styling */
.expandedRow {
  background-color: #f9f9f9;
}

.expandedContent {
  padding: 10px;
}

/* Table styles */
.subTable {
  width: 100%;
  border-collapse: collapse;
}

.subTable th, .subTable td {
  padding: 8px 12px;
  text-align: left;
}

/* No records message */
.noRecordsCell {
  text-align: center;
  padding: 20px;
  color: #888;
}

/* Filter-related styles */
.filterContainer {
  position: relative;
}

.filterDropdownContainer {
  position: relative;
  display: inline-block;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  font-family: var(--font-inter);
}

.filterIcon {
  font-size: 16px;
}

.filterDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 10;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 150px;
}

.filterOption {
  padding: 8px 12px;
  cursor: pointer;
  font-family: var(--font-inter);
}

.filterOption:hover {
  background-color: #f5f5f5;
}

/* Pagination styles */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.paginationButton {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  font-family: var(--font-inter);
}

.paginationButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationInfo {
  font-size: 14px;
  color: #666;
  font-family: var(--font-inter);
}

.rowsPerPageSelect {
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-left: 10px;
  font-family: var(--font-inter);
}

/* Entity table container styles */
.entityTableContainer {
  margin-bottom: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.entityTableHeader {
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px 8px 0 0;
}

.entityTableTitle {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-transform: capitalize;
  font-family: var(--font-inter);
}

.entityRecordCount {
  font-size: 14px;
  color: #666;
  font-weight: 400;
  margin-left: 8px;
}

/* Entity type specific styling */
.entityTableContainer[data-entity="tickets"] .entityTableHeader {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%);
  border-bottom-color: #2196f3;
}

.entityTableContainer[data-entity="changes"] .entityTableHeader {
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
  border-bottom-color: #ff9800;
}

.entityTableContainer[data-entity="users"] .entityTableHeader {
  background: linear-gradient(135deg, #f3e5f5 0%, #e8f5e8 100%);
  border-bottom-color: #9c27b0;
}

.entityTableContainer[data-entity="organizations"] .entityTableHeader {
  background: linear-gradient(135deg, #e8f5e8 0%, #f3e5f5 100%);
  border-bottom-color: #4caf50;
}

.entityTableContainer[data-entity="tickets"] .entityTableTitle {
  color: #1976d2;
}

.entityTableContainer[data-entity="changes"] .entityTableTitle {
  color: #f57c00;
}

.entityTableContainer[data-entity="users"] .entityTableTitle {
  color: #7b1fa2;
}

.entityTableContainer[data-entity="organizations"] .entityTableTitle {
  color: #388e3c;
}

/* Table content area */
.entityTableContent {
  padding: 0;
  background-color: white;
  border-radius: 0 0 8px 8px;
}

/* Entity summary card styles */
.entitySummaryCard {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  min-width: 140px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.entitySummaryCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.entitySummaryHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.entitySummaryStats {
  display: flex;
  justify-content: center;
}

/* Entity type specific colors for summary cards */
.entitySummaryCard[data-entity="tickets"] {
  border-left: 4px solid #2196f3;
}

.entitySummaryCard[data-entity="changes"] {
  border-left: 4px solid #ff9800;
}

/* Additional entity types can be added here */
.entitySummaryCard[data-entity="users"] {
  border-left: 4px solid #9c27b0;
}

.entitySummaryCard[data-entity="organizations"] {
  border-left: 4px solid #4caf50;
}
