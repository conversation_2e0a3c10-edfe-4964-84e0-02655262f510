import { useContext, useEffect, useState } from "react"
import styles from "./Data-type.module.css"
import globalStyles from "../../globalStyles.module.css"
import { HiXMark, HiTrash, HiDocument } from "react-icons/hi2"
import { Dialog, DialogContent } from "@mui/material"
import {
  getMigrationEntities,
  getMigrationObjects,
  getMigrationObjectsById,
  getMigrationTargetById,
  getUniqueValuesFromCSV,
  saveMigrationPlan,
  validateSourceData,
  getMigrationObjectsBulkFetch,
  getMigrationTargetsBulkFetch,
  migrationTemplate,
} from "../../apiService"
import { MigrationContext } from "../Data-Migration"
import UploadCSV from "../UploadCSV/uploadcsv"
import { toast, ToastContainer } from "react-toastify"
import LoaderSpinner from "../../loaderspinner"
import { displayArticle, executeApi } from "../../../Helper/helper"
import { BlobServiceClient } from "@azure/storage-blob"

const deleteFileFromAzureBlob = async (fileUrl) => {
  try {
    const SAS_URL =
      "https://saasgeniestorage.blob.core.windows.net/?sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2025-12-31T13:47:32Z&st=2025-06-02T05:47:32Z&spr=https,http&sig=3lxpy88Cx8o0JfTZDtYk54ESpIZElAmlCTmeZKkwh6s%3D"
    const blobServiceClient = new BlobServiceClient(SAS_URL)

    const url = new URL(fileUrl)
    const containerName = "migrategenie-v2-migrations-data"
    const blobName = "csv/" + decodeURIComponent(url.pathname.split("/").pop())

    const containerClient = blobServiceClient.getContainerClient(containerName)
    const blockBlobClient = containerClient.getBlockBlobClient(blobName)

    await blockBlobClient.delete()
    console.log(`File deleted successfully: ${blobName}`)
  } catch (error) {
    console.error("Error deleting file from Azure Blob Storage:", error)
    throw error
  }
}

export default function Datatype({
  setSelectedTab,
  setDependentFields,
  dependentFields,
  templateName,
  planId,
  setPlanId,
}) {
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const dataTypeData = migrationState.dataTypeData
  const source = migrationState.sourceObjData
  const target = migrationState.targetData
  const selectedObjectData = migrationState.selectedObjectData

  const [email, setEmail] = useState(localStorage.getItem("email"))
  const [selectedData, setSelectedData] = useState([])
  const [entityData, setEntityData] = useState([])
  const [objectData, setObjectData] = useState([])
  const [selectedObject, setSelectedObject] = useState(null)
  const [selectedObjectName, setSelectedObjectName] = useState(null)
  const [selectedObjectIndex, setSelectedObjectIndex] = useState(null)
  const [checked, setChecked] = useState(false)
  const [numberOfRecords, setNumberOfRecords] = useState("")
  const [confirm, setConfirm] = useState(false)
  const [showUploadCSV, setShowUploadCSV] = useState(false)
  const [uploadedFileData, setUploadedFileData] = useState(null)
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [currentUploadEntity, setCurrentUploadEntity] = useState(null)
  const [pendingEntities, setPendingEntities] = useState([])
  const [isLastEntity, setIsLastEntity] = useState(false)
  const [allFilesUploaded, setAllFilesUploaded] = useState(false)
  const [requiredEntities, setRequiredEntities] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [isEntityDataLoading, setIsEntityDataLoading] = useState(true)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [fileToDelete, setFileToDelete] = useState({ index: null, name: "" })
  const [showReplaceConfirmation, setShowReplaceConfirmation] = useState(false)
  const [entityToReplace, setEntityToReplace] = useState(null)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [isConfirmLoading, setIsConfirmLoading] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [isCSVSource, setIsCSVSource] = useState(false)

  // State for main entity record count (only for validation)
  const [mainEntityRecordCount, setMainEntityRecordCount] = useState(0)

  // State for UUID column selection
  const [uuidColumns, setUuidColumns] = useState([])
  const [showUuidSelection, setShowUuidSelection] = useState(false)

  useEffect(() => {
    setIsCSVSource(source?.source?.name?.toLowerCase() === "csv")
  }, [source])

  // Track main entity record count for validation
  useEffect(() => {
    if (isCSVSource && uploadedFiles.length > 0 && selectedObjectName) {
      const mainEntityFile = uploadedFiles.find((file) => file.entity === selectedObjectName)
      if (mainEntityFile && mainEntityFile.recordCount) {
        setMainEntityRecordCount(mainEntityFile.recordCount)
        setNumberOfRecords(mainEntityFile.recordCount.toString()) // Always sync numberOfRecords for CSV
      }
    }
  }, [uploadedFiles, selectedObjectName, isCSVSource])

  // Initialize UUID columns when files are uploaded and all files are ready
  useEffect(() => {
    if (isCSVSource && allFilesUploaded && uploadedFiles.length > 0) {
      const requiredEnts = [selectedObjectName, ...(dependentFields || [])]
      const existingColumns = uuidColumns.filter((col) => requiredEnts.includes(col.name))

      // Create UUID column entries for entities that don't have one
      const newColumns = requiredEnts.map((entityName) => {
        const existingColumn = existingColumns.find((col) => col.name === entityName)
        if (existingColumn) {
          return existingColumn
        }

        const entityFile = uploadedFiles.find((file) => file.entity === entityName)
        const headers = entityFile ? entityFile.headers : []

        return {
          name: entityName,
          uuidColumn: "",
          isMainTarget: entityName === selectedObjectName,
          availableColumns: headers || [],
        }
      })

      setUuidColumns(newColumns)
      setShowUuidSelection(true)
    } else {
      setShowUuidSelection(false)
    }
  }, [isCSVSource, allFilesUploaded, uploadedFiles, selectedObjectName, dependentFields])

  // Function to handle record count updates from CSV uploads
  const handleRecordCountUpdate = (entityName, recordCount) => {
    // Only update main entity record count for validation
    if (entityName === selectedObjectName) {
      setMainEntityRecordCount(recordCount)
    }
  }

  // Handle UUID column selection
  const handleUuidColumnChange = (entityName, columnName) => {
    setUuidColumns((prev) =>
      prev.map((col) =>
        col.name === entityName
          ? { ...col, uuidColumn: columnName }
          : col,
      ),
    )
  }

  const handleConfirm = () => {
    setIsConfirmLoading(true)

    const isCSVSource = source?.source?.name?.toLowerCase() === "csv"
    const required = [selectedObjectName, ...(dependentFields || [])]
    setRequiredEntities(required)

    if (isCSVSource) {
      const uploadedEntityNames = [...new Set(uploadedFiles.map((file) => file.entity))]
      const allUploaded = required.every((entity) => uploadedEntityNames.includes(entity))

      setAllFilesUploaded(allUploaded)

      if (allUploaded) {
        setTimeout(() => {
          setConfirm(true)
          setIsConfirmLoading(false)
        }, 500)
      } else {
        setIsConfirmLoading(false)
        if (!uploadedEntityNames.includes(selectedObjectName)) {
          setCurrentUploadEntity(selectedObjectName)
          const pendingEnts =
            selectedObject?.dependent
              ?.filter((val) => (dependentFields || []).includes(val) && !uploadedEntityNames.includes(val))
              ?.map((entityName) => entityName) || []
          setPendingEntities(pendingEnts)
          setIsLastEntity(pendingEnts.length === 0)
        } else {
          const pendingEnts = (dependentFields || []).filter((dep) => !uploadedEntityNames.includes(dep))
          if (pendingEnts.length > 0) {
            setCurrentUploadEntity(pendingEnts[0])
            setPendingEntities(pendingEnts.slice(1))
            setIsLastEntity(pendingEnts.length <= 1)
          } else {
            setAllFilesUploaded(true)
            setConfirm(true)
            return
          }
        }
        setShowUploadCSV(true)
      }
    } else {
      setTimeout(() => {
        setConfirm(true)
        setIsConfirmLoading(false)
      }, 500)
    }
  }

  const sortUploadedFiles = (files) => {
    if (!files || files.length === 0) return []
    const sortedFiles = [...files]
    const mainEntityIndex = sortedFiles.findIndex((file) => file.entity === selectedObjectName)
    if (mainEntityIndex > 0) {
      const mainEntityFile = sortedFiles.splice(mainEntityIndex, 1)[0]
      sortedFiles.unshift(mainEntityFile)
    }
    return sortedFiles
  }
  const handleFileUploaded = (fileData) => {
    // Use recordCount as the primary source, with length as fallback for backward compatibility
    const recordCount = fileData.recordCount !== undefined ? fileData.recordCount : (fileData.length || 0)

    // Validate record count for main entity (tickets, changes, etc.)
    if (currentUploadEntity === selectedObjectName && recordCount > 10000000) {
      toast.error(
        `${currentUploadEntity} file contains ${recordCount.toLocaleString()} records, which exceeds the maximum limit of 10000000 records. Please reduce the number of records.`,
        {
          position: "top-right",
          autoClose: 5000,
        },
      )
      return
    }
    const fileWithEntity = {
      ...fileData,
      entity: currentUploadEntity,
      uploadedAt: new Date().toISOString(),
      recordCount: recordCount,
    }
    const existingFileIndex = uploadedFiles.findIndex((file) => file.entity === currentUploadEntity)
    let newUploadedFiles
    if (existingFileIndex !== -1) {
      // Only replace the file in the array, do not delete from Azure Blob
      newUploadedFiles = [...uploadedFiles]
      newUploadedFiles[existingFileIndex] = fileWithEntity
    } else {
      newUploadedFiles = [...uploadedFiles, fileWithEntity]
    }
    const sortedFiles = sortUploadedFiles(newUploadedFiles)
    setUploadedFiles(sortedFiles)
    setUploadedFileData(fileWithEntity)
    setShowUploadCSV(false)
    // Update numberOfRecords for main entity (CSV only)
    if (currentUploadEntity === selectedObjectName && isCSVSource) {
      setNumberOfRecords(recordCount.toString())
    }

    // Update UUID columns with new file headers
    if (isCSVSource && fileData.headers) {
      setUuidColumns(prev =>
        prev.map(col =>
          col.name === currentUploadEntity
            ? { ...col, availableColumns: fileData.headers }
            : col
        )
      )
    }

    const uploadedEntityNames = [...new Set(newUploadedFiles.map((file) => file.entity))]
    const allUploaded = requiredEntities.every((entity) => uploadedEntityNames.includes(entity))
    setAllFilesUploaded(allUploaded)
    const action = existingFileIndex !== -1 ? "replaced" : "uploaded"
    toast.success(`File for ${currentUploadEntity} ${action} successfully!`, {
      position: "top-right",
    })
    if (isLastEntity || allUploaded) {
      setConfirm(true)
    } else {
      if (pendingEntities.length > 0) {
        const nextEntity = pendingEntities[0]
        const remainingEntities = pendingEntities.slice(1)
        setTimeout(() => {
          setCurrentUploadEntity(nextEntity)
          setPendingEntities(remainingEntities)
          setIsLastEntity(remainingEntities.length === 0)
          setShowUploadCSV(true)
        }, 100)
      }
    }
  }

  const handleDeleteConfirmation = (index, fileName) => {
    setFileToDelete({ index, name: fileName })
    setShowDeleteConfirmation(true)
  }

  const handleDeleteFile = async () => {
    if (fileToDelete.index !== null) {
      const fileToRemove = uploadedFiles[fileToDelete.index]
      const entityToRemove = fileToRemove.entity

      try {
        if (fileToRemove.azureBlobUrl) {
          await deleteFileFromAzureBlob(fileToRemove.azureBlobUrl)
        }

        const updatedFiles = [...uploadedFiles]
        updatedFiles.splice(fileToDelete.index, 1)
        const sortedFiles = sortUploadedFiles(updatedFiles)
        setUploadedFiles(sortedFiles)

        const updatedEntities = [...new Set(updatedFiles.map((file) => file.entity))]
        const allUploaded = requiredEntities.every((entity) => updatedEntities.includes(entity))
        setAllFilesUploaded(allUploaded)        // Update main entity record count if main entity file was deleted
        if (entityToRemove === selectedObjectName) {
          setMainEntityRecordCount(0)
          setNumberOfRecords("") // Reset numberOfRecords for main entity
        }

        setShowDeleteConfirmation(false)
        toast.success(`File for ${entityToRemove} deleted successfully!`, {
          position: "top-right",
        })

        setCurrentUploadEntity(entityToRemove)
        setShowUploadCSV(true)
      } catch (error) {
        console.error("Error deleting file:", error)
        toast.error("Failed to delete file. Please try again.")
      }
    }
  }

  const closeUploadDialog = () => {
    setShowUploadCSV(false)
  }

  const handleUploadForEntity = (entity) => {
    // Check if there's already a file uploaded for this entity
    const existingFile = uploadedFiles.find((file) => file.entity === entity)

    if (existingFile) {
      // Show confirmation dialog if file already exists
      setEntityToReplace(entity)
      setShowReplaceConfirmation(true)
    } else {
      // Proceed with upload if no existing file
      setCurrentUploadEntity(entity)
      setShowUploadCSV(true)
    }
  }

  const handleConfirmReplace = () => {
    setCurrentUploadEntity(entityToReplace)
    setShowUploadCSV(true)
    setShowReplaceConfirmation(false)
    setEntityToReplace(null)
  }

  const handleCancelReplace = () => {
    setShowReplaceConfirmation(false)
    setEntityToReplace(null)
  }

  useEffect(() => {
    if (dataTypeData) {
      setChecked(dataTypeData.checked)
      setSelectedObjectName(dataTypeData.selectedObjectName)
      setSelectedObject(dataTypeData.selectedObject)
      setSelectedObjectIndex(dataTypeData.selectedObjectIndex)
      setEntityData(dataTypeData.entityData)
      setObjectData(dataTypeData.objectData)
      setNumberOfRecords(dataTypeData.numberOfRecords)
      setDependentFields(dataTypeData.dependentFields)
      setConfirm(dataTypeData.confirm)
      if (dataTypeData.uploadedFiles) {
        const sortedFiles = sortUploadedFiles(dataTypeData.uploadedFiles)
        setUploadedFiles(sortedFiles)
        // For CSV sources, update numberOfRecords from main entity file if available
        if (isCSVSource && dataTypeData.selectedObjectName) {
          const mainEntityFile = sortedFiles.find((file) => file.entity === dataTypeData.selectedObjectName)
          if (mainEntityFile && mainEntityFile.recordCount) {
            setNumberOfRecords(mainEntityFile.recordCount.toString())
          }
        }
      }

      // Restore main entity record count
      if (dataTypeData.mainEntityRecordCount) {
        setMainEntityRecordCount(dataTypeData.mainEntityRecordCount)
      }

      // Restore UUID columns
      if (dataTypeData.uuidColumns) {
        setUuidColumns(dataTypeData.uuidColumns)
        setShowUuidSelection(isCSVSource && dataTypeData.uuidColumns.length > 0)
      }

      if (dataTypeData.selectedObjectName && dataTypeData.dependentFields) {
        setRequiredEntities([dataTypeData.selectedObjectName, ...(dataTypeData.dependentFields || [])])
      }

      if (dataTypeData.entityData) {
        const entities =
          dataTypeData.entityData?.migrationMapping?.flatMap(
            (mapping) =>
              mapping.sourceField?.map((field) => ({
                name: field.entity,
                dependent: field.dependentEntities || [],
              })) || [],
          ) || []
        setSelectedData(entities)
      }

      setIsEntityDataLoading(false)
    }
  }, [dataTypeData])

  useEffect(() => {
    if (uploadedFiles.length > 0 && requiredEntities.length > 0) {
      const uploadedEntityNames = [...new Set(uploadedFiles.map((file) => file.entity))]
      const allUploaded = requiredEntities.every((entity) => uploadedEntityNames.includes(entity))
      setAllFilesUploaded(allUploaded)
    } else {
      setAllFilesUploaded(false)
    }
  }, [uploadedFiles, requiredEntities])

  useEffect(() => {
    if (uploadedFiles.length > 0 && selectedObjectName) {
      const sortedFiles = sortUploadedFiles(uploadedFiles)
      const hasOrderChanged = JSON.stringify(sortedFiles) !== JSON.stringify(uploadedFiles)
      if (hasOrderChanged) {
        setUploadedFiles(sortedFiles)
      }
    }
  }, [selectedObjectName])

  const moveToNext = async () => {
    try {
      setIsValidating(true)

      const obj = {
        selectedObject: selectedData,
        selectedObjectName: selectedObjectName,
        checked: checked,
        selectedObjectIndex: selectedObjectIndex,
        entityData: entityData,
        objectData: objectData,
        numberOfRecords: numberOfRecords,
        dependentFields: dependentFields,
        confirm: confirm,
        uploadedFiles: uploadedFiles,
        mainEntityRecordCount: mainEntityRecordCount, // Save main entity record count
        uuidColumns: uuidColumns, // Save UUID column selections
      }

      if (planId) {
        const oldPlan = await migrationTemplate.get({ id: planId })
        const inJson = JSON.parse(oldPlan.metadata)

        const updatedMetadata = {
          ...inJson,
          entityName: selectedObjectName,
          entityCount: numberOfRecords,
          mainEntityRecordCount: mainEntityRecordCount,
        }
        await migrationTemplate.update(planId, {
          metadata: JSON.stringify(updatedMetadata),
        })
      }

      setMigrationState((prevState) => ({
        ...prevState,
        dataTypeData: obj,
      }))

      // Update selectedObjectData with flatFileMainTargetUuid for CSV sources
      if (isCSVSource && uuidColumns.length > 0) {
        const flatFileMainTargetUuid = uuidColumns.map(col => ({
          name: col.name,
          uuidColumn: col.uuidColumn,
          isMainTarget: col.isMainTarget
        }))

        // Directly update the existing selectedObjectData with flatFileMainTargetUuid
        if (migrationState.selectedObjectData) {
          migrationState.selectedObjectData.flatFileMainTargetUuid = flatFileMainTargetUuid
        }
      }

      await saveMigration(obj)

      // Filter selectedEntityData based on user's actual selections
      const originalSelectedEntityData = entityData.migrationMapping[selectedObjectIndex]
      const filteredSelectedEntityData = {
        ...originalSelectedEntityData,
        // Filter dependentTargets to only include targets that correspond to selected dependent fields
        dependentTargets: originalSelectedEntityData.dependentTargets?.filter(target => {
          // Check if this target corresponds to any of the selected dependent fields
          return (dependentFields || []).some(dependentField =>
            target.name?.toLowerCase() === dependentField.toLowerCase()
          )
        }) || []
      }

      // if (source.type === "api") {
      setMigrationState((prevState) => ({
        ...prevState,
        selectedEntityData: filteredSelectedEntityData,
        selectedObjectData: objectData?.migration_objects?.find((obj) => obj.migrationEntity === selectedObjectName),
      }))
      // } else {
      //   setMigrationState((prevState) => ({
      //     ...prevState,
      //     selectedEntityData: filteredSelectedEntityData,
      //     selectedObjectData: objectData?.migration_objects[0],
      //   }))
      // }

      await new Promise((resolve) => setTimeout(resolve, 100))

      const updatedObjectData =
        source.type === "api"
          ? objectData?.migration_objects?.find((obj) => obj.migrationEntity === selectedObjectName)
          : objectData?.migration_objects[0]

      if (updatedObjectData) {
        const apiCallsPromise = new Promise((resolve) => {
          setMigrationState((prev) => ({
            ...prev,
            apiCallsPending: true,
            apiCallsCompleteCallback: resolve,
          }))
        })
        await apiCallsPromise
      }

      let validationResult = true
      if (source.type === "api") {
        validationResult = await validateData()
      }

      if (validationResult) {
        setSelectedTab("4")
      } else {
        toast.error("Validation failed. Please check your data and try again.", {
          position: "top-right",
        })
      }
    } catch (error) {
      console.error("Error during moveToNext:", error)
      toast.error("An error occurred while processing your request.", {
        position: "top-right",
      })
    } finally {
      setIsValidating(false)
    }
  }

  // Rest of the component code remains the same...
  // (Including all the other functions and useEffects)

  const executeFun = async (exe) => {
    try {
      const res = await executeApi(exe)
      return res
    } catch (e) {
      throw e
    }
  }

  const updateQueryParams = (executor, source) => {
    if (!executor) return

    const updateOrAddParam = (key, value, description = "", req = false) => {
      const index = executor.queryParams.findIndex((param) => param.key === key)
      if (index > -1) {
        executor.queryParams[index].value = value
      } else {
        executor.queryParams.push({ key, value, description, req })
      }
    }

    if (Array.isArray(executor.headers)) {
      executor.headers.forEach(({ key, value, description, req }) => {
        updateOrAddParam(key, value, description, req)
      })
    }

    if (source.username && source.password) {
      updateOrAddParam("username", source.username)
      updateOrAddParam("password", source.password)
    }

    if (source.instance_url && source.apikey) {
      updateOrAddParam("instance_url", source.instance_url)
      updateOrAddParam("username", source.apikey)
      // updateOrAddParam("password", source.password)
    }

    addQueryParamsToSource(executor)
    syncHeadersWithQueryParams(executor)
  }

  const syncHeadersWithQueryParams = (executor) => {
    if (!executor || !Array.isArray(executor.headers) || !Array.isArray(executor.queryParams)) return

    executor.headers.forEach((header) => {
      const matchingParam = executor.queryParams.find((param) => param.key === header.key)
      if (matchingParam) {
        header.value = matchingParam.value
        header.description = matchingParam.description
        header.req = matchingParam.req
      }
    })
  }

  const updateTargetQueryParameter = (targetMappingExecutor, target) => {
    Object.entries(target.formData).forEach(([key, value]) => {
      if (key === "instance_url") {
        updateOrAddParam(
          targetMappingExecutor,
          "domainUrl",
          target.domainType === "sourceDomain" ? extractSourceDomain(value) : extractSubdomain(value),
        )
        updateOrAddParam(
          targetMappingExecutor,
          "domain",
          target.domainType === "sourceDomain" ? extractSourceDomain(value) : extractSubdomain(value),
        )
      } else {
        updateOrAddParam(targetMappingExecutor, key, value)
      }
    })
  }

  // const updateOrAddParam = (executor, key, value, description, req) => {
  //   executor.queryParams = executor.queryParams || []
  //   const param = executor.queryParams.find((p) => p.key === key)
  //   param ? (param.value = value) : executor.queryParams.push({ key, value, description, req })
  // }

  const updateOrAddParam = (executor, key, value, description, req) => {
    executor.queryParams = executor.queryParams || []

    // Special handling for apikey - update existing username parameter if available
    if (key === "apikey") {
      const usernameParam = executor.queryParams.find((p) => p.key === "username")
      if (usernameParam) {
        usernameParam.value = value
        return
      }
    }

    const param = executor.queryParams.find((p) => p.key === key)
    param ? (param.value = value) : executor.queryParams.push({ key, value, description, req })
  }

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedObjectData) return

      setIsLoading(true)

      const { sourceExecutor, targetMappingExecutor, sourceResponseAttributesExecutor, sourceMappingExecutor } =
        selectedObjectData

      const promises = []
      updateQueryParams(sourceExecutor, source?.connDetails)

      if (targetMappingExecutor) {
        updateTargetQueryParameter(targetMappingExecutor, target)
        const targetPromise = executeFun(targetMappingExecutor).then((result) => ({
          key: "targetExeRes",
          result,
        }))
        promises.push(targetPromise)
      }

      if (sourceResponseAttributesExecutor) {
        updateQueryParams(sourceResponseAttributesExecutor, source?.connDetails)
        const responsePromise = executeApi(sourceResponseAttributesExecutor).then((result) => ({
          key: "sourceResAtt",
          result: result.response,
        }))
        promises.push(responsePromise)
      }

      if (sourceMappingExecutor) {
        updateQueryParams(sourceMappingExecutor, source?.connDetails)
        const mappingPromise = executeApi(sourceMappingExecutor).then((result) => ({
          key: "sourceMapRes",
          result,
        }))
        promises.push(mappingPromise)
      }

      try {
        const results = await Promise.all(promises)

        results.forEach(({ key, result }) => {
          if (key === "targetExeRes" || key === "sourceResAtt" || key === "sourceMapRes") {
            setMigrationState((prev) => ({
              ...prev,
              [key]: result,
            }))
          }
        })
        setMigrationState((prev) => {
          if (prev.apiCallsPending && prev.apiCallsCompleteCallback) {
            prev.apiCallsCompleteCallback()
            return {
              ...prev,
              apiCallsPending: false,
              apiCallsCompleteCallback: null,
            }
          }
          return prev
        })
      } catch (error) {
        console.error("Error executing APIs:", error)
        setMigrationState((prev) => {
          if (prev.apiCallsPending && prev.apiCallsCompleteCallback) {
            prev.apiCallsCompleteCallback()
            return {
              ...prev,
              apiCallsPending: false,
              apiCallsCompleteCallback: null,
            }
          }
          return prev
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (selectedObjectData) {
      fetchData()
    }
  }, [selectedObjectData])

  useEffect(() => {
    const fetchUniqueValues = async () => {
      if (uploadedFiles && uploadedFiles.length > 0) {
        const mainEntityFile = uploadedFiles.find((file) => file.entity === selectedObjectName) || uploadedFiles[0]

        // Create files array with name, url, and columns for each uploaded file
        const files = uploadedFiles.map((file) => ({
          name: file.entity,
          url: file.azureBlobUrl,
          columns: file.headers || []
        }))

        // const payload = {
        //   files: files,
        // }

        try {
          const res = await getUniqueValuesFromCSV(files)
          setMigrationState((prev) => ({
            ...prev,
            sourceObjData: {
              ...prev.sourceObjData,
              uniqueSourceValues: res,
              lengthOfCSV: mainEntityFile.recordCount || mainEntityFile.length, // Use recordCount first, then fallback to length
            },
          }))
        } catch (error) {
          console.error("Error fetching unique values:", error)
        }
      }
    }

    fetchUniqueValues()
  }, [uploadedFiles, selectedObjectName])

  const saveMigration = async (obj) => {
    try {
      // Prepare flatFileMainTargetUuid data for CSV sources and store in selectedObjectData
      if (isCSVSource && uuidColumns.length > 0) {
        const flatFileMainTargetUuid = uuidColumns.map(col => ({
          name: col.name,
          uuidColumn: col.uuidColumn,
          isMainTarget: col.isMainTarget
        }))

        // Store directly in selectedObjectData
        if (migrationState.selectedObjectData) {
          migrationState.selectedObjectData.flatFileMainTargetUuid = flatFileMainTargetUuid
        }
      }

      const payload = {
        plan_name: templateName,
        migration_objects: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        email_id: email,
        additional_details: {
          sourceObjData: source,
          targetData: target,
          dataTypeData: obj,
          dataMappingData: migrationState.dataMappingData,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
        },
      }
      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
      }
      const res = await saveMigrationPlan(payload)

      if (res?.message === "plan created successfully" && res?.response?.id) {
        setPlanId(res.response.id)
      }
    } catch (error) {
      toast.error(error?.response.data.message || "Something went wrong!", {
        position: "top-right",
      })
    }
  }

  const parseJsonFields = (obj) => {
    if (!obj || typeof obj !== "object") return obj

    const parsed = { ...obj }

    Object.keys(parsed).forEach((key) => {
      const value = parsed[key]

      if (typeof value === "string" && value.trim()) {
        const trimmed = value.trim()
        if ((trimmed.startsWith("{") && trimmed.endsWith("}")) || (trimmed.startsWith("[") && trimmed.endsWith("]"))) {
          try {
            parsed[key] = JSON.parse(value)
          } catch (error) {
            console.warn(`Failed to parse JSON for key "${key}":`, error)
          }
        }
      }
    })

    return parsed
  }

  useEffect(() => {
    const fetchMigrationEntityData = async () => {
      try {
        setIsEntityDataLoading(true)
        let response = await getMigrationEntities(source.source.name, target.target.name)

        const allTargetIds = []
        const allSourceIds = []

        response.forEach((item) => {
          item.sourceField = item.sourceField ? JSON.parse(item.sourceField) : []
          item.targetField = item.targetField ? JSON.parse(item.targetField) : []
          item.sourceExtender = item.sourceExtender ? JSON.parse(item.sourceExtender) : []

          if (
            item.migrationObject &&
            item.migrationObject !== null &&
            item.migrationObject !== undefined &&
            item.migrationObject !== ""
          ) {
            allSourceIds.push(item.migrationObject)
          }

          if (item.mainTarget && item.mainTarget !== null && item.mainTarget !== undefined && item.mainTarget !== "") {
            allTargetIds.push(item.mainTarget)
          }

          if (item.dependentTargets && Array.isArray(item.dependentTargets)) {
            item.dependentTargets.forEach((targetId) => {
              if (targetId !== null && targetId !== undefined && targetId !== "") {
                allTargetIds.push(targetId)
              }
            })
          }

          if (item.mappingTargets && Array.isArray(item.mappingTargets)) {
            item.mappingTargets.forEach((targetId) => {
              if (targetId !== null && targetId !== undefined && targetId !== "") {
                allTargetIds.push(targetId)
              }
            })
          }
        })

        const uniqueSourceIds = [...new Set(allSourceIds)].filter((id) => id !== null && id !== undefined && id !== "")
        const uniqueTargetIds = [...new Set(allTargetIds)].filter((id) => id !== null && id !== undefined && id !== "")

        const sourceObjectsMap = new Map()
        const targetsMap = new Map()

        if (uniqueSourceIds.length > 0) {
          try {
            const bulkSourceResponse = await getMigrationObjectsBulkFetch({ ids: uniqueSourceIds })
            if (Array.isArray(bulkSourceResponse)) {
              bulkSourceResponse.forEach((sourceObj) => {
                if (sourceObj.id) {
                  const parsedSourceData = parseJsonFields(sourceObj)
                  sourceObjectsMap.set(sourceObj.id, parsedSourceData)
                }
              })
            }
          } catch (error) {
            console.warn("Bulk fetch of migration objects failed, falling back to individual calls:", error)
            for (const sourceId of uniqueSourceIds) {
              try {
                const sourceData = await getMigrationObjectsById(sourceId)
                const parsedSourceData = parseJsonFields(sourceData)
                sourceObjectsMap.set(sourceId, parsedSourceData)
              } catch (error) {
                console.error(`Error fetching migration object ${sourceId}:`, error)
              }
            }
          }
        }

        if (uniqueTargetIds.length > 0) {
          try {
            const bulkTargetResponse = await getMigrationTargetsBulkFetch({ ids: uniqueTargetIds })
            if (Array.isArray(bulkTargetResponse)) {
              bulkTargetResponse.forEach((targetObj) => {
                if (targetObj.id) {
                  const parsedTargetData = parseJsonFields(targetObj)
                  targetsMap.set(targetObj.id, parsedTargetData)
                }
              })
            }
          } catch (error) {
            console.warn("Bulk fetch of migration targets failed, falling back to individual calls:", error)
            for (const targetId of uniqueTargetIds) {
              try {
                const targetData = await getMigrationTargetById(targetId)
                const parsedTargetData = parseJsonFields(targetData)
                targetsMap.set(targetId, parsedTargetData)
              } catch (error) {
                console.error(`Error fetching migration target ${targetId}:`, error)
              }
            }
          }
        }

        response.forEach((item) => {
          if (item.migrationObject) {
            const sourceData = sourceObjectsMap.get(item.migrationObject)
            item.migrationObject = sourceData ? [sourceData] : []
          }

          if (item.mainTarget) {
            const mainTargetData = targetsMap.get(item.mainTarget)
            item.mainTarget = mainTargetData ? [mainTargetData] : []
          } else {
            item.mainTarget = []
          }

          if (item.dependentTargets && Array.isArray(item.dependentTargets)) {
            const dependentTargetsData = []
            item.dependentTargets.forEach((targetId) => {
              const targetData = targetsMap.get(targetId)
              if (targetData) {
                dependentTargetsData.push(targetData)
              }
            })
            item.dependentTargets = dependentTargetsData
          } else {
            item.dependentTargets = []
          }

          if (item.mappingTargets && Array.isArray(item.mappingTargets)) {
            const mappingTargetsData = []
            item.mappingTargets.forEach((targetId) => {
              const targetData = targetsMap.get(targetId)
              if (targetData) {
                mappingTargetsData.push(targetData)
              }
            })
            item.mappingTargets = mappingTargetsData
          } else {
            item.mappingTargets = []
          }
        })

        response = {
          migrationMapping: response,
          source: source.source.name,
          target: target.target.name,
        }
        setEntityData(response)
        const entities =
          response?.migrationMapping?.flatMap(
            (mapping) =>
              mapping.sourceField?.map((field) => ({
                name: field.entity,
                dependent: field.dependentEntities || [],
              })) || [],
          ) || []
        setSelectedData(entities)
      } catch (error) {
        console.error("Error fetching migration entities:", error)
        setSelectedData([])
      } finally {
        setIsEntityDataLoading(false)
      }
    }

    const fetchMigrationObjectsData = async () => {
      try {
        const response = await getMigrationObjects(source.source.name, target.target.name)
        response.forEach((item) => {
          item.sourceMappingExecutor = JSON.parse(item.sourceMappingExecutor)
          item.sourceExecutor = JSON.parse(item.sourceExecutor)
          item.sourceProvider = JSON.parse(item.sourceProvider)
          item.sourceQueryParamsOptions = JSON.parse(item.sourceQueryParamsOptions)
          item.sourceResponseAttributesExecutor = JSON.parse(item.sourceResponseAttributesExecutor)
          item.targetMappingExecutor = JSON.parse(item.targetMappingExecutor)
          item.paginationFields = JSON.parse(item.paginationFields)
        })
        const formattedResponse = {
          migration_objects: response,
          validation_rules: [],
        }
        setObjectData(formattedResponse)
      } catch (error) {
        console.error("Error fetching migration objects:", error)
      }
    }
    fetchMigrationEntityData()
    fetchMigrationObjectsData()
  }, [])

  const addQueryParamsToSource = (data) => {
    replaceByKey(data.queryParams, "authorization", {
      key: "username",
      value: source.connDetails.username ? source.connDetails.username : source.connDetails.apikey,
      description: "",
      req: false,
    })
    replaceByKey(data.queryParams, "apikey", {
      key: "username",
      value: source.connDetails.apikey,
      description: "",
      req: false,
    })
    replaceByKey(data.queryParams, "domainUrl", {
      key: "domainUrl",
      value: extractSubdomain(source.connDetails.instance_url),
      description: "",
      req: false,
    })
    replaceByKey(data.queryParams, "domain", {
      key: "domainUrl",
      value: extractSubdomain(source.connDetails.instance_url),
      description: "",
      req: false,
    })
    if (source.queryParam && Object.keys(source.queryParam).length > 0) {
      updateQueryFilterParams(data.queryParams, source.queryParam)
    }
  }

  const updateQueryFilterParams = (source, target) => {
    if (source && target) {
      Object.keys(target).forEach((key) => {
        const index = source.findIndex((item) => item.key === key)

        if (index !== -1) {
          const item = source[index]
          item.value = target[key]
          if (!item.hasOwnProperty("description")) {
            item.description = ""
          }
          if (!item.hasOwnProperty("req")) {
            item.req = false
          }
        } else {
          source.push({
            key,
            value: target[key],
            description: "",
            req: false,
          })
        }
      })
    }
  }

  const extractSubdomain = (url) => {
    if (!url || typeof url !== "string" || url.trim() === "") return null // null/empty check
    const subdomainMatch = url.match(/^(?:https?:\/\/)?([^./]+)\./)
    return subdomainMatch ? subdomainMatch[1] : null
  }

  const extractSourceDomain = (url) => {
    if (!url || typeof url !== "string" || url.trim() === "") return null // null/empty check
    try {
      const trimmedUrl = url.trim()
      const parsedUrl = new URL(trimmedUrl)
      return parsedUrl.hostname
    } catch (error) {
      console.error("Invalid URL:", error)
      return null
    }
  }

  const validateData = async () => {
    const data = objectData?.migration_objects?.find((obj) => obj.migrationEntity === selectedObjectName)
    updateQueryParams(data.sourceExecutor, source?.connDetails)
    const queryParams = { queryParams: data.sourceExecutor.queryParams }

    try {
      const res = await validateSourceData(source.source.name, target.target.name, selectedObjectName, queryParams)
      return res.hasData
    } catch (error) {
      return false
    }
  }

  // Function to check if all UUID columns are selected
  const areAllUuidFieldsSelected = () => {
    if (!isCSVSource || !showUuidSelection || uuidColumns.length === 0) {
      return true // No UUID selection required for non-CSV sources
    }
    return uuidColumns.every(col => col.uuidColumn && col.uuidColumn.trim() !== "")
  }

  const handleContinueClick = async () => {
    if (!isCSVSource && (!numberOfRecords || numberOfRecords.trim() === "")) {
      toast.error("Number of records is required!", {
        position: "top-right",
      })
      return
    }

    if (isCSVSource) {
      const required = [selectedObjectName, ...(dependentFields || [])]
      const uploadedEntityNames = [...new Set(uploadedFiles.map((file) => file.entity))]
      const allUploaded = required.every((entity) => uploadedEntityNames.includes(entity))

      if (!allUploaded) {
        toast.error("Please upload all required files before continuing.", {
          position: "top-right",
        })
        return
      }

      // Check if all entities have selected unique fields
      if (showUuidSelection && uuidColumns.length > 0) {
        const incompleteEntities = uuidColumns.filter(col => !col.uuidColumn || col.uuidColumn.trim() === "")

        if (incompleteEntities.length > 0) {
          const entityNames = incompleteEntities.map(col => col.name).join(", ")
          toast.error(`Please select unique fields for: ${entityNames}`, {
            position: "top-right",
            autoClose: 5000,
          })
          return
        }
      }
    }

    await moveToNext()
  }

  const replaceByKey = (data, key, newValue) => {
    if (data) {
      const index = data.findIndex((item) => item.key === key)
      if (index !== -1) {
        data[index] = newValue
      }
    }
  }

  const handleObject = (item, name, index) => {
    setSelectedObjectIndex(index)
    setDependentFields([])
    setSelectedObject(item)
    setSelectedObjectName(name)
  }

  const handleDependent = (dep) => {
    setDependentFields((prevState) => {
      if (prevState.includes(dep)) {
        return prevState.filter((field) => field !== dep)
      } else {
        return [...prevState, dep]
      }
    })
  }

  const [showBackConfirmation, setShowBackConfirmation] = useState(false)

  const closeSelectedObject = () => {
    if (uploadedFiles.length > 0) {
      setShowBackConfirmation(true)
    } else {
      setConfirm(false)
    }
  }

  const confirmGoBack = () => {
    setShowBackConfirmation(false)
    setConfirm(false)
  }

  const cancelGoBack = () => {
    setShowBackConfirmation(false)
  }

  const handleNumberOfRecordsChange = (event) => {
    const value = event.target.value

    const numValue = Number.parseInt(value)

    if (numValue === 0) {
      toast.error("Number of records cannot be zero", {
        position: "top-right",
        autoClose: 3000,
      })
      return
    }

    if (numValue > 10000000) {
      toast.error("Number of records cannot exceed 10000000", {
        position: "top-right",
        autoClose: 3000,
      })
      return
    }

    setNumberOfRecords(value)
  }

  return (
    <div>
      {showUploadCSV && (
        <UploadCSV
          onFileUploaded={handleFileUploaded}
          onClose={closeUploadDialog}
          entityName={currentUploadEntity}
          isLastEntity={isLastEntity}
          previouslyUploadedFiles={uploadedFiles.filter((file) => file.entity === currentUploadEntity)}
          onRecordCountUpdate={handleRecordCountUpdate}
        />
      )}

      {isLoading || isValidating ? (
        <div className={styles.loaderContainer}>
          <LoaderSpinner />
          {isValidating && <p className={styles.loaderText}>Validating data, please wait...</p>}
        </div>
      ) : (
        <div>
          <div className={styles.dFlex}>
            <div className={styles.section} style={{ width: "35%" }}>
              <div className={styles.sourceTargetGraphic}>
                <div className={styles.sourceTargetContainer}>
                  <img
                    src="/assets/Sourcetargetconnector.png"
                    alt="Source Target Connector"
                    className={styles.backgroundImage}
                  />

                  {source && target && (
                    <div className={styles.logoOverlay}>
                      <div className={styles.sourceLogoContainer}>
                        <img
                          src={source.source?.imgUrl || "/assets/default-source.png"}
                          alt={source.source?.name || "Source"}
                          className={styles.connectionLogo}
                        />
                      </div>

                      <div className={styles.connectionArrow}>
                        <img src="/assets/Arrow.png" alt="Connection Arrow" className={styles.arrowImage} />
                      </div>

                      <div className={styles.targetLogoContainer}>
                        <img
                          src={target.target?.imgUrl || "/assets/default-target.png"}
                          alt={target.target?.name || "Target"}
                          className={styles.connectionLogo}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className={styles.section} style={{ width: "65%" }}>
              {!confirm ? (
                <div>
                  {isEntityDataLoading ? (
                    <LoaderSpinner />
                  ) : (
                    <>
                      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
                        <div className={globalStyles.selectionName}>SELECT THE OBJECTS YOU WANT TO MIGRATE</div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            cursor: "pointer",
                          }}
                          onClick={() => {
                            displayArticle("What are objects?")
                          }}
                        >
                          <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                          <span className={globalStyles.guideName}>What are objects?</span>
                        </div>
                      </div>
                      {selectedData.map((item, index) => (
                        <div className={styles.entityRow} key={`entity-row-${index}`}>
                          <div
                            key={index}
                            className={`${styles.entityBox} ${index === selectedObjectIndex ? styles.selectedEntityBox : ""}`}
                            onClick={() => handleObject(item, item.name, index)}
                          >
                            <div className={styles.entityContent}>
                              <img src="/assets/Tickets.jpg" alt="Ticket" className={styles.entityImage} />
                            </div>
                            <div className={styles.entityName}>{item.name}</div>
                          </div>
                          {item.dependent.length > 0 && index === selectedObjectIndex && (
                            <div className={styles.dependentContainer} key={`dep-container-${index}`}>
                              {item.dependent.map((dep, i) => (
                                <div
                                  key={`dep-${i}`}
                                  className={`${styles.entityBox} ${(dependentFields || []).includes(dep) ? styles.selectedEntityBox : ""}`}
                                  onClick={() => handleDependent(dep)}
                                >
                                  <div className={styles.entityContent}>
                                    <img
                                      src="/assets/Conversations.png"
                                      alt="Conversation"
                                      className={styles.entityImage}
                                    />
                                  </div>
                                  <div className={styles.entityName}>{dep}</div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}

                      <button className={`${globalStyles.mainButton} ${styles.floatingButton}`} onClick={handleConfirm}>
                        {isConfirmLoading ? (
                          <div className={globalStyles.loaderContainer}>
                            <div className={globalStyles.loader}></div>
                          </div>
                        ) : (
                          "Confirm"
                        )}
                      </button>
                    </>
                  )}
                </div>
              ) : (
                <div>
                  <div className={styles.dFlex} style={{ justifyContent: "space-between", alignItems: "center" }}>
                    <span className={globalStyles.selectionName}>OBJECTS SELECTED FOR MIGRATION</span>
                    <div style={{ display: "flex", alignItems: "center", gap: "15px" }}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={() => {
                          displayArticle("What are objects?")
                        }}
                      >
                        <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                        <span className={globalStyles.guideName}>What are objects?</span>
                      </div>
                      <HiXMark className={styles.closeIcon} onClick={closeSelectedObject} />
                    </div>
                  </div>
                  <div style={{ display: "flex", gap: "10px" }}>
                    <div className={styles.selectedEntityCard}>
                      <img src="/assets/Tickets.jpg" alt="Ticket" className={styles.selectedEntityImage} />
                      <div className={styles.selectedEntityName}>{selectedObjectName}</div>
                    </div>

                    {(dependentFields || []).length > 0 &&
                      (dependentFields || []).map((dep, index) => (
                        <div className={styles.selectedEntityCard} key={index}>
                          <img
                            src="/assets/Conversations.png"
                            alt="Conversation"
                            className={styles.selectedEntityImage}
                          />
                          <div className={styles.selectedEntityName}>{dep}</div>
                        </div>
                      ))}
                  </div>

                  {/* Remove input field for CSV sources - only show for non-CSV sources */}
                  {!isCSVSource ? (
                    <>
                      <div className={styles.dFlex} style={{ marginTop: "30px" }}>
                        <div className={globalStyles.poppinsHeaderStyle}>Enter total number of records</div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginLeft: "auto",
                          }}
                          onClick={() => {
                            displayArticle("What are records?")
                          }}
                        >
                          <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                          <span className={globalStyles.guideName}>What are records?</span>
                        </div>
                      </div>

                      <div className="form-group">
                        <input
                          className="form-control"
                          placeholder="Number of records*"
                          value={numberOfRecords}
                          onChange={handleNumberOfRecordsChange}
                        />
                      </div>
                      <div className={globalStyles.guideName}>Enter your live migration count</div>
                    </>
                  ) : (
                    <>
                      <div className={styles.dFlex} style={{ marginTop: "30px" }}>
                        <div className={globalStyles.poppinsHeaderStyle}>Total number of records (from CSV)</div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginLeft: "auto",
                          }}
                          onClick={() => {
                            displayArticle("What are records?")
                          }}
                        >
                          <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                          <span className={globalStyles.guideName}>What are records?</span>
                        </div>
                      </div>
                      <div className="form-group">
                        <input
                          className="form-control"
                          placeholder="Number of records*"
                          value={numberOfRecords}
                          readOnly
                          style={{ backgroundColor: "#f5f5f5", color: "#888" }}
                        />
                      </div>
                      <div className={globalStyles.guideName}>This is auto-filled from your main entity CSV file</div>
                    </>
                  )}

                  {uploadedFiles.length > 0 && (
                    <div style={{ marginTop: "30px" }}>
                      <div className={globalStyles.poppinsHeaderStyle} style={{ marginBottom: "10px" }}>
                        Uploaded Files:
                      </div>

                      {/* Group files by entity */}
                      {(() => {
                        const entities = [...new Set(uploadedFiles.map((file) => file.entity))]

                        return entities.map((entity) => (
                          <div key={entity} style={{ marginBottom: "15px" }}>
                            <div
                              style={{
                                fontWeight: "500",
                                marginBottom: "5px",
                                color: "#333",
                                display: "flex",
                                alignItems: "center",
                                fontFamily: "Inter",
                                fontSize: "14px",
                              }}
                            >
                              <span
                                style={{
                                  display: "inline-block",
                                  width: "8px",
                                  height: "8px",
                                  backgroundColor: "#EA5822",
                                  borderRadius: "50%",
                                  marginRight: "8px",
                                }}
                              ></span>
                              <span style={{ flex: 1 }}>{entity}</span>
                              <button
                                onClick={() => handleUploadForEntity(entity)}
                                style={{
                                  backgroundColor: "#EA5822",
                                  color: "white",
                                  border: "none",
                                  borderRadius: "4px",
                                  padding: "2px 8px",
                                  fontSize: "12px",
                                  cursor: "pointer",
                                  marginLeft: "10px",
                                  fontFamily: "Inter, sans-serif",
                                }}
                              >
                                Replace
                              </button>
                            </div>
                            <ul style={{ listStyleType: "none", paddingLeft: "16px", margin: "0" }}>
                              {uploadedFiles
                                .filter((file) => file.entity === entity)
                                .map((file, index) => {
                                  const actualIndex = uploadedFiles.findIndex(
                                    (f) => f.name === file.name && f.entity === entity,
                                  )

                                  return (
                                    <li
                                      key={index}
                                      style={{
                                        marginBottom: "8px",
                                        color: "#514742",
                                        fontSize: "15px",
                                        fontFamily: "Inter, sans-serif",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "space-between",
                                        padding: "10px",
                                        backgroundColor: "#f9f9f9",
                                        borderRadius: "4px",
                                      }}
                                    >
                                      <div style={{ display: "flex", alignItems: "center" }}>
                                        <HiDocument style={{ width: "20px", height: "20px", marginRight: "10px" }} />
                                        {file.name || "File uploaded"}
                                        <span
                                          style={{
                                            fontSize: "12px",
                                            color: "#777",
                                            marginLeft: "10px",
                                          }}
                                        >
                                          {file.size}
                                        </span>
                                        {file.recordCount && (
                                          <span
                                            style={{
                                              fontSize: "12px",
                                              color: "#1976D2",
                                              marginLeft: "10px",
                                              fontWeight: "500",
                                            }}
                                          >
                                            ({file.recordCount.toLocaleString()} records)
                                          </span>
                                        )}
                                      </div>
                                      <HiTrash
                                        className={styles.deleteIcon}
                                        onClick={() => handleDeleteConfirmation(actualIndex, file.name)}
                                      />
                                    </li>
                                  )
                                })}
                            </ul>
                          </div>
                        ))
                      })()}

                      {/* Show which entities still need files */}
                      {(() => {
                        const required = [selectedObjectName, ...(dependentFields || [])]
                        const uploadedEntityNames = [...new Set(uploadedFiles.map((file) => file.entity))]
                        const missingEntities = required.filter((entity) => !uploadedEntityNames.includes(entity))

                        if (missingEntities.length > 0) {
                          return (
                            <div style={{ marginTop: "15px" }}>
                              <div
                                style={{
                                  fontWeight: "500",
                                  color: "#f44336",
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  fontFamily: "Inter",
                                  color: "#EA5822",
                                }}
                              >
                                <span style={{ fontFamily: "Inter", fontSize: "14px", fontFamily: "Inter" }}>
                                  Files still needed for:
                                </span>
                              </div>
                              <ul
                                style={{
                                  listStyleType: "none",
                                  paddingLeft: "16px",
                                  margin: "10px 0",
                                  fontFamily: "Inter",
                                }}
                              >
                                {missingEntities.map((entity, index) => (
                                  <li
                                    key={index}
                                    style={{
                                      marginBottom: "5px",
                                      display: "flex",
                                      alignItems: "center",
                                      fontFamily: "Inter",
                                    }}
                                  >
                                    <span
                                      style={{
                                        display: "inline-block",
                                        width: "8px",
                                        height: "8px",
                                        backgroundColor: "#f44336",
                                        borderRadius: "50%",
                                        marginRight: "8px",
                                        fontFamily: "Inter",
                                      }}
                                    ></span>
                                    <span style={{ flex: 1, fontFamily: "Inter" }}>{entity}</span>
                                    <button
                                      onClick={() => handleUploadForEntity(entity)}
                                      style={{
                                        backgroundColor: "#EA5822",
                                        color: "white",
                                        border: "none",
                                        borderRadius: "4px",
                                        padding: "2px 8px",
                                        fontSize: "12px",
                                        cursor: "pointer",
                                        marginLeft: "10px",
                                        fontFamily: "Inter, sans-serif",
                                      }}
                                    >
                                      Upload
                                    </button>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )
                        }
                        return null
                      })()}
                    </div>
                  )}

                  {/* UUID Column Selection for CSV Sources */}
                  {showUuidSelection && (
                    <div style={{ marginTop: "20px", marginBottom: "20px" }}>
                      <div style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginBottom: "5px"
                      }}>
                        <div style={{
                          color: "#EA5822",
                          fontSize: "14px",
                          fontWeight: "700",
                          fontFamily: "Poppins, sans-serif",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px"
                        }}>
                          Select Unique Field
                        </div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            cursor: "pointer",
                          }}
                          onClick={() => {
                            displayArticle("What is a Unique Field?")
                          }}
                        >
                          <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                          <span className={globalStyles.guideName}>What is a Unique Field?</span>
                        </div>
                      </div>
                      <div style={{
                        color: "#666",
                        fontSize: "12px",
                        fontWeight: "400",
                        marginBottom: "15px",
                        fontFamily: "Inter, sans-serif",
                        fontStyle: "italic"
                      }}>

                      </div>
                      {uuidColumns.map((column, index) => (
                        <div key={column.name} style={{
                          display: "flex",
                          alignItems: "center",
                          marginBottom: index < uuidColumns.length - 1 ? "15px" : "0",
                          justifyContent: "space-between"
                        }}>
                          <div style={{
                            color: "#170903",
                            fontSize: "14px",
                            fontWeight: "500",
                            fontFamily: "Inter, sans-serif",
                            minWidth: "150px",
                            textTransform: "capitalize"
                          }}>
                            {column.name}
                          </div>
                          <div style={{ flex: 1, maxWidth: "300px" }}>
                            <select
                              value={column.uuidColumn}
                              onChange={(e) => handleUuidColumnChange(column.name, e.target.value)}
                              style={{
                                height: "45px",
                                padding: "10px 15px",
                                borderRadius: "3px",
                                border: "1px solid #DCDAD9",
                                background: "#FFF",
                                width: "100%",
                                color: "#170903",
                                fontSize: "14px",
                                fontFamily: "Inter, sans-serif",
                                cursor: "pointer",
                                outline: "none",
                                appearance: "none",
                                backgroundImage: "url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 12 8\"><path fill=\"%23666\" d=\"M6 8L0 2h12z\"/></svg>')",
                                backgroundRepeat: "no-repeat",
                                backgroundPosition: "right 15px center",
                                backgroundSize: "12px",
                                paddingRight: "40px"
                              }}
                            >
                              <option value="">Select Unique Field</option>
                              {column.availableColumns.map((col) => (
                                <option key={col} value={col}>
                                  {col}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  <button
                    className={globalStyles.mainButton}
                    style={{
                      width: "100%",
                      marginTop: "20px",
                      marginBottom: "50px",
                      opacity: (source?.source?.name?.toLowerCase() === "csv" && (!allFilesUploaded || !areAllUuidFieldsSelected())) ? "0.7" : "1",
                    }}
                    disabled={source?.source?.name?.toLowerCase() === "csv" && (!allFilesUploaded || !areAllUuidFieldsSelected())}
                    onClick={handleContinueClick}
                  >
                    {source?.source?.name?.toLowerCase() === "csv" && !allFilesUploaded
                      ? "Upload All Required Files First"
                      : source?.source?.name?.toLowerCase() === "csv" && !areAllUuidFieldsSelected()
                        ? "Select All Unique Fields First"
                        : "Confirm & Continue"}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {showDeleteConfirmation && (
        <Dialog open={showDeleteConfirmation} maxWidth="md" onClose={() => setShowDeleteConfirmation(false)}>
          <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>
            <div
              style={{
                display: "flex",
                width: "450px",
                padding: "20px 30px 30px 30px",
                flexDirection: "column",
                alignItems: "flex-start",
                gap: "15px",
                borderRadius: "10px",
                background: "#170903",
                position: "relative",
              }}
            >
              {/* Close button */}
              <button
                onClick={() => setShowDeleteConfirmation(false)}
                style={{
                  position: "absolute",
                  top: "20px",
                  right: "20px",
                  background: "none",
                  border: "none",
                  color: "#fff",
                  fontSize: "24px",
                  cursor: "pointer",
                  padding: "0",
                  width: "30px",
                  height: "30px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                ×
              </button>

              {/* Header */}
              <h2
                style={{
                  color: "#EA5822",
                  fontFamily: "Poppins",
                  fontSize: "14px",
                  fontStyle: "normal",
                  fontWeight: "700",
                  lineHeight: "24px",
                  margin: "0",
                  textTransform: "uppercase",
                  letterSpacing: "0.5px",
                }}
              >
                DO YOU WANT TO DELETE THIS FILE?
              </h2>

              {/* Warning message */}
              <p
                style={{
                  color: "#F8F8F7",
                  fontFamily: "Inter",
                  fontSize: "14px",
                  fontStyle: "normal",
                  fontWeight: "400",
                  lineHeight: "24px",
                  margin: "0",
                }}
              >
                This will permanently remove the file "{fileToDelete.name}" from your uploads. This action cannot be undone.
              </p>

              {/* Buttons */}
              <div
                style={{
                  display: "flex",
                  gap: "10px",
                  alignSelf: "flex-end",
                  marginTop: "5px",
                }}
              >
                <button
                  onClick={() => setShowDeleteConfirmation(false)}
                  style={{
                    display: "flex",
                    height: "40px",
                    padding: "8px 20px",
                    justifyContent: "center",
                    alignItems: "center",
                    gap: "10px",
                    minWidth: "100px",
                    borderRadius: "3px",
                    background: "transparent",
                    color: "#F8F8F7",
                    textAlign: "center",
                    fontFamily: "Inter",
                    fontSize: "16px",
                    fontStyle: "normal",
                    fontWeight: "500",
                    lineHeight: "24px",
                    border: "1px solid #F8F8F7",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = "#F8F8F7"
                    e.target.style.color = "#170903"
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = "transparent"
                    e.target.style.color = "#F8F8F7"
                  }}
                >
                  Cancel
                </button>

                <button
                  onClick={handleDeleteFile}
                  style={{
                    display: "flex",
                    height: "40px",
                    padding: "8px 20px",
                    justifyContent: "center",
                    alignItems: "center",
                    gap: "10px",
                    minWidth: "100px",
                    borderRadius: "3px",
                    background: "#EF8963",
                    color: "#170903",
                    textAlign: "center",
                    fontFamily: "Inter",
                    fontSize: "16px",
                    fontStyle: "normal",
                    fontWeight: "500",
                    lineHeight: "24px",
                    border: "none",
                    cursor: "pointer",
                    transition: "background-color 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = "#EFEEED"
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = "#EF8963"
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {showBackConfirmation && (
        <div className={styles.modalOverlay}>
          <div style={{
            display: 'flex',
            width: '450px',
            padding: '20px 30px 30px 30px',
            flexDirection: 'column',
            alignItems: 'flex-start',
            gap: '15px',
            borderRadius: '10px',
            background: '#170903',
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.3)',
            position: 'relative',
          }}>
            {/* Close button */}
            <button
              onClick={cancelGoBack}
              style={{
                position: 'absolute',
                top: '20px',
                right: '20px',
                background: 'none',
                border: 'none',
                color: '#fff',
                fontSize: '24px',
                cursor: 'pointer',
                padding: '0',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              ×
            </button>

            {/* Header */}
            <h2 style={{
              color: '#EA5822',
              fontFamily: 'Poppins',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: '700',
              lineHeight: '24px',
              margin: '0',
              textTransform: 'uppercase',
              letterSpacing: '0.5px',
            }}>
              CONFIRM GO BACK
            </h2>

            {/* Message */}
            <p style={{
              color: '#F8F8F7',
              fontFamily: 'Inter',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: '400',
              lineHeight: '24px',
              margin: '0',
            }}>
              Are you sure you want to go back to object selection?
            </p>

            <p style={{
              color: '#F8F8F7',
              fontFamily: 'Inter',
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: '400',
              lineHeight: '24px',
              margin: '0',
            }}>
              Your uploaded files will be preserved, but you'll need to reselect objects.
            </p>

            {/* Buttons */}
            <div style={{
              display: 'flex',
              gap: '10px',
              alignSelf: 'flex-end',
              marginTop: '5px',
            }}>
              <button
                onClick={cancelGoBack}
                style={{
                  display: 'flex',
                  height: '40px',
                  padding: '8px 20px',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '10px',
                  minWidth: '100px',
                  borderRadius: '3px',
                  background: 'transparent',
                  color: '#F8F8F7',
                  textAlign: 'center',
                  fontFamily: 'Inter',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: '500',
                  lineHeight: '24px',
                  border: '1px solid #F8F8F7',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#F8F8F7';
                  e.target.style.color = '#170903';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = 'transparent';
                  e.target.style.color = '#F8F8F7';
                }}
              >
                Cancel
              </button>

              <button
                onClick={confirmGoBack}
                style={{
                  display: 'flex',
                  height: '40px',
                  padding: '8px 20px',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '10px',
                  minWidth: '100px',
                  borderRadius: '3px',
                  background: '#EF8963',
                  color: '#170903',
                  textAlign: 'center',
                  fontFamily: 'Inter',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: '500',
                  lineHeight: '24px',
                  border: 'none',
                  cursor: 'pointer',
                  transition: 'background-color 0.3s ease',
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#EFEEED';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#EF8963';
                }}
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Replace File Confirmation Dialog */}
      <Dialog open={showReplaceConfirmation} maxWidth="md" onClose={handleCancelReplace}>
        <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>
          <div
            style={{
              display: "flex",
              width: "450px",
              padding: "20px 30px 30px 30px",
              flexDirection: "column",
              alignItems: "flex-start",
              gap: "15px",
              borderRadius: "10px",
              background: "#170903",
              position: "relative",
            }}
          >
            {/* Close button */}
            <button
              onClick={handleCancelReplace}
              style={{
                position: "absolute",
                top: "20px",
                right: "20px",
                background: "none",
                border: "none",
                color: "#fff",
                fontSize: "24px",
                cursor: "pointer",
                padding: "0",
                width: "30px",
                height: "30px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              ×
            </button>

            {/* Header */}
            <h2
              style={{
                color: "#EA5822",
                fontFamily: "Poppins",
                fontSize: "14px",
                fontStyle: "normal",
                fontWeight: "700",
                lineHeight: "24px",
                margin: "0",
                textTransform: "uppercase",
                letterSpacing: "0.5px",
              }}
            >
              ARE YOU SURE YOU WANT TO REPLACE THE FILE?
            </h2>

            {/* Warning message */}
            <p
              style={{
                color: "#F8F8F7",
                fontFamily: "Inter",
                fontSize: "14px",
                fontStyle: "normal",
                fontWeight: "400",
                lineHeight: "24px",
                margin: "0",
              }}
            >
              This will replace the existing file with a new one. The current file and its data will be permanently removed.
            </p>

            {/* Buttons */}
            <div
              style={{
                display: "flex",
                gap: "10px",
                alignSelf: "flex-end",
                marginTop: "5px",
              }}
            >
              <button
                onClick={handleCancelReplace}
                style={{
                  display: "flex",
                  height: "40px",
                  padding: "8px 20px",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: "10px",
                  minWidth: "100px",
                  borderRadius: "3px",
                  background: "transparent",
                  color: "#F8F8F7",
                  textAlign: "center",
                  fontFamily: "Inter",
                  fontSize: "16px",
                  fontStyle: "normal",
                  fontWeight: "500",
                  lineHeight: "24px",
                  border: "1px solid #F8F8F7",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = "#F8F8F7"
                  e.target.style.color = "#170903"
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = "transparent"
                  e.target.style.color = "#F8F8F7"
                }}
              >
                Cancel
              </button>

              <button
                onClick={handleConfirmReplace}
                style={{
                  display: "flex",
                  height: "40px",
                  padding: "8px 20px",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: "10px",
                  minWidth: "100px",
                  borderRadius: "3px",
                  background: "#EF8963",
                  color: "#170903",
                  textAlign: "center",
                  fontFamily: "Inter",
                  fontSize: "16px",
                  fontStyle: "normal",
                  fontWeight: "500",
                  lineHeight: "24px",
                  border: "none",
                  cursor: "pointer",
                  transition: "background-color 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = "#EFEEED"
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = "#EF8963"
                }}
              >
                Replace
              </button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
        toastStyle={{ backgroundColor: "#000", color: "#fff" }}
      />
    </div>
  )
}
