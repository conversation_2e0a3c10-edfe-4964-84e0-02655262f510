import { useState, useEffect } from "react"
import styles from "./MigrationHistory.module.css"
import Sidebar from "../Sidebar/Sidebar"
import { useNavigate, useLocation } from "react-router-dom"
import { HiOutlineFunnel } from "react-icons/hi2"
import globalStyles from "../globalStyles.module.css"
import { GlobeIcon, SearchIcon, RefreshIcon } from "@heroicons/react/solid"
import { getMigrationById, getMigrationProviders, getMigrationsByUser, getTransformationsWithEmail, migrationTemplate } from "../apiService"
import MigrationDetails from "../MigrationDetails/Migrationdetails"
import { FaChevronLeft, FaChevronRight, FaAngleDoubleLeft, FaAngleDoubleRight } from "react-icons/fa"
import LoaderSpinner from "../loaderspinner";
import { useMongodb } from "../mongodbService";

// const formatTimestamp = (timestamp) => {
//   if (!timestamp) return "N/A";
//
//   const date = new Date(timestamp);
//
//   if (isNaN(date.getTime())) return "Invalid Date";
//
//   return date.toLocaleString('en-US', {
//     month: 'short',
//     day: 'numeric',
//     year: 'numeric',
//     hour: '2-digit',
//     minute: '2-digit',
//     hour12: true
//   });
// };
const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toLocaleString();
};


const MigrationHistory = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    return localStorage.getItem("isSidebarCollapsed") === "true"
  })
  const { watchMigrations, migrationDataMongo, batchDataMongo, closeConnection } = useMongodb();
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [migrations, setMigrations] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredMigrations, setFilteredMigrations] = useState([])
  const [filterType, setFilterType] = useState("all")
  const [currentPage, setCurrentPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(25)
  const [paginatedData, setPaginatedData] = useState([])
  const [selectedMigration, setSelectedMigration] = useState(null)
  const [showMigrationDetails, setShowMigrationDetails] = useState(false)
  const [email, setEmail] = useState(localStorage.getItem("email"));

  const navigate = useNavigate()
  const location = useLocation()

  // Function to fetch migrations data
  const fetchMigrations = async () => {
    try {
      setLoading(true)
      // const response = await getMigrationsByUser(email, true)

      const newResponse = await getTransformationsWithEmail(email)

      const response = await Promise.all(newResponse.map(async (migration) => {
        // const template_id = migration.template_id
        // const planData = await migrationTemplate.get({ id: template_id })
        // let source = JSON.parse(planData?.migration_source)[0]
        // let target = JSON.parse(planData?.migration_target)[0]

        // const providers = await getMigrationProviders()
        // // Find source and target names from providers
        // const sourceProvider = providers.find(provider => provider.id === source);
        // const targetProvider = providers.find(provider => provider.id === target);

        // source = sourceProvider ? sourceProvider.displayName : source;
        // target = targetProvider ? targetProvider.displayName : target;

        // console.log("M.json", migration?.transformation_json.name)

        // console.log("Migration providers:", providers)

        // console.log("Plan Data:", planData)


        return {
          "migrationId": migration.id,
          sourcePlatform: migration?.transformation_json?.sourceModuleName || "Unknown",
          targetPlatform: migration?.transformation_json?.targetModuleName || "Unknown",
          "name": migration?.transformation_json?.name,
          "executedBy": migration?.transformation_json?.executedBy,
          "startTime": new Date(migration?.created_at).getTime(),
          // "endTime": 1749221068199,
          "endTime": "N/A",
          "status": migration?.status,
          "totalRecords": migration?.transformation_json?.totalRecords,
          // "scheduled": 3,
          // "passed": 3,
          // "failed": 0,
          // "skipped": 0,
          // "batches": null,
        }
      }))

      // console.log("New Response:", newResponse)

      // console.log("Response:", response)


      const sortedMigrations = [...response].sort((a, b) => {
        const dateA = a.startTime ? new Date(a.startTime).getTime() : 0;
        const dateB = b.startTime ? new Date(b.startTime).getTime() : 0;
        return dateB - dateA;
      });
      // console.log("Sorted Migrations:................", sortedMigrations)

      setMigrations(sortedMigrations);
      setFilteredMigrations(sortedMigrations);
    } catch (err) {
      setMigrations([])
      setFilteredMigrations([])
    } finally {
      setLoading(false)
    }
  }

  // Initial load and when email changes
  useEffect(() => {
    fetchMigrations()
  }, [email])

  // Refresh data when navigating from other pages via sidebar
  useEffect(() => {
    // Check if we have a refresh state parameter
    if (location.state && location.state.refresh) {
      // Reset any selected migration or details view
      setShowMigrationDetails(false)
      setSelectedMigration(null)

      // Fetch fresh data
      fetchMigrations()

      // Clear the state to prevent unnecessary refreshes
      navigate(location.pathname, { replace: true, state: {} })
    }
  }, [location.state])

  useEffect(() => {
    let statusFiltered = migrations;
    if (filterType !== "all") {
      statusFiltered = migrations.filter((migration) => {
        const status = getStatusDetails(migration).text.toLowerCase();
        return status === filterType.toLowerCase();
      });
    }

    if (searchTerm) {
      const searchFiltered = statusFiltered.filter(
        (migration) =>
          (migration.name && migration.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (migration.migrationId && migration.migrationId.toLowerCase().includes(searchTerm.toLowerCase())),
      )
      setFilteredMigrations(searchFiltered)
    } else {
      setFilteredMigrations(statusFiltered)
    }

    setCurrentPage(0)
  }, [searchTerm, migrations, filterType])

  useEffect(() => {
    const startIndex = currentPage * rowsPerPage
    const endIndex = startIndex + rowsPerPage
    setPaginatedData(filteredMigrations.slice(startIndex, endIndex))
  }, [filteredMigrations, currentPage, rowsPerPage])

  const getStatusDetails = (migration) => {
    if (!migration) {
      return { text: 'Unknown', className: styles.unknown };
    }

    // if (migration.passed === migration.totalRecords) {
    //   return { text: 'Completed', className: styles.completed };
    // } else if (migration.passed !== migration.totalRecords && migration.passed > 0) {
    //   return { text: 'Partially Completed', className: styles.partiallycompleted };
    // } else if (migration.passed === 0 && migration.status === 'scheduled') {
    //   return { text: 'Scheduled', className: styles.scheduled };
    // } else if (migration.passed === 0 && migration.failed > 0) {
    //   return { text: 'Failed', className: styles.failed };
    // } else {
    //   return { text: 'Unknown', className: styles.unknown };
    // }
    if (migration.status && migration.status.toLowerCase() === "completed") {
      return { text: 'Completed', className: styles.completed };
    } else if (migration.status && migration.status.toLowerCase() === "in_progress") {
      return { text: 'In Progress', className: styles.inProgress };
    } else if (migration.status && migration.status.toLowerCase() === "queued") {
      return { text: 'Queued', className: styles.scheduled };
    } else if (migration.status && migration.status.toLowerCase() === "terminated") {
      return { text: 'Terminated', className: styles.failed };
    } else {
      return { text: 'Unknown', className: styles.unknown };
    }
  };


  const toggleFilterDropdown = () => {
    setShowFilterDropdown(!showFilterDropdown)
  }

  const handleFilterSelect = (filterValue) => {
    setFilterType(filterValue)
    setShowFilterDropdown(false)
  }
  const getFilterDisplayText = () => {
    switch (filterType) {
      case "completed":
        return "Completed migrations"
      case "terminated":
        return "Terminated migrations"
      case "in progress":
        return "In Progress migrations"
      default:
        return "See all"
    }
  }

  useEffect(() => {
    if (migrationDataMongo) {
      updateMigrationData(migrationDataMongo);
    }
  }, [migrationDataMongo]);

  const updateMigrationData = (data) => {
    if (migrations && data.fullDocument && data.fullDocument.migrationId) {
      const index = migrations.findIndex(
        (obj) => obj.migrationId === data.fullDocument.migrationId
      );

      if (index !== -1) {
        const updatedHistory = [...migrations];
        updatedHistory[index] = {
          ...updatedHistory[index],
          scheduled: data.fullDocument.scheduled,
          passed: data.fullDocument.passed,
          skipped: data.fullDocument.skipped,
          failed: data.fullDocument.failed,
          status: data.fullDocument.status,
        };

        setMigrations(updatedHistory);

      }
    }
  };



  const totalPages = Math.ceil(filteredMigrations.length / rowsPerPage)

  const handlePageChange = (pageNumber) => {
    if (pageNumber >= 0 && pageNumber < totalPages) {
      setCurrentPage(pageNumber)
    }
  }

  const handleRowsPerPageChange = (rows) => {
    setRowsPerPage(rows)
    setCurrentPage(0)
  }

  const getVisiblePages = () => {
    const visiblePages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      for (let i = 0; i < totalPages; i++) {
        visiblePages.push(i)
      }
    } else {
      let startPage = Math.max(0, currentPage - Math.floor(maxVisiblePages / 2))
      let endPage = startPage + maxVisiblePages - 1

      if (endPage >= totalPages) {
        endPage = totalPages - 1
        startPage = Math.max(0, endPage - maxVisiblePages + 1)
      }

      if (startPage > 0) {
        visiblePages.push(0)
        if (startPage > 1) {
          visiblePages.push("ellipsis-start")
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        visiblePages.push(i)
      }

      if (endPage < totalPages - 1) {
        if (endPage < totalPages - 2) {
          visiblePages.push("ellipsis-end")
        }
        visiblePages.push(totalPages - 1)
      }
    }

    return visiblePages
  }

  const stats = {
    total: migrations.filter((m) => m.status && m.status.toLowerCase() === "completed").length,
    terminated: migrations.filter((m) => m.status && m.status.toLowerCase() === "terminated").length,
    inProgress: migrations.filter((m) => m.status && m.status.toLowerCase() === "in_progress").length,
  }

  const handleDetailsClick = (migration) => {
    setSelectedMigration(migration)
    setShowMigrationDetails(true)
  }

  const handleBackToList = () => {
    setShowMigrationDetails(false)
    setSelectedMigration(null)

  }

  // Auto-refresh migrations every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchMigrations();
    }, 10000); // 10 seconds
    return () => clearInterval(interval);
  }, [email]);

  if (showMigrationDetails && selectedMigration) {
    return <MigrationDetails migration={selectedMigration} onBack={handleBackToList} />
  }
  return (loading ? <LoaderSpinner /> : (
    <div className={styles.container} style={{ overflow: 'hidden' }}>
      <Sidebar
        key={`sidebar-history-${Date.now()}`}
        isCollapsed={isSidebarCollapsed}
        setIsCollapsed={setIsSidebarCollapsed}
      />

      <div className={`${styles.mainSection} ${isSidebarCollapsed ? styles.expanded : ""}`} style={{ overflowY: 'auto', overflowX: 'hidden' }}>
        <div className={styles.headerContainer}>
          <h1 className={globalStyles.headerStyle} style={{ paddingLeft: "0" }}>
            {filterType === "all" ? "Completed Migrations" : getFilterDisplayText()}
          </h1>
          <div className={styles.headerControls}>
            {/* <div className={globalStyles.searchWrapper}>
              <SearchIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Search..."
                className={globalStyles.searchInput}
                onFocus={(e) => {
                  e.target.style.width = "200px"
                  e.target.placeholder = "Typing..."
                }}
                onBlur={(e) => {
                  e.target.style.width = "80px"
                  e.target.placeholder = "Search..."
                }}
              />
            </div> */}

            <div className={globalStyles.searchWrapper} style={{ marginRight: "0px" }}>
              <GlobeIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Eng"
                className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                readOnly
              />
            </div>
          </div>
        </div>

        <div className={styles.statsContainer}>
          <div className={styles.statItem}>
            <div className={styles.statContent}>
              <span className={styles.statNumber}>{stats.total}</span>
              <span className={styles.statLabel}>Total migrations completed</span>
            </div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statContent}>
              <span className={styles.statNumber}>{stats.terminated}</span>
              <span className={styles.statLabel}>Migration terminated</span>
            </div>
          </div>
          <div className={styles.statItem}>
            <div className={styles.statContent}>
              <span className={styles.statNumber}>{stats.inProgress}</span>
              <span className={styles.statLabel}>Migration in process</span>
            </div>
          </div>
        </div>

        <div className={styles.listOfMigrations}>
          <h2>LIST OF MIGRATIONS</h2>
          {filteredMigrations.length === 0 && !loading && (
            <div className={styles.unableToFind}>
              <img src="/assets/help.png" alt="Help Icon" className={styles.helpIcon} />
              <span>Unable to find migration</span>
            </div>
          )}
        </div>

        <div className={styles.tableControls}>
          <div className={styles.filterContainer} style={{ justifyContent: 'space-between', width: '100%' }}>
            <button
              onClick={fetchMigrations}
              disabled={loading}
              className={globalStyles.mainButton}
              style={{
                padding: "8px 16px",
                fontSize: "14px",
                fontWeight: "500",
                opacity: loading ? 0.7 : 1,
                cursor: loading ? "not-allowed" : "pointer",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "6px",
                margin: "0",
                marginBottom: "12px",
                marginLeft: "0"
              }}
            >
              <RefreshIcon
                style={{
                  width: "14px",
                  height: "14px",
                  animation: loading ? "spin 1s linear infinite" : "none"
                }}
              />
              {loading ? "Refreshing..." : "Refresh"}
            </button>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div className={styles.filterDropdownContainer}>
                <button className={styles.filterButton} onClick={toggleFilterDropdown} style={{ marginBottom: "12px" }}>
                  <HiOutlineFunnel />
                  {getFilterDisplayText()}
                </button>
                {showFilterDropdown && (
                  <div className={styles.filterDropdown}>
                    <div className={styles.filterOption} onClick={() => handleFilterSelect("all")}>
                      See all
                    </div>
                    <div className={styles.filterOption} onClick={() => handleFilterSelect("completed")}>
                      Completed
                    </div>
                    <div className={styles.filterOption} onClick={() => handleFilterSelect("terminated")}>
                      Terminated
                    </div>
                    <div className={styles.filterOption} onClick={() => handleFilterSelect("in progress")}>
                      In Progress
                    </div>
                  </div>
                )}
              </div>
              <div className={globalStyles.searchWrapper} style={{ marginRight: 0 }}>
                <SearchIcon className={globalStyles.searchIcon} />
                <input
                  type="text"
                  placeholder="Search..."
                  className={globalStyles.searchInput}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onFocus={(e) => {
                    e.target.style.width = "200px"
                    e.target.placeholder = "Typing..."
                  }}
                  onBlur={(e) => {
                    e.target.style.width = "80px"
                    e.target.placeholder = "Search..."
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {loading ? (
          <div className={styles.loadingContainer}>Loading migrations...</div>
        ) : error ? (
          <div className={styles.errorContainer}>{error}</div>
        ) : (
          <div className={styles.tableWrapper}>
            <table className={styles.migrationsTable}>
              <thead>
                <tr>
                  <th>Migration ID</th>
                  <th>Status</th>
                  <th>Template Name</th>
                  <th>Timeline</th>
                  <th>No.of records</th>
                  <th>Details</th>
                </tr>
              </thead>
              <tbody>
                {paginatedData.length > 0 ? (
                  paginatedData.map((migration, index) => (
                    <tr key={migration.migrationId}>
                      <td className={styles.idCell}>
                        <div>{migration?.migrationId}</div>
                      </td>
                      <td>
                        <span className={`${styles.statusBadge} ${getStatusDetails(migration).className}`}>
                          {getStatusDetails(migration).text}
                        </span>

                      </td>
                      <td>{(migration.name || migration.template || '').split(' -')[0]}</td>
                      <td>{formatTimestamp(migration.startTime)}</td>
                      <td>{migration.totalRecords || "0"}</td>
                      <td>
                        <button className={styles.detailsButton} onClick={() => handleDetailsClick(migration)}>
                          <span>↗</span>
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className={styles.noDataCell}>
                      No migrations found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        <div className={styles.paginationContainer}>
          <div className={styles.pageInfo}>
            <span className={styles.paginationText}>Page no</span>
            <div className={styles.pageButtons}>
              <button className={styles.arrowButton} onClick={() => handlePageChange(0)} disabled={currentPage === 0}>
                <FaAngleDoubleLeft />
              </button>
              <button
                className={styles.arrowButton}
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 0}
              >
                <FaChevronLeft />
              </button>

              {getVisiblePages().map((page, index) => {
                if (page === "ellipsis-start" || page === "ellipsis-end") {
                  return (
                    <span key={index} className={styles.pageEllipsis}>
                      ...
                    </span>
                  )
                }
                return (
                  <button
                    key={page}
                    className={`${styles.pageButton} ${currentPage === page ? styles.active : ""}`}
                    onClick={() => handlePageChange(page)}
                  >
                    {(page + 1).toString().padStart(2, "0")}
                  </button>
                )
              })}

              <button
                className={styles.arrowButton}
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= totalPages - 1}
              >
                <FaChevronRight />
              </button>
              <button
                className={styles.arrowButton}
                onClick={() => handlePageChange(totalPages - 1)}
                disabled={currentPage >= totalPages - 1}
              >
                <FaAngleDoubleRight />
              </button>
            </div>
          </div>
          <div className={styles.rowsPerPageContainer}>
            <span className={styles.paginationText}>Show</span>
            <div className={styles.rowsButtons}>
              <button
                className={`${styles.rowsButton} ${rowsPerPage === 25 ? styles.active : ""}`}
                onClick={() => handleRowsPerPageChange(25)}
              >
                25
              </button>
              <button
                className={`${styles.rowsButton} ${rowsPerPage === 50 ? styles.active : ""}`}
                onClick={() => handleRowsPerPageChange(50)}
              >
                50
              </button>
              <button
                className={`${styles.rowsButton} ${rowsPerPage === 75 ? styles.active : ""}`}
                onClick={() => handleRowsPerPageChange(75)}
              >
                75
              </button>
            </div>
          </div>
          {/* <button className={`${globalStyles.mainButton} ${styles.downloadButton}`}>Download migration report</button> */}
        </div>
      </div>
    </div>
  ))
}

export default MigrationHistory
