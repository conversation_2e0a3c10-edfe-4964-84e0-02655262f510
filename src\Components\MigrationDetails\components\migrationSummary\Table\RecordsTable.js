import React, { useState, useEffect } from "react";
import PropTypes from 'prop-types';
import { Dialog, DialogContent } from "@mui/material";
import { HiOutlineFunnel } from "react-icons/hi2";
import { FaChevronLeft, FaChevronRight, FaAngleDoubleLeft, FaAngleDoubleRight } from "react-icons/fa";
import { SearchIcon } from "@heroicons/react/solid";
import styles from "../../../MigrationDetails.module.css";
import globalStyles from "../../../../globalStyles.module.css";
import bseStyles from "../BSE.module.css";
import ErrorLogDialog from "../../errorLog/ErrorLogDialog";
import RecordRow from "./RecordRow";
import { migration } from "../../../../apiService";

// Column keys for CSV export
const CSV_KEYS = [
    'type','source_id','target_id','start_time','end_time','status','request','response','tags','parent_source_id','parent_target_id','info','info_status'
];

const ROWS_OPTIONS = [100, 250, 500];

const RecordsTable = ({
    records,
    currentPage,
    rowsPerPage,
    currentFilter,
    expandedRows,
    toggleRowExpand,
    subIsLoading,
    subTableData,
    formatTimestamp,
    getRowClass,
    getStatusClass,
    activeDependentTabs,
    toggleTab,
    onRetryBatch,
    onPageChange,
    onRowsPerPageChange,
    onFilterChange,
    entityType,
    totalItems,
    onSearch,
    migrationId
}) => {
    const [showFilterDropdown, setShowFilterDropdown] = useState(false);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selectedPayload, setSelectedPayload] = useState(null);
    const [errorLogData, setErrorLogData] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [isLoadingPayload, setIsLoadingPayload] = useState(false);
    const [payloadError, setPayloadError] = useState(null);
    const [isDownloading, setIsDownloading] = useState(false);
    const [showDownloadModal, setShowDownloadModal] = useState(false);
    const [searchTimeout, setSearchTimeout] = useState(null);
    const [downloadFormat, setDownloadFormat] = useState('json');

    useEffect(() => {
        return () => {
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
        };
    }, [searchTimeout]);

    const getFilterDisplayText = () => {
        switch (currentFilter) {
            case "passed": return "Success records";
            case "failed": return "Error records";
            default: return "See all";
        }
    };

    const toggleFilterDropdown = () => {
        setShowFilterDropdown(!showFilterDropdown);
    };

    const getDownloadModalMessage = () => {
        switch (currentFilter) {
            case "passed": return "All the Success records will be downloaded";
            case "failed": return "All the Error records will be downloaded";
            default: return "All the records will be downloaded";
        }
    };

    const handleDownloadClick = () => {
        setShowDownloadModal(true);
        setDownloadFormat('json');
    };

    const handleDownloadConfirm = () => {
        setShowDownloadModal(false);
        if (downloadFormat === 'json') {
            handleDownload();
        } else if (downloadFormat === 'csv') {
            handleDownloadCSV();
        } else if (downloadFormat === 'logs') {
            alert('Logs download not implemented yet.');
        }
    };

    // CSV conversion helper
    const convertToCSVWithInfo = (rows) => {
        const csvRows = [CSV_KEYS.join(',')];
        rows.forEach(row => {
            const values = CSV_KEYS.map(k => {
                let val = row[k];
                if (typeof val === 'object' && val !== null) val = JSON.stringify(val);
                if (typeof val === 'string' && (val.includes(',') || val.includes('"') || val.includes('\n'))) {
                    val = '"' + val.replace(/"/g, '""') + '"';
                }
                return val ?? '';
            });
            csvRows.push(values.join(','));
        });
        return csvRows.join('\n');
    };

    const handleDownloadCSV = async () => {
        setIsDownloading(true);
        try {
            const actualMigrationId = migrationId;
            const downloadParams = { migrationId: actualMigrationId };
            if (currentFilter === "passed") downloadParams.status = "SUCCESS";
            else if (currentFilter === "failed") downloadParams.status = "FAILED";
            const data = await migration.downloadMigrationFile(downloadParams);
            let rows = [];
            if (data && data.entity && data.entity.tickets) {
                const tickets = Object.values(data.entity.tickets);
                for (const ticket of tickets) {
                    let request = '';
                    let response = '';
                    let tags = '';
                    if (ticket.payload) {
                        if (ticket.payload.request) request = JSON.stringify(ticket.payload.request);
                        if (ticket.payload.response) response = JSON.stringify(ticket.payload.response);
                        if (ticket.payload.tags && Array.isArray(ticket.payload.tags)) tags = ticket.payload.tags.join(', ');
                        else if (ticket.payload.tags) tags = ticket.payload.tags;
                    }
                    if (ticket.tag) tags = Array.isArray(ticket.tag) ? ticket.tag.join(', ') : ticket.tag;
                    else if (ticket.tags) tags = Array.isArray(ticket.tags) ? ticket.tags.join(', ') : ticket.tags;
                    let row = {
                        type: 'ticket',
                        source_id: ticket.source_id,
                        target_id: ticket.target_id,
                        start_time: ticket.start_time,
                        end_time: ticket.end_time,
                        status: ticket.status,
                        request,
                        response,
                        tags,
                        parent_source_id: '',
                        parent_target_id: '',
                        info: '',
                        info_status: ''
                    };
                    if (ticket.status === 'FAILED' || ticket.status === 'failed' || ticket.status === 'ERROR') {
                        try {
                            const info = await migration.getErrorLog(actualMigrationId, ticket.source_id);
                            if (info && typeof info === 'object') {
                                row.info = info.id ?? ticket.source_id ?? '';
                                row.info_status = info.status ?? '';
                                if (info.errorBody) {
                                    row.request = JSON.stringify(info.errorBody);
                                    if (info.errorBody.tags) {
                                        if (Array.isArray(info.errorBody.tags)) row.tags = info.errorBody.tags.join(', ');
                                        else row.tags = info.errorBody.tags;
                                    }
                                }
                                if (info.errorResponse) row.response = JSON.stringify(info.errorResponse);
                            } else {
                                row.info = ticket.source_id ?? '';
                            }
                        } catch (e) {
                            row.info = ticket.source_id ?? '';
                        }
                    }
                    rows.push(row);
                    if (Array.isArray(ticket.dependents)) {
                        for (const dep of ticket.dependents) {
                            if (dep && dep.conversations) {
                                for (const conv of Object.values(dep.conversations)) {
                                    let convRequest = '';
                                    let convResponse = '';
                                    let convTags = '';
                                    if (conv.payload) {
                                        if (conv.payload.request) convRequest = JSON.stringify(conv.payload.request);
                                        if (conv.payload.response) convResponse = JSON.stringify(conv.payload.response);
                                        if (conv.payload.tags && Array.isArray(conv.payload.tags)) convTags = conv.payload.tags.join(', ');
                                        else if (conv.payload.tags) convTags = conv.payload.tags;
                                    }
                                    if (conv.tag) convTags = Array.isArray(conv.tag) ? conv.tag.join(', ') : conv.tag;
                                    else if (conv.tags) convTags = Array.isArray(conv.tags) ? conv.tags.join(', ') : conv.tags;
                                    let convRow = {
                                        type: 'conversation',
                                        source_id: conv.source_id,
                                        target_id: conv.target_id,
                                        start_time: conv.start_time,
                                        end_time: conv.end_time,
                                        status: conv.status,
                                        request: convRequest,
                                        response: convResponse,
                                        tags: convTags,
                                        parent_source_id: ticket.source_id,
                                        parent_target_id: ticket.target_id,
                                        info: '',
                                        info_status: ''
                                    };
                                    if (conv.status === 'FAILED' || conv.status === 'failed' || conv.status === 'ERROR') {
                                        try {
                                            const info = await migration.getErrorLog(actualMigrationId, conv.source_id);
                                            if (info && typeof info === 'object') {
                                                convRow.info = info.id ?? conv.source_id ?? '';
                                                convRow.info_status = info.status ?? '';
                                                if (info.errorBody) {
                                                    convRow.request = JSON.stringify(info.errorBody);
                                                    if (info.errorBody.tags) {
                                                        if (Array.isArray(info.errorBody.tags)) convRow.tags = info.errorBody.tags.join(', ');
                                                        else convRow.tags = info.errorBody.tags;
                                                    }
                                                }
                                                if (info.errorResponse) convRow.response = JSON.stringify(info.errorResponse);
                                            } else {
                                                convRow.info = conv.source_id ?? '';
                                            }
                                        } catch (e) {
                                            convRow.info = conv.source_id ?? '';
                                        }
                                    }
                                    rows.push(convRow);
                                }
                            }
                        }
                    }
                }
            }
            const csvString = convertToCSVWithInfo(rows);
            const blob = new Blob([csvString], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `migration-${actualMigrationId}-data.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            alert("CSV Download failed. Please try again.");
        } finally {
            setIsDownloading(false);
        }
    };

    const handleDownload = async () => {
        setIsDownloading(true);
        try {
            const actualMigrationId = migrationId;
            const downloadParams = { migrationId: actualMigrationId };
            if (currentFilter === "passed") downloadParams.status = "SUCCESS";
            else if (currentFilter === "failed") downloadParams.status = "FAILED";
            const data = await migration.downloadMigrationFile(downloadParams);
            const jsonString = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `migration-${actualMigrationId}-data.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            alert("Download failed. Please try again.");
        } finally {
            setIsDownloading(false);
        }
    };

    const handleDownloadCancel = () => {
        setShowDownloadModal(false);
    };

    const handleFilterSelect = (filter) => {
        onFilterChange(filter);
        setShowFilterDropdown(false);
    };

    const getFilteredRecords = () => {
        if (!records || !Array.isArray(records)) return [];
        if (typeof totalItems !== 'undefined') return records;
        switch (currentFilter) {
            case "passed":
                return records.filter(record =>
                    record.status === "success" || record.status === "SUCCESS");
            case "failed":
                return records.filter(record =>
                    record.status === "failed" || record.status === "FAILED" || record.status === "ERROR");
            default:
                return records;
        }
    };

    const getPaginatedRecords = () => {
        if (typeof totalItems !== 'undefined') {
            return getFilteredRecords();
        } else {
            const filtered = getFilteredRecords();
            const startIndex = currentPage * rowsPerPage;
            return filtered.slice(startIndex, startIndex + rowsPerPage);
        }
    };

    const getTotalPages = () => {
        if (typeof totalItems !== 'undefined') {
            return Math.ceil(totalItems / rowsPerPage);
        } else {
            const total = getFilteredRecords().length;
            return Math.ceil(total / rowsPerPage);
        }
    };

    const openPayloadDialog = async (record) => {
        setSelectedPayload(record);
        setIsDialogOpen(true);
        setIsLoadingPayload(true);
        setPayloadError(null);
        setErrorLogData(null);
        try {
            const sourceId = record.source_id || record.sourceId;
            if (sourceId && migrationId) {
                const payloadData = await migration.getErrorLog(migrationId, sourceId);
                setErrorLogData(payloadData);
            } else {
                setPayloadError('Missing source ID or migration ID for this record');
            }
        } catch (error) {
            setPayloadError('Failed to load payload data. Please try again.');
        } finally {
            setIsLoadingPayload(false);
        }
    };

    const closePayloadDialog = () => {
        setIsDialogOpen(false);
        setSelectedPayload(null);
        setErrorLogData(null);
        setIsLoadingPayload(false);
        setPayloadError(null);
    };

    const getVisiblePages = (currentPage, totalPages) => {
        const visiblePages = [];
        const maxVisiblePages = 5;
        if (totalPages <= maxVisiblePages) {
            for (let i = 0; i < totalPages; i++) visiblePages.push(i);
        } else {
            let startPage = Math.max(0, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = startPage + maxVisiblePages - 1;
            if (endPage >= totalPages) {
                endPage = totalPages - 1;
                startPage = Math.max(0, endPage - maxVisiblePages + 1);
            }
            if (startPage > 0) {
                visiblePages.push(0);
                if (startPage > 1) visiblePages.push("ellipsis-start");
            }
            for (let i = startPage; i <= endPage; i++) visiblePages.push(i);
            if (endPage < totalPages - 1) {
                if (endPage < totalPages - 2) visiblePages.push("ellipsis-end");
                visiblePages.push(totalPages - 1);
            }
        }
        return visiblePages;
    };

    const renderPagination = () => {
        const totalPages = getTotalPages();
        return (
            <div className={styles.paginationContainer}>
                <div className={styles.pageInfo}>
                    <span className={styles.paginationText}>Page no</span>
                    <div className={styles.pageButtons}>
                        <button className={styles.arrowButton} onClick={() => onPageChange(0)} disabled={currentPage === 0}>
                            <FaAngleDoubleLeft />
                        </button>
                        <button
                            className={styles.arrowButton}
                            onClick={() => onPageChange(currentPage - 1)}
                            disabled={currentPage === 0}
                        >
                            <FaChevronLeft />
                        </button>
                        {getVisiblePages(currentPage, totalPages).map((page, index) => {
                            if (page === "ellipsis-start" || page === "ellipsis-end") {
                                return (
                                    <span key={index} className={styles.pageEllipsis}>...</span>
                                );
                            }
                            return (
                                <button
                                    key={page}
                                    className={`${styles.pageButton} ${currentPage === page ? styles.active : ""}`}
                                    onClick={() => onPageChange(page)}
                                >
                                    {(page + 1).toString().padStart(2, "0")}
                                </button>
                            );
                        })}
                        <button
                            className={styles.arrowButton}
                            onClick={() => onPageChange(currentPage + 1)}
                            disabled={currentPage >= totalPages - 1}
                        >
                            <FaChevronRight />
                        </button>
                        <button
                            className={styles.arrowButton}
                            onClick={() => onPageChange(totalPages - 1)}
                            disabled={currentPage >= totalPages - 1}
                        >
                            <FaAngleDoubleRight />
                        </button>
                    </div>
                </div>
                <div className={styles.rowsPerPageContainer}>
                    <span className={styles.paginationText}>Show</span>
                    <div className={styles.rowsButtons}>
                        {ROWS_OPTIONS.map((rows) => (
                            <button
                                key={rows}
                                className={`${styles.rowsButton} ${rowsPerPage === rows ? styles.active : ""}`}
                                onClick={() => onRowsPerPageChange(rows)}
                            >
                                {rows}
                            </button>
                        ))}
                    </div>
                </div>
            </div>
        );
    };

    // Search handlers
    const handleSearch = async () => {
        if (!searchTerm.trim()) return clearSearch();
        try {
            if (onSearch) {
                await onSearch(searchTerm);
                if (currentPage !== 0) onPageChange(0);
            }
        } catch {}
    };

    const handleSearchChange = (e) => {
        const value = e.target.value;
        setSearchTerm(value);
        if (searchTimeout) clearTimeout(searchTimeout);
        const timeout = setTimeout(async () => {
            if (value.trim() === '') {
                clearSearch();
            } else {
                try {
                    if (onSearch) {
                        await onSearch(value.trim());
                        if (currentPage !== 0) onPageChange(0);
                    }
                } catch {}
            }
        }, 500);
        setSearchTimeout(timeout);
    };

    const handleSearchKeyPress = (e) => {
        if (e.key === 'Enter') handleSearch();
    };

    const clearSearch = () => {
        setSearchTerm('');
        try {
            if (onSearch) {
                onSearch('');
                if (currentPage !== 0) onPageChange(0);
            }
        } catch {}
    };

    return (
        <div className={styles.recordsSection}>
            <div className={styles.recordsHeader} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                    {entityType && (
                        <h3 className={globalStyles.poppinsHeaderStyle} style={{
                            fontSize: "18px",
                            margin: "0",
                            textTransform: "capitalize",
                            color: "#333"
                        }}>
                            {entityType}
                        </h3>
                    )}
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '15px', height: '40px' }}>
                    <div className={globalStyles.searchWrapper} style={{ 
                        height: '40px', 
                        display: 'flex', 
                        alignItems: 'center',
                        position: 'relative',
                        marginRight: '0'
                    }}>
                        <SearchIcon className={globalStyles.searchIcon} style={{
                            position: 'absolute',
                            left: '10px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            width: '20px',
                            height: '20px',
                            color: 'gray',
                            zIndex: 1
                        }} />
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={handleSearchChange}
                            onKeyPress={handleSearchKeyPress}
                            placeholder="Search by ID..."
                            className={globalStyles.searchInput}
                            style={{ 
                                height: '40px', 
                                width: '180px',
                                boxSizing: 'border-box',
                                padding: '0 12px 0 40px',
                                fontSize: '16px',
                                border: '1px solid #DCDAD9',
                                borderRadius: '7px',
                                backgroundColor: '#FFFFFF',
                                fontFamily: 'Inter',
                                fontWeight: '400',
                                margin: '0',
                                outline: 'none'
                            }}
                            onFocus={(e) => {
                                e.target.style.width = "200px";
                                e.target.placeholder = "Typing...";
                            }}
                            onBlur={(e) => {
                                e.target.style.width = "180px";
                                e.target.placeholder = "Search by ID...";
                            }}
                        />
                    </div>
                    {showDownloadModal && (
                        <Dialog open={showDownloadModal} maxWidth="md" onClose={handleDownloadCancel}>
                            <DialogContent style={{ padding: "0", overflow: "auto", backgroundColor: "#170903" }}>
                                <div
                                    style={{
                                        display: "flex",
                                        width: "450px",
                                        padding: "20px 30px 30px 30px",
                                        flexDirection: "column",
                                        alignItems: "flex-start",
                                        gap: "15px",
                                        borderRadius: "10px",
                                        background: "#170903",
                                        position: "relative",
                                    }}
                                >
                                    <button
                                        onClick={handleDownloadCancel}
                                        style={{
                                            position: "absolute",
                                            top: "20px",
                                            right: "20px",
                                            background: "none",
                                            border: "none",
                                            color: "#fff",
                                            fontSize: "24px",
                                            cursor: "pointer",
                                            padding: "0",
                                            width: "30px",
                                            height: "30px",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                        }}
                                    >
                                        ×
                                    </button>
                                    <h2
                                        style={{
                                            color: "#EA5822",
                                            fontFamily: "Poppins",
                                            fontSize: "14px",
                                            fontStyle: "normal",
                                            fontWeight: "700",
                                            lineHeight: "24px",
                                            margin: "0",
                                            textTransform: "uppercase",
                                            letterSpacing: "0.5px",
                                        }}
                                    >
                                        CONFIRM DOWNLOAD
                                    </h2>
                                    <p
                                        style={{
                                            color: "#F8F8F7",
                                            fontFamily: "Inter",
                                            fontSize: "14px",
                                            fontStyle: "normal",
                                            fontWeight: "400",
                                            lineHeight: "24px",
                                            margin: "0",
                                        }}
                                    >
                                        {getDownloadModalMessage()}
                                    </p>
                                    <div style={{ margin: '16px 0', display: 'flex', gap: '24px', color: '#F8F8F7', fontSize: 16, fontFamily: 'Inter', fontWeight: 500 }}>
                                        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                                            <input type="radio" name="downloadFormat" value="json" checked={downloadFormat === 'json'} onChange={() => setDownloadFormat('json')} style={{ accentColor: '#EA5822', marginRight: 6, width: 18, height: 18 }} />
                                            <span style={{ color: '#F8F8F7' }}>JSON</span>
                                        </label>
                                        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                                            <input type="radio" name="downloadFormat" value="csv" checked={downloadFormat === 'csv'} onChange={() => setDownloadFormat('csv')} style={{ accentColor: '#EA5822', marginRight: 6, width: 18, height: 18 }} />
                                            <span style={{ color: '#F8F8F7' }}>CSV</span>
                                        </label>
                                        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                                            <input type="radio" name="downloadFormat" value="logs" checked={downloadFormat === 'logs'} onChange={() => setDownloadFormat('logs')} style={{ accentColor: '#EA5822', marginRight: 6, width: 18, height: 18 }} />
                                            <span style={{ color: '#F8F8F7' }}>Logs</span>
                                        </label>
                                    </div>
                                    <div
                                        style={{
                                            display: "flex",
                                            gap: "10px",
                                            alignSelf: "flex-end",
                                            marginTop: "5px",
                                        }}
                                    >
                                        <button
                                            onClick={handleDownloadCancel}
                                            style={{
                                                display: "flex",
                                                height: "40px",
                                                padding: "8px 20px",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                gap: "10px",
                                                minWidth: "100px",
                                                borderRadius: "3px",
                                                background: "transparent",
                                                color: "#F8F8F7",
                                                textAlign: "center",
                                                fontFamily: "Inter",
                                                fontSize: "16px",
                                                fontStyle: "normal",
                                                fontWeight: "500",
                                                lineHeight: "24px",
                                                border: "1px solid #F8F8F7",
                                                cursor: "pointer",
                                                transition: "all 0.3s ease",
                                            }}
                                            onMouseEnter={(e) => {
                                                e.target.style.backgroundColor = "#F8F8F7";
                                                e.target.style.color = "#170903";
                                            }}
                                            onMouseLeave={(e) => {
                                                e.target.style.backgroundColor = "transparent";
                                                e.target.style.color = "#F8F8F7";
                                            }}
                                        >
                                            Cancel
                                        </button>
                                        <button
                                            onClick={handleDownloadConfirm}
                                            style={{
                                                display: "flex",
                                                height: "40px",
                                                padding: "8px 20px",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                gap: "10px",
                                                minWidth: "100px",
                                                borderRadius: "3px",
                                                background: "#EF8963",
                                                color: "#170903",
                                                textAlign: "center",
                                                fontFamily: "Inter",
                                                fontSize: "16px",
                                                fontStyle: "normal",
                                                fontWeight: "500",
                                                lineHeight: "24px",
                                                border: "none",
                                                cursor: "pointer",
                                                transition: "background-color 0.3s ease",
                                            }}
                                            onMouseEnter={(e) => {
                                                e.target.style.backgroundColor = "#EFEEED";
                                            }}
                                            onMouseLeave={(e) => {
                                                e.target.style.backgroundColor = "#EF8963";
                                            }}
                                        >
                                            Download
                                        </button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>
                    )}
                    <div style={{ height: '40px' }}>
                        <button
                            className={globalStyles.mainButton}
                            onClick={handleDownloadClick}
                            disabled={isDownloading}
                            title="Download Migration Report"
                            style={{
                                opacity: isDownloading ? 0.7 : 1,
                                cursor: isDownloading ? "not-allowed" : "pointer",
                                height: '40px',
                                width: '40px',
                                margin: '0',
                                padding: '0',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                        >
                            {isDownloading ? (
                                <div style={{
                                    width: "16px",
                                    height: "16px",
                                    border: "2px solid transparent",
                                    borderTop: "2px solid currentColor",
                                    borderRadius: "50%",
                                    animation: "spin 1s linear infinite"
                                }}></div>
                            ) : (
                                <svg
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M12 16L7 11L8.41 9.59L11 12.17V4H13V12.17L15.59 9.59L17 11L12 16Z"
                                        fill="currentColor"
                                    />
                                    <path
                                        d="M20 16V18C20 19.1 19.1 20 18 20H6C4.9 20 4 19.1 4 18V16H6V18H18V16H20Z"
                                        fill="currentColor"
                                    />
                                </svg>
                            )}
                        </button>
                    </div>
                    <div className={bseStyles.filterContainer} style={{ height: '40px' }}>
                        <div className={bseStyles.filterDropdownContainer}>
                            <button 
                                className={bseStyles.filterButton} 
                                onClick={toggleFilterDropdown}
                                style={{ height: '40px' }}
                            >
                                <HiOutlineFunnel className={bseStyles.filterIcon} />
                                {getFilterDisplayText()}
                            </button>
                            {showFilterDropdown && (
                                <div className={bseStyles.filterDropdown}>
                                    <div className={bseStyles.filterOption} onClick={() => handleFilterSelect("all")}>See all</div>
                                    <div className={bseStyles.filterOption} onClick={() => handleFilterSelect("passed")}>Success records</div>
                                    <div className={bseStyles.filterOption} onClick={() => handleFilterSelect("failed")}>Error records</div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            <div className={styles.recordsTableContainer}>
                <table className={styles.recordsTable}>
                    <thead>
                        <tr>
                            <th className={styles.centerColumn}>#</th>
                            <th>Source ID</th>
                            <th>Record ID</th>
                            <th>Start Time</th>
                            <th>End Time</th>
                            <th>Status</th>
                            <th>See Payload</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {getPaginatedRecords().length > 0 ? (
                            getPaginatedRecords().map((record, index) => (
                                <RecordRow
                                    key={record.key_id || record.id || record.sourceId || index}
                                    record={record}
                                    index={currentPage * rowsPerPage + index}
                                    expandedRows={expandedRows}
                                    toggleRowExpand={toggleRowExpand}
                                    subIsLoading={subIsLoading}
                                    subTableData={subTableData}
                                    formatTimestamp={formatTimestamp}
                                    getRowClass={getRowClass}
                                    getStatusClass={getStatusClass}
                                    openPayloadDialog={openPayloadDialog}
                                    activeDependentTabs={activeDependentTabs}
                                    toggleTab={toggleTab}
                                />
                            ))) : (
                            <tr>
                                <td colSpan="8" className={styles.noRecordsCell}>No records found</td>
                            </tr>
                        )}
                    </tbody>
                </table>
                <Dialog open={isDialogOpen} maxWidth="md" fullWidth PaperProps={{ style: { width: "70%" } }}>
                    <DialogContent sx={{ backgroundColor: "#170903", padding: "0" }}>
                        <ErrorLogDialog
                            isOpen={isDialogOpen}
                            selectedPayload={selectedPayload}
                            errorLogData={errorLogData}
                            onClose={closePayloadDialog}
                            isLoading={isLoadingPayload}
                            error={payloadError}
                        />
                    </DialogContent>
                </Dialog>
            </div>
            {renderPagination()}
        </div>
    );
};

RecordsTable.propTypes = {
    records: PropTypes.array.isRequired,
    currentPage: PropTypes.number.isRequired,
    rowsPerPage: PropTypes.number.isRequired,
    currentFilter: PropTypes.string.isRequired,
    expandedRows: PropTypes.object.isRequired,
    toggleRowExpand: PropTypes.func.isRequired,
    subIsLoading: PropTypes.object.isRequired,
    subTableData: PropTypes.object.isRequired,
    formatTimestamp: PropTypes.func.isRequired,
    getRowClass: PropTypes.func.isRequired,
    getStatusClass: PropTypes.func.isRequired,
    activeDependentTabs: PropTypes.object.isRequired,
    toggleTab: PropTypes.func.isRequired,
    onRetryBatch: PropTypes.func.isRequired,
    onPageChange: PropTypes.func.isRequired,
    onRowsPerPageChange: PropTypes.func.isRequired,
    onFilterChange: PropTypes.func.isRequired,
    onSearch: PropTypes.func,
    entityType: PropTypes.string,
    totalItems: PropTypes.number,
    migrationId: PropTypes.string
};

export default RecordsTable;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = `
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
`;
document.head.appendChild(styleSheet);