.header{
    background-color: #FFFFFF;
}
.buttonStyle{
    border: 1px solid #DCDAD9;
    padding: 5px;
    border-radius: 3px;
    background-color: white;
    cursor: pointer;
}
.mappingGuide{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    background-color: #EFEEED;
    padding: 0 45px;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
}
.closemap{
    height: 50px !important;
    overflow: hidden;
}
.guideStyle{
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    color: #170903;
    line-height: 24px;

}
.closemg{
    height: 50px;
    /*justify-content: center;*/
    align-items: center;
}

.dFlex{
    display: flex;
}
.iconStyle{
    height: 24px;
    width: 24px;
    cursor: pointer;
}
.statusCardsContainer {
    display: flex;
    padding: 20px 45px;
    gap: 20px;
    height: 128px;
}

.statusCard {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.countNumber {
    font-family: Poppins;
    font-weight: 600;
    font-size: 36px;
    line-height: 24px;
    color: #746B68;
    margin-right: 15px;
}

.countDescription {
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #170903;
}

.section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.leftSection {
    display: flex;
    height: 128px;
}

.leftCard {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rightSection {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 128px;
}

.rightCard {
    height: 54px; /* Each right card takes equal height */
    display: flex;    align-items: center;    justify-content: center;
}
.deleteButton{
    display: flex;
    justify-content: center;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.mappingRow {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
}

.mappingRowIndented {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    padding-left: 24px;
}

.mappingRowDoubleIndented {
    display: flex;
    align-items: flex-start;
    padding-left: 48px;
}



.mappingContent {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sourceContainer, .targetContainer {
    display: flex;
    align-items: center;
    width: 50%;
}

.arrowContainer {
    margin: 0 16px;
    display: flex;
    align-items: center;
}

.dropdownContainer {
    position: relative;
    width: 100%;
}

.dropdown {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
}

.dropdownText {
    font-size: 14px;
    color: #374151;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdownIcon {
    width: 16px;
    height: 16px;
    color: #6b7280;
}

.chevronIcon {
    width: 16px;
    height: 16px;
    color: #6b7280;
    margin-right: 8px;
}

.dropdownWrapper {
    width: 100%;
}
.buttonContainer {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}

.combineButton {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    color: #333;
}

.combineButton:hover {
    background-color: #f5f5f5;
}

/* Hover effects for Smart Auto-Map and Clear All Mappings buttons */
.smartAutoMapBtn {
    background-color: #EA5822 !important;
    transition: background-color 0.2s ease !important;
}

.smartAutoMapBtn:hover {
    background-color: #ec9d7f !important;
    transition: background-color 0.2s ease !important;
}

.clearAllMappingsBtn {
    background-color: #DC2626 !important;
    transition: background-color 0.2s ease !important;
}

.clearAllMappingsBtn:hover {
    background-color: rgb(132, 16, 16) !important;
    transition: background-color 0.2s ease !important;
}

.buttonIcon {
    width: 16px;
    height: 16px;
}

