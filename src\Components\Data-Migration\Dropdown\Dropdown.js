import React, { useState, useRef, useEffect } from 'react';

const dropdownStyles = `
  /* Search input styles */
  .simple-dropdown .search-container {
    padding: 8px;
    position: sticky;
    top: 0;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    z-index: 2;
  }
  .simple-dropdown .search-input {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: #170903;
    outline: none;
    transition: border-color 0.2s;
  }
  .simple-dropdown .search-input:focus {
    border-color: #EA5822;
    box-shadow: 0 0 0 2px rgba(234, 88, 34, 0.1);
  }

  .simple-dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
  }
  .simple-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.5rem 1rem;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: #170903;
    background-color: #fff;
    border: 1px solid #fff;
    border-radius: 0.35rem;
    cursor: pointer;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-height: 42px;
    max-width: 260px;
    box-shadow: 0 2px 8px rgba(234,88,34,0.08);
    transition: border-color 0.2s, box-shadow 0.2s;
  }
  .simple-dropdown .dropdown-toggle:focus, .simple-dropdown .dropdown-toggle:hover {
    border-color:rgb(238, 104, 55);
    box-shadow: 0 0 0 1.5px #EA582233;
  }
  .simple-dropdown .dropdown-toggle span {
    max-width: 220px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    color: #111;
    font-size: 14px;
    font-weight: 300;
  }
  .simple-dropdown .dropdown-toggle::after {
    margin-left: auto;
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-top: 0.4em solid rgb(234, 105, 58);
    border-right: 0.4em solid transparent;
    border-left: 0.4em solid transparent;
  }
  .simple-dropdown .dropdown-menu {
    position: absolute;
    z-index: 1000;
    display: none;
    min-width: 100%;
    padding: 0;
    margin: 0;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: #170903;
    background-color: #fff;
    border: 1px solid #EA5822;
    border-radius: 0.35rem;
    box-shadow: 0 4px 16px rgba(234,88,34,0.12);
    max-height: 320px;
    overflow-y: auto;
    margin-top: 2px;
  }
  .simple-dropdown .dropdown-menu.show {
    display: block;
  }
  .simple-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.5rem 1.2rem;
    font-family: Inter, sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #170903;
    background-color: transparent;
    border: 0;
    cursor: pointer;
    max-width: 260px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: background 0.2s, color 0.2s;
  }
  .simple-dropdown .dropdown-item:hover, .simple-dropdown .dropdown-item.bg-light {
    background-color: #EA582222;
    color: #EA5822;
  }
  .simple-dropdown .dropdown-item input[type='checkbox'] {
    accent-color: #EA5822;
  }
  .simple-dropdown .dropdown-item.text-muted {
    color: #888;
    font-style: italic;
  }
`;

// Utility to extract a string label from any value
function getDisplayLabel(label) {
  if (!label) return '';
  if (typeof label === 'string' || typeof label === 'number') return String(label);
  if (typeof label === 'object') {
    // Try common keys
    if (label.label) return getDisplayLabel(label.label);
    if (label.name) return getDisplayLabel(label.name);
    if (label.value) return getDisplayLabel(label.value);
    if (label.id) return getDisplayLabel(label.id);
    // If none, try to stringify
    return JSON.stringify(label);
  }
  return String(label);
}

export default function SimpleDropdown({
  value = '',
  options = [],
  onChange = () => {},
  placeholder = 'Select...',
  required = false,
  displayKey = 'label',
  valueKey = 'value',
  multiSelect = false, // Add multiSelect prop
  showSelectAll = false, // Add showSelectAll prop
}) {
  // Use multiSelect and showSelectAll as passed in props, do not force for 'source' placeholder
  const effectiveMultiSelect = multiSelect;
  const effectiveShowSelectAll = showSelectAll;

  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);
  const [selectedValues, setSelectedValues] = useState(() => {
    if (effectiveMultiSelect) {
      return Array.isArray(value) ? value : (value ? [value] : []);
    }
    return value;
  });

  // Update selected values when value prop changes
  useEffect(() => {
    if (effectiveMultiSelect) {
      setSelectedValues(Array.isArray(value) ? value : (value ? [value] : []));
    }
  }, [value, effectiveMultiSelect]);

  // Initialize filtered options when options change
  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  // Filter options based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredOptions(options);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = options.filter(option => {
        let label = '';
        if (typeof option === 'object') {
          label = getDisplayLabel(option[displayKey]);
        } else {
          label = getDisplayLabel(option);
        }
        return label.toLowerCase().includes(query);
      });
      setFilteredOptions(filtered);
    }
  }, [searchQuery, options, displayKey]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (menuIsOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    } else {
      // Clear search when dropdown closes
      setSearchQuery('');
    }
  }, [menuIsOpen]);

  // Handle keyboard events for accessibility
  const handleKeyDown = (event) => {
    if (!menuIsOpen) return;

    switch (event.key) {
      case 'Escape':
        setMenuIsOpen(false);
        break;
      case 'ArrowDown':
        if (searchInputRef.current === document.activeElement) {
          // Move focus to the first option
          const firstOption = dropdownRef.current.querySelector('.dropdown-item');
          if (firstOption) {
            event.preventDefault();
            firstOption.focus();
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setMenuIsOpen(false);
      }
    };

    const handleKeyDownEvent = (event) => {
      handleKeyDown(event);
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDownEvent);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDownEvent);
    };
  }, [menuIsOpen]);

  const handleOptionClick = (option, e) => {
    if (e) e.stopPropagation(); // Prevent event bubbling
    
    console.log('Option clicked:', option, 'current selectedValues:', selectedValues);
    
    if (effectiveMultiSelect) {
      // Ensure selectedValues is always an array
      const currentSelected = Array.isArray(selectedValues) ? selectedValues : [];
      
      // Use the whole option object for selection
      let newSelected;
      const optionValue = typeof option === 'object' ? option[valueKey] : option;
      
      // Check if this option is already selected
      const isAlreadySelected = currentSelected.some(v => {
        const selectedValue = typeof v === 'object' ? v[valueKey] : v;
        return selectedValue === optionValue;
      });
      
      console.log('Option is already selected:', isAlreadySelected);
      
      if (isAlreadySelected) {
        // Remove the option
        newSelected = currentSelected.filter(v => {
          const selectedValue = typeof v === 'object' ? v[valueKey] : v;
          return selectedValue !== optionValue;
        });
      } else {
        // Add the option
        newSelected = [...currentSelected, option];
      }
      console.log('New selected length:', newSelected.length);
      setSelectedValues(newSelected);
      onChange(newSelected);
    } else {
      // Pass the whole option object for single select
      onChange(option);
      setMenuIsOpen(false);
    }
  };

  const handleSelectAll = (e) => {
    e.stopPropagation(); // Prevent event bubbling
    if (!effectiveMultiSelect) return;

    // Ensure selectedValues is always an array
    const currentSelected = Array.isArray(selectedValues) ? selectedValues : [];

    console.log('Select All clicked', { 
      filteredOptionsLength: filteredOptions.length, 
      selectedValuesLength: currentSelected.length 
    });

    // Get all option objects from filtered options
    const filteredOptionObjects = filteredOptions.slice();

    // Get values from filtered options for comparison
    const filteredValues = filteredOptionObjects.map(opt =>
      typeof opt === 'object' ? opt[valueKey] : opt
    );

    // Check if all filtered options are selected by comparing values
    const allFilteredSelected = filteredValues.every(val => {
      return currentSelected.some(selectedVal => {
        const selectedValue = typeof selectedVal === 'object' ? selectedVal[valueKey] : selectedVal;
        return selectedValue === val;
      });
    });

    console.log('All filtered selected:', allFilteredSelected);

    if (allFilteredSelected) {
      // Deselect only the filtered options - keep objects that don't match filtered values
      const newValues = currentSelected.filter(selectedVal => {
        const selectedValue = typeof selectedVal === 'object' ? selectedVal[valueKey] : selectedVal;
        return !filteredValues.includes(selectedValue);
      });
      console.log('Deselecting, new values length:', newValues.length);
      setSelectedValues(newValues);
      onChange(newValues);
    } else {
      // Select all filtered options (keeping previously selected ones)
      // First remove any existing selections that match filtered values to avoid duplicates
      const cleanedExisting = currentSelected.filter(selectedVal => {
        const selectedValue = typeof selectedVal === 'object' ? selectedVal[valueKey] : selectedVal;
        return !filteredValues.includes(selectedValue);
      });
      
      // Then add all filtered option objects
      const newValues = [...cleanedExisting, ...filteredOptionObjects];
      console.log('Selecting all, new values length:', newValues.length);
      setSelectedValues(newValues);
      onChange(newValues);
    }
  };

  const displayText = () => {
    if (effectiveMultiSelect) {
      const currentSelected = Array.isArray(selectedValues) ? selectedValues : [];
      if (!currentSelected || currentSelected.length === 0) {
        return `${placeholder}${required ? '*' : ''}`;
      }
      if (currentSelected.length > 2) {
        return `${currentSelected.length} selected`;
      }
      return options
        .filter((opt) => {
          const optValue = typeof opt === 'object' ? opt[valueKey] : opt;
          return currentSelected.some(selectedVal => {
            const selectedValue = typeof selectedVal === 'object' ? selectedVal[valueKey] : selectedVal;
            return selectedValue === optValue;
          });
        })
        .map((opt) => {
          if (typeof opt === 'object') {
            return getDisplayLabel(opt[displayKey]);
          }
          return getDisplayLabel(opt);
        })
        .join(', ');
    } else {
      if (!value && value !== 0) {
        return `${placeholder}${required ? '*' : ''}`;
      }
      const selected = options.find(
        (opt) => (typeof opt === 'object' ? opt[valueKey] : opt) === value
      );
      if (selected) {
        if (typeof selected === 'object') {
          return getDisplayLabel(selected[displayKey]);
        }
        return getDisplayLabel(selected);
      }
      return getDisplayLabel(value);
    }
  };

  return (
    <div className="simple-dropdown" ref={dropdownRef}>
      <style>{dropdownStyles}</style>
      <div className="dropdown">
        <button
          className="dropdown-toggle"
          type="button"
          onClick={() => setMenuIsOpen((prev) => !prev)}
          aria-haspopup="listbox"
          aria-expanded={menuIsOpen}
          aria-label={`${placeholder} dropdown`}
        >
          <span>{displayText()}</span>
        </button>
        <div
          className={`dropdown-menu${menuIsOpen ? ' show' : ''}`}
          role="listbox"
          aria-multiselectable={effectiveMultiSelect}
          aria-label={`${placeholder} options`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Search input */}
          <div className="search-container">
            <input
              ref={searchInputRef}
              type="text"
              className="search-input"
              placeholder="Search options..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onClick={(e) => e.stopPropagation()}
              aria-label="Search dropdown options"
              aria-controls="dropdown-options"
              autoComplete="off"
              spellCheck="false"
            />
          </div>

          {/* Select All option for multi-select */}
          {effectiveMultiSelect && filteredOptions.length > 0 && (
            <div
              className="dropdown-item"
              onClick={handleSelectAll}
              style={{ fontFamily: 'Inter, sans-serif', fontWeight: '400', fontSize: '14px', lineHeight: '24px', color: '#EA5822' }}
            >
              {filteredOptions.every(opt => {
                const optValue = typeof opt === 'object' ? opt[valueKey] : opt;
                const currentSelected = Array.isArray(selectedValues) ? selectedValues : [];
                return currentSelected.some(selectedVal => {
                  const selectedValue = typeof selectedVal === 'object' ? selectedVal[valueKey] : selectedVal;
                  return selectedValue === optValue;
                });
              })
                ? 'Deselect All'
                : 'Select All'
              }
            </div>
          )}

          {/* No options message */}
          {filteredOptions.length === 0 && (
            <div className="dropdown-item" style={{ fontStyle: 'italic', color: '#666', fontFamily: 'Inter, sans-serif', fontWeight: '400', fontSize: '14px', lineHeight: '24px' }}>
              {searchQuery ? 'No matching options' : 'No options available'}
            </div>
          )}

          {/* Option items */}
          {filteredOptions.map((option, idx) => {
            const optValue = typeof option === 'object' ? option[valueKey] : option;
            const optLabel = typeof option === 'object' ? option[displayKey] : option;
            const displayLabel = getDisplayLabel(optLabel);
            
            const isSelected = effectiveMultiSelect ? 
              (Array.isArray(selectedValues) ? selectedValues : []).some(selectedVal => {
                const selectedValue = typeof selectedVal === 'object' ? selectedVal[valueKey] : selectedVal;
                return selectedValue === optValue;
              }) : value === optValue;
              
            return (
              <div
                key={optValue + '_' + idx}
                className={`dropdown-item${isSelected ? ' bg-light' : ''}`}
                onClick={(e) => handleOptionClick(option, e)}
                tabIndex="0"
                role="option"
                aria-selected={isSelected}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleOptionClick(option, e);
                  }
                }}
              >
                {effectiveMultiSelect && (
                  <input
                    type="checkbox"
                    checked={isSelected}
                    readOnly
                    style={{ marginRight: 8 }}
                    onClick={(e) => e.stopPropagation()}
                  />
                )}
                {displayLabel}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}