import { useEffect, useState, useC<PERSON>back, useMemo, useRef, useContext } from "react"
import Tabs from "@mui/material/Tabs"
import Tab from "@mui/material/Tab"
import styles from "./Data-mapping.module.css"
import globalStyles from "../../globalStyles.module.css"
import { CheckIcon, SearchIcon } from "@heroicons/react/solid"
import { HiNoSymbol } from "react-icons/hi2"
import { HiOutlineFunnel } from "react-icons/hi2"
import Dropdown from "../Dropdown/Dropdown"
import ValueMapping from "./Value-mapping/Value-mapping"
import DefaultValue from "./Default-value/DefaultValue"
import CombineFields from "./Combine-value/CombineFields"
import OverrideValues from "./OverrideValues/OverrideValues"
import Advanced from "./Advanced/Advanced"
import { postActivity, saveMigrationPlan, getMigrationPlanByIdandFields, patchMigrationPlanById } from "../../apiService"
import { CogIcon } from "@heroicons/react/outline"
import { MigrationContext } from "../Data-Migration"
import { toast, ToastContainer } from "react-toastify"
import { Dialog, DialogContent, FormControlLabel, Switch } from "@mui/material"
import "react-toastify/dist/ReactToastify.css"
import { displayArticle } from "../../../Helper/helper"
import LoaderSpinner from "../../loaderspinner"
import MappableToggle from "./Components/MappableToggle"
import SkipAllOptionalToggle from "./Components/SkipAllToggle"

export default function DataMapping({ setSelectedTab, planId, setPlanId, templateName, completedSteps }) {
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const dataTypeData = migrationState.dataTypeData
  const source = migrationState.sourceObjData
  // const uniqueSourceValues = [...new Set(Object.values(source.uniqueSourceValues).flat())];
  const uniqueSourceValues = source.uniqueSourceValues
  const target = migrationState.targetData
  const dataMappingData = migrationState.dataMappingData
  const targetExeRes = migrationState.targetExeRes
  const selectedObjectData = migrationState.selectedObjectData
  const selectedEntityData = migrationState.selectedEntityData
  const sourceExeRes = migrationState.sourceExeRes
  const sourceMapRes = migrationState.sourceMapRes
  const sourceResAtt = migrationState.sourceResAtt
  const dependentFields = migrationState.dataTypeData.dependentFields

  const [email, setEmail] = useState(localStorage.getItem("email"))
  const settingsDropdownRef = useRef({})
  const filterDropdownRef = useRef(null)
  const [value, setValue] = useState(0)
  const [isDependencyAdded, setIsDependencyAdded] = useState(true)
  const [mappedSourceFields, setMappedSourceFields] = useState(new Set())
  const [openDialog, setOpenDialog] = useState(false)
  const [openDefaultDialog, setOpenDefaultDialog] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const showPages = [15, 25, 50]
  const [itemsPerPage, setItemsPerPage] = useState(showPages[0])
  const [attributeNames, setAttributeNames] = useState([])
  const [filteredAttributes, setFilteredAttributes] = useState([])
  const [filteredList, setFilteredList] = useState([])
  const [targets, setTargets] = useState([])
  const [targetData, setTargetData] = useState([])
  const [searchTerm, setSearchTerm] = useState("")
  const [shouldRefreshTargets, setShouldRefreshTargets] = useState(false)
  const [currentTabData, setCurrentTabData] = useState([])
  const [selectedMapping, setSelectedMapping] = useState(null)
  const [selectedTarget, setSelectedTarget] = useState(null)
  const [showSettingsDropdown, setShowSettingsDropdown] = useState({})
  const [activeSettingIndex, setActiveSettingIndex] = useState(null)
  const [openCombineFieldsDialog, setOpenCombineFieldsDialog] = useState(false)
  const [openOverrideDialog, setOpenOverrideDialog] = useState(false)
  const [openAdvancedDialog, setOpenAdvancedDialog] = useState(false)
  const [isNotes, setIsNotes] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [savingMessage, setSavingMessage] = useState("Saving...")
  const [isSkipAllOptional, setIsSkipAllOptional] = useState(false)
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [selectedFilter, setSelectedFilter] = useState("See all")
  const [showTooltip, setShowTooltip] = useState(null)
  const [showSmartAutoMapConfirm, setShowSmartAutoMapConfirm] = useState(false)
  // New state for data type icon tooltip
  const [dataTypeTooltip, setDataTypeTooltip] = useState(null)

  // Add state to track if targets have been initialized from saved data
  const [isInitializedFromSavedData, setIsInitializedFromSavedData] = useState(false)

  // Dynamic workspace_id update - This useEffect will update workspace_id default value when target data changes
  useEffect(() => {
    if (target?.formData?.workspace_id && targets.length > 0) {
      const updatedTargets = targets.map((targetItem) => {
        if (["freshservice", "atomicworks"].includes(target.target.name)) {
          const updatedFieldMappings = targetItem.fieldMappings.map((field) => {
            if (field.targetfield === "workspace_id") {
              return {
                ...field,
                default: target.formData.workspace_id
              }
            }
            return field
          })
          return {
            ...targetItem,
            fieldMappings: updatedFieldMappings
          }
        }
        return targetItem
      })

      // Only update if there's actually a change
      const hasWorkspaceIdChanged = targets.some((targetItem) =>
        targetItem.fieldMappings.some((field) =>
          field.targetfield === "workspace_id" && field.default !== target.formData.workspace_id
        )
      )

      if (hasWorkspaceIdChanged) {
        setTargets(updatedTargets)
        setTargetData(updatedTargets)
      }
    }
  }, [target?.formData?.workspace_id, target?.target?.name, targets])

  // Additional useEffect to ensure workspace_id is updated when targetData changes
  useEffect(() => {
    if (target?.formData?.workspace_id && targetData.length > 0) {
      const needsUpdate = targetData.some((targetItem) =>
        targetItem.fieldMappings?.some((field) =>
          field.targetfield === "workspace_id" && field.default !== target.formData.workspace_id
        )
      )

      if (needsUpdate) {
        const updatedTargetData = targetData.map((targetItem) => {
          if (["freshservice", "atomicworks"].includes(target.target.name)) {
            const updatedFieldMappings = targetItem.fieldMappings.map((field) => {
              if (field.targetfield === "workspace_id") {
                return {
                  ...field,
                  default: target.formData.workspace_id
                }
              }
              return field
            })
            return {
              ...targetItem,
              fieldMappings: updatedFieldMappings
            }
          }
          return targetItem
        })
        setTargetData(updatedTargetData)
      }
    }
  }, [target?.formData?.workspace_id, targetData.length])

  // Add function to remove duplicate target fields
  const removeDuplicateTargetFields = useCallback((targets) => {
    return targets.map((target) => {
      if (!target.fieldMappings || target.fieldMappings.length === 0) {
        return target
      }

      // Track seen target fields and keep only the first occurrence
      const seenTargetFields = new Set()
      const uniqueFieldMappings = []

      target.fieldMappings.forEach((field) => {
        if (!seenTargetFields.has(field.targetfield)) {
          seenTargetFields.add(field.targetfield)
          uniqueFieldMappings.push(field)
        } else {
          console.warn(`Duplicate target field detected and removed: ${field.targetfield}`)
        }
      })

      return {
        ...target,
        fieldMappings: uniqueFieldMappings,
      }
    })
  }, [])

  // Override setTargets to always apply duplicate removal
  const setTargetsWithDuplicateRemoval = useCallback((newTargets) => {
    const cleanedTargets = removeDuplicateTargetFields(newTargets)
    setTargets(cleanedTargets)
  }, [removeDuplicateTargetFields])

  // Override setTargetData to always apply duplicate removal  
  const setTargetDataWithDuplicateRemoval = useCallback((newTargetData) => {
    const cleanedTargetData = removeDuplicateTargetFields(newTargetData)
    setTargetData(cleanedTargetData)
  }, [removeDuplicateTargetFields])

  const allFilterOptions = [
    "See all",
    "Mapped successfully",
    "Errors in mapping",
    "Data mismatch",
    "Unmapped fields",
    "Skipped fields",
    "Required fields",
  ]

  // Function to get data type icon
  const getDataTypeIcon = useCallback((type) => {
    const iconMap = {
      string: "/assets/text.png",
      number: "/assets/number.png",
      array: "/assets/array.png",
      "array of objects": "/assets/array.png",
      datetime: "/assets/date.png",
      date: "/assets/date.png",
      boolean: "/assets/text.png",
    }
    return iconMap[type] || "/assets/text.png"
  }, [])

  const getFilterOptions = () => {
    if (selectedFilter === "See all") {
      return allFilterOptions.filter((option) => option !== "See all")
    }
    return allFilterOptions.filter((option) => option !== selectedFilter)
  }

  const getSourceFieldDropdownValuesforFields = (attribute) => {
    if (uniqueSourceValues && attribute.mappingEnabled) {
      const sourceDropValueArray = Object.values(uniqueSourceValues)
      return getValuesByColumnNameforFields(sourceDropValueArray[value], attribute.sourcefield, "column", "values")
    }
    return []
  }

  const getValuesByColumnNameforFields = (arr, columnName, searchFieldName, returnFieldName) => {
    if (arr) {
      const columnObject = arr.find((obj) => obj[searchFieldName] === columnName)
      return columnObject ? columnObject[returnFieldName] : []
    }
    return []
  }

  const getSourceMappingObjectKey = (sourceMappingConfig) => {
    return sourceMappingConfig?.sourceMappingObjectKey?.trim()
      ? sourceMappingConfig.sourceMappingObjectKey
      : "ticket_fields"
  }

  const getSourceMappingFieldName = (sourceMappingConfig) => {
    return sourceMappingConfig?.fieldName?.trim() ? sourceMappingConfig.fieldName : "title"
  }

  const getTotalSourceCountforFields = (attribute, target) => {
    const sourceMappingKey = getSourceMappingObjectKey(
      selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
    )
    const fieldName = getSourceMappingFieldName(
      selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
    )

    if (attribute) {
      if (source.type == "csv") {
        return getSourceFieldDropdownValuesforFields(attribute).length
      } else if (source.type == "api") {
        if (target.name !== selectedEntityData.mainTarget[0].name && sourceMapRes && sourceMapRes[target.name]) {
          return sourceMapRes[target.name].length
        } else if (attribute.mappingTarget === "Agents" || attribute.mappingTarget === "Groups") {
          return sourceMapRes[attribute.mappingTarget]?.length
        } else if (
          target.name == selectedEntityData?.mainTarget[0].name &&
          (attribute.sourcefield == "sub_category" ||
            attribute.sourcefield == "category" ||
            attribute.attribute == "sub_category" ||
            attribute.sourcefield == "item_category" ||
            attribute.attribute == "item_category")
        ) {
          // For category-type fields, get the actual source values from the source data
          // instead of using mappings to avoid circular dependency
          if (sourceExeRes && sourceExeRes[sourceMappingKey] && sourceExeRes[sourceMappingKey].length > 0) {
            for (const field of sourceExeRes[sourceMappingKey]) {
              if (attribute.sourcefield == field[fieldName]) {
                return field.choices ? field.choices.length : 0
              }
            }
          }
          // Fallback: if no source data found, use the mapped values count
          // but only if mappings exist
          if (attribute?.mappings?.length > 0) {
            const totalSourceValueLength = attribute?.mappings?.reduce((total, item) => {
              const validCount = Array.isArray(item?.sourcevalue)
                ? item?.sourcevalue.filter((value) => value !== "" && value !== null).length
                : 0
              return total + validCount
            }, 0)
            return totalSourceValueLength
          } else {
            return 0 // Changed from -1 to 0 to avoid negative counts
          }
        } else {
          if (sourceExeRes && sourceExeRes[sourceMappingKey] && sourceExeRes[sourceMappingKey].length > 0) {
            for (const field of sourceExeRes[sourceMappingKey]) {
              if (attribute.sourcefield == field[fieldName]) {
                return field.choices.length
              }
            }
          }
        }
      }
    }
    return 0
  }

  const getTotalMappedCountforFields = (attribute) => {
    if (attribute && attribute.mappings) {
      // Collect all unique mapped source values to avoid double counting
      const uniqueMappedValues = new Set()
      attribute.mappings.forEach((obj) => {
        if (Array.isArray(obj.sourcevalue)) {
          obj.sourcevalue.forEach((value) => {
            // Handle both string values and object values
            let valueToCheck
            if (typeof value === "object" && value !== null) {
              // For objects, use the value property (consistent with flattenMappings)
              valueToCheck = value?.value
            } else {
              // For strings, use the value directly
              valueToCheck = value
            }
            // Exclude empty strings, null values, "null" strings, and "<"">" which represents empty strings
            if (
              valueToCheck !== "" &&
              valueToCheck !== null &&
              valueToCheck !== "null" &&
              valueToCheck !== '<"">' &&
              valueToCheck !== '<"">'
            ) {
              uniqueMappedValues.add(valueToCheck)
            }
          })
        }
      })
      return uniqueMappedValues.size
    }
    return 0
  }

  const showAlertIcon = (attribute, target, emptyColumns = []) => {
    if (attribute && !attribute.skip) {
      if (
        attribute.required &&
        (!attribute.sourcefield || attribute.sourcefield === "" || attribute.sourcefield === undefined) &&
        (attribute.default === undefined || attribute.default === "") &&
        (!attribute.combinedFields || (attribute.combinedFields && attribute.combinedFields.length == 0))
      ) {
        return true
      }
      if (source.type == "csv" && emptyColumns.includes(attribute.sourcefield)) {
        return true
      } else if (attribute.mappingEnabled) {
        // Check if mappingEnabled is ON but no source field is selected
        if (!attribute.sourcefield || attribute.sourcefield === "" || attribute.sourcefield === undefined) {
          return true
        }
        if (
          target.name === selectedEntityData.mainTarget[0].name &&
          (attribute.targetfield === "category" ||
            attribute.targetfield === "sub_category" ||
            attribute.targetfield === "item_category")
        ) {
          if (!attribute.mappings || attribute.mappings.length === 0) {
            return true
          }
          const hasEmptySourceValues = attribute.mappings.some(
            (mapping) =>
              !mapping.sourcevalue ||
              mapping.sourcevalue.length === 0 ||
              mapping.sourcevalue.every((val) => val === "" || val === null || val === "null"),
          )
          if (hasEmptySourceValues) {
            return true
          }
          // For category fields, check if all mappings have valid target values
          const hasValidTargetMappings = attribute.mappings.every(
            (mapping) =>
              mapping.targetvalue && (mapping.targetvalue.id !== undefined || mapping.targetvalue.id !== null),
          )
          if (!hasValidTargetMappings) {
            return true
          }
          // Additional check: ensure source count vs mapped count logic
          const sourceCount = getTotalSourceCountforFields(attribute, target)
          const mappedCount = getTotalMappedCountforFields(attribute)
          // Only show error if source count is positive and doesn't match mapped count
          if (sourceCount > 0 && sourceCount !== mappedCount) {
            return true
          }
        } else {
          const sourceCount = getTotalSourceCountforFields(attribute, target)
          const mappedCount = getTotalMappedCountforFields(attribute)
          if (sourceCount > 0 && sourceCount !== mappedCount) {
            if (target.name === "Requesters" && attribute.targetfield === "first_name") {
              return false
            }
            return true
          }
        }
      } else if (
        (!attribute.sourcefield || attribute.sourcefield === "" || attribute.sourcefield === undefined) &&
        (attribute.default === undefined || attribute.default === "") &&
        (!attribute.combinedFields || (attribute.combinedFields && attribute.combinedFields.length === 0))
      ) {
        return true
      }
    }
    return false
  }

  const handleAdvancedMappingDialog = (mapping, target) => {
    setOpenAdvancedDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
    setShowSettingsDropdown({})
  }

  const handleSaveAdvancedCode = (code) => {
    if (selectedMapping && selectedTarget) {
      const newTargets = [...targets]
      const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)
      if (targetIndex !== -1) {
        const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
          (f) => f.targetfield === selectedMapping.targetfield,
        )
        if (fieldIndex !== -1) {
          newTargets[targetIndex].fieldMappings[fieldIndex]["mappingType"] = "js"
          newTargets[targetIndex].fieldMappings[fieldIndex]["value"] = code
          setTargets(newTargets)
          setTargetData(newTargets)
        }
      }
    }
  }

  const castValueByType = (value, fieldType) => {
    switch (fieldType) {
      case "number":
        return value === "" || value === null ? null : Number(value)
      case "boolean":
        return value === "true" || value === true
      case "array":
        return Array.isArray(value) ? value : [value]
      case "datetime":
      case "date":
        return value === "" ? null : value
      case "string":
      default:
        return value === null ? "" : String(value)
    }
  }

  const handleOverrideValuesDialog = (mapping, target) => {
    setOpenOverrideDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
    setShowSettingsDropdown({})
  }

  const handleSaveOverrideValues = (overrideMappings) => {
    if (selectedMapping && selectedTarget) {
      const newTargets = [...targets]
      const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)
      if (targetIndex !== -1) {
        const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
          (f) => f.targetfield === selectedMapping.targetfield,
        )
        if (fieldIndex !== -1) {
          const fieldType = newTargets[targetIndex].fieldMappings[fieldIndex].type
          // Cast override mappings based on field type
          const castedOverrideMappings = overrideMappings.map((mapping) => ({
            ...mapping,
            targetvalue: castValueByType(mapping.targetvalue, fieldType),
          }))
          newTargets[targetIndex].fieldMappings[fieldIndex].override = castedOverrideMappings
          setTargetsWithDuplicateRemoval(newTargets)
          setTargetDataWithDuplicateRemoval(newTargets)
        }
      }
    }
  }
  // const handleSaveOverrideValues = (overrideMappings) => {
  //   if (selectedMapping && selectedTarget) {
  //     const newTargets = [...targets]
  //     const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)

  //     if (targetIndex !== -1) {
  //       const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
  //         (f) => f.targetfield === selectedMapping.targetfield,
  //       )

  //       if (fieldIndex !== -1) {
  //         newTargets[targetIndex].fieldMappings[fieldIndex].override = overrideMappings
  //         setTargets(newTargets)
  //         setTargetData(newTargets)
  //       }
  //     }
  //   }
  // }

  const handleCombineFieldsDialog = (mapping, target) => {
    setOpenCombineFieldsDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
    setShowSettingsDropdown({})
  }

  const handleSaveCombinedFields = (combinedFields) => {
    if (selectedMapping && selectedTarget && combinedFields.length >= 2) {
      const newTargets = [...targets]
      const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)
      if (targetIndex !== -1) {
        const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
          (f) => f.targetfield === selectedMapping.targetfield,
        )
        if (fieldIndex !== -1) {
          const fieldType = newTargets[targetIndex].fieldMappings[fieldIndex].type
          // Cast combined fields based on field type
          const castedCombinedFields = combinedFields.map((field) => {
            if (typeof field === "object" && field.value !== undefined) {
              return {
                ...field,
                value: castValueByType(field.value, fieldType),
              }
            }
            return castValueByType(field, fieldType)
          })
          newTargets[targetIndex].fieldMappings[fieldIndex].combinedFields = castedCombinedFields
          setTargetsWithDuplicateRemoval(newTargets)
          setTargetDataWithDuplicateRemoval(newTargets)
        }
      }
    }
  }
  // const handleSaveCombinedFields = (combinedFields) => {
  //   if (selectedMapping && selectedTarget && combinedFields.length >= 2) {
  //     const newTargets = [...targets]
  //     const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)

  //     if (targetIndex !== -1) {
  //       const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
  //         (f) => f.targetfield === selectedMapping.targetfield,
  //       )

  //       if (fieldIndex !== -1) {
  //         newTargets[targetIndex].fieldMappings[fieldIndex].combinedFields = combinedFields
  //         setTargets(newTargets)
  //         setTargetData(newTargets)
  //       }
  //     }
  //   }
  // }


  const flattenMappings = (input) => {
    const result = []
    input.forEach((mapping) => {
      // For subcategory and item_category, preserve the actual value/name instead of just ID
      let targetValue
      if (mapping.targetvalue && typeof mapping.targetvalue === "object") {
        // Use value or label if available, otherwise fall back to id
        targetValue =
          mapping.targetvalue.value || mapping.targetvalue.label || mapping.targetvalue.id || mapping.targetvalue
      } else {
        targetValue = mapping.targetvalue
      }
      mapping.sourcevalue.forEach((source) => {
        let sourceVal
        if (typeof source === "object" && source !== null) {
          // If value is null or "", use that directly
          if ("value" in source) {
            sourceVal = source.value
          } else if ("id" in source) {
            sourceVal = source.id
          } else {
            sourceVal = null
          }
        } else {
          sourceVal = source
        }
        result.push({
          sourcevalue: sourceVal,
          targetvalue: targetValue,
        })
      })
    })
    return result
  }

  const handleValueMappingSave = (mapping, attribute, name) => {
    //console.log("Value mapping save:", mapping, attribute.targetfield)
    const updatedTargets = JSON.parse(JSON.stringify(targets))
    if (attribute.mappingTarget === "Agents" || attribute.mappingTarget === "Groups") {
      // name = "Tickets"
      name =
        targets.find((t) => t.name.toLowerCase() === "tickets")?.name ||
        targets.find((t) => t.name.toLowerCase() === "conversations")?.name ||
        "Tickets"
    }
    const targetIndex = updatedTargets.findIndex((t) => t.name === name)
    if (targetIndex !== -1) {
      const fieldIndex = updatedTargets[targetIndex].fieldMappings.findIndex(
        (field) => field.targetfield === attribute.targetfield,
      )
      if (fieldIndex !== -1) {
        if (!updatedTargets[targetIndex].fieldMappings[fieldIndex].mappings) {
          updatedTargets[targetIndex].fieldMappings[fieldIndex].mappings = []
        }
        updatedTargets[targetIndex].fieldMappings[fieldIndex].mappings = mapping
        // Improved validation: check both source values and target values
        const hasValidMappings = mapping.some(
          (m) =>
            Array.isArray(m.sourcevalue) &&
            m.sourcevalue.some((val) => val !== "" && val !== null && val !== "null") &&
            m.targetvalue &&
            m.targetvalue.id !== undefined &&
            m.targetvalue.id !== null,
        )
        if (updatedTargets[targetIndex].fieldMappings[fieldIndex].mappings) {
          updatedTargets[targetIndex].fieldMappings[fieldIndex].override = flattenMappings(
            updatedTargets[targetIndex].fieldMappings[fieldIndex].mappings,
          )
        }
        // Enhanced status determination
        const field = updatedTargets[targetIndex].fieldMappings[fieldIndex]
        const targetObj = updatedTargets[targetIndex]
        // Use the showAlertIcon logic to determine status consistently
        const hasError = showAlertIcon(field, targetObj)
        field.status = hasError ? "error" : "success"

        const target = updatedTargets[targetIndex]
        if (target.name === "Tickets" && attribute.targetfield === "category") {
          const subcategoryMappings = []
          const itemcategoryMappings = []
          for (const item of mapping) {
            if (item.subCategories && item.subCategories.length > 0) {
              for (const sub of item.subCategories) {
                if (!Array.isArray(sub.sourcevalue)) {
                  sub.sourcevalue = sub.sourcevalue ? [sub.sourcevalue] : []
                }
                subcategoryMappings.push(sub)
                if (sub.items && sub.items.length > 0) {
                  for (const itm of sub.items) {
                    if (!Array.isArray(itm.sourcevalue)) {
                      itm.sourcevalue = itm.sourcevalue ? [itm.sourcevalue] : []
                    }
                    itemcategoryMappings.push(itm)
                  }
                }
              }
            }
          }
          for (const field of target.fieldMappings) {
            if (field.targetfield === "sub_category" && subcategoryMappings.length > 0) {
              field.mappings = subcategoryMappings
              const hasValidSubcategoryMappings = subcategoryMappings.some(
                (m) =>
                  Array.isArray(m.sourcevalue) &&
                  m.sourcevalue.some((val) => val !== "" && val !== null && val !== "null") &&
                  m.targetvalue &&
                  m.targetvalue.id !== undefined &&
                  m.targetvalue.id !== null,
              )
              const subHasError = showAlertIcon(field, target)
              field.status = subHasError ? "error" : "success"
            }
            if (field.targetfield === "item_category" && itemcategoryMappings.length > 0) {
              field.mappings = itemcategoryMappings
              const hasValidItemMappings = itemcategoryMappings.some(
                (m) =>
                  Array.isArray(m.sourcevalue) &&
                  m.sourcevalue.some((val) => val !== "" && val !== null && val !== "null") &&
                  m.targetvalue &&
                  m.targetvalue.id !== undefined &&
                  m.targetvalue.id !== null,
              )
              const itemHasError = showAlertIcon(field, target)
              field.status = itemHasError ? "error" : "success"
            }
          }
        }
        setTargets(updatedTargets)
        setTargetData([...updatedTargets])
      }
    }
  }

  const handleDefaultMappingDialog = (mapping, selectedTarget) => {
    // Ensure workspace_id field has the latest workspace_id value from global target
    let updatedMapping = { ...mapping };
    if (mapping.targetfield === "workspace_id" && target?.formData?.workspace_id) {
      updatedMapping.default = target.formData.workspace_id;
    }

    setOpenDefaultDialog(true)
    setSelectedMapping(updatedMapping)
    setSelectedTarget(selectedTarget)
    setShowSettingsDropdown({})
  }
  // const handleSaveDefaultValue = (defaultValue) => {
  //   if (selectedMapping && selectedTarget) {
  //     const newTargets = [...targets]
  //     const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)

  //     if (targetIndex !== -1) {
  //       const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
  //         (f) => f.targetfield === selectedMapping.targetfield,
  //       )

  //       if (fieldIndex !== -1) {
  //         newTargets[targetIndex].fieldMappings[fieldIndex].default = defaultValue
  //         if (newTargets[targetIndex].fieldMappings[fieldIndex].value !== undefined) {
  //           newTargets[targetIndex].fieldMappings[fieldIndex].value = defaultValue
  //         } else {
  //           newTargets[targetIndex].fieldMappings[fieldIndex]["value"] = defaultValue
  //         }
  //         setTargets(newTargets)
  //         setTargetData(newTargets)
  //       }
  //     }
  //   }
  // }


  const handleSaveDefaultValue = (defaultValue) => {
    if (selectedMapping && selectedTarget) {
      const newTargets = [...targets]
      const targetIndex = newTargets.findIndex((t) => t.name === selectedTarget.name)
      if (targetIndex !== -1) {
        const fieldIndex = newTargets[targetIndex].fieldMappings.findIndex(
          (f) => f.targetfield === selectedMapping.targetfield,
        )
        if (fieldIndex !== -1) {
          const fieldType = newTargets[targetIndex].fieldMappings[fieldIndex].type
          // Cast defaultValue based on field type using the existing method
          const castedDefaultValue = castValueByType(defaultValue, fieldType)
          newTargets[targetIndex].fieldMappings[fieldIndex].default = castedDefaultValue
          if (newTargets[targetIndex].fieldMappings[fieldIndex].value !== undefined) {
            newTargets[targetIndex].fieldMappings[fieldIndex].value = castedDefaultValue
          } else {
            newTargets[targetIndex].fieldMappings[fieldIndex]["value"] = castedDefaultValue
          }
          setTargets(newTargets)
          setTargetData(newTargets)
        }
      }
    }
  }

  useEffect(() => {
    function handleClickOutside(event) {
      if (!Object.values(settingsDropdownRef.current || {}).some((ref) => ref?.contains(event.target))) {
        setShowSettingsDropdown({})
      }
      if (filterDropdownRef.current && !filterDropdownRef.current.contains(event.target)) {
        setShowFilterDropdown(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Function to check if a tab should be enabled based on step completion
  const isTabEnabled = useCallback((tabIndex, tabName) => {
    // Default sequential enabling (index <= value) for basic navigation
    if (tabIndex <= value) {
      return true;
    }

    // Check if completedSteps is available
    if (!completedSteps) {
      return tabIndex <= value;
    }

    // Special case: For tickets, conversations, messages tabs - require step 4 AND step 2 completion
    const restrictedTabNames = ['tickets', 'conversations', 'messages', 'conversation'];
    const normalizedTabName = tabName.toLowerCase();

    console.log(`Checking tab "${tabName}" (normalized: "${normalizedTabName}") at index ${tabIndex}`);
    console.log('Step 2 completed:', completedSteps[2], 'Step 4 completed:', completedSteps[4]);

    if (restrictedTabNames.includes(normalizedTabName)) {
      // Require both step 2 and step 4 to be completed
      const shouldEnable = completedSteps[2] && completedSteps[4];
      console.log(`Tab "${tabName}" should be enabled:`, shouldEnable);
      return shouldEnable;
    }

    // For other tabs, use default sequential logic
    return tabIndex <= value;
  }, [value, completedSteps]);

  const handleChange = useCallback((event, newValue) => {
    // Check if the tab is enabled before allowing the change
    const targetItem = targetData.filter((item) => item.isMappingTarget === "false")[newValue];
    if (targetItem && !isTabEnabled(newValue, targetItem.name)) {
      return; // Prevent tab change if not enabled
    }

    setValue(newValue)
    setCurrentPage(1)
    if (targetData[newValue]?.fieldMappings) {
      setCurrentTabData(targetData[newValue].fieldMappings)
    }
  }, [targetData, isTabEnabled]);

  // Ensure component refreshes when targetData or fieldMappings change
  // useEffect(() => {
  //   if (targetData[value]?.fieldMappings) {
  //     setCurrentTabData(targetData[value].fieldMappings)
  //   }
  // }, [targetData, value]);

  const initialTargets = useMemo(() => {
    if (!targetExeRes || !sourceResAtt) return []
    const mainTarget = selectedEntityData?.mainTarget || []
    const dependentTargets = JSON.parse(JSON.stringify(selectedEntityData?.dependentTargets || []))
    return [...mainTarget, ...dependentTargets]
  }, [targetExeRes, sourceResAtt, selectedEntityData])

  // One-time guarded initialization to prevent overwriting freshly computed targets
  const hasInitializedTargetsRef = useRef(false)
  useEffect(() => {
    if (hasInitializedTargetsRef.current) return

    if (dataMappingData?.targets && dataMappingData.targets.length > 0) {
      console.log("[Init] Using saved mapping targets (one-time):", dataMappingData.targets.length)
      const cleanedTargets = removeDuplicateTargetFields(dataMappingData.targets)
      setTargets(cleanedTargets)
      setIsInitializedFromSavedData(true)
      hasInitializedTargetsRef.current = true
      return
    }

    if (initialTargets.length > 0) {
      console.log("[Init] Using fresh initial targets (one-time):", initialTargets.length)
      const cleanedTargets = removeDuplicateTargetFields(initialTargets)
      setTargets(cleanedTargets)
      setIsInitializedFromSavedData(false)
      hasInitializedTargetsRef.current = true
    }
  }, [initialTargets, dataMappingData?.targets, removeDuplicateTargetFields])

  // Separate effect just to keep filteredAttributes up to date (not resetting targets)
  useEffect(() => {
    if (!source) return
    if (source.type === "api") {
      setFilteredAttributes(sourceResAtt.filter((val) => val !== "").sort())
    } else if (uniqueSourceValues) {
      try {
        const sourceDropValueArray = Object.values(uniqueSourceValues)
        setFilteredAttributes(sourceDropValueArray[value]?.map((val) => val.column) || [])
      } catch (e) {
        console.warn("Unable to derive filtered attributes from uniqueSourceValues", e)
      }
    } else {
      setFilteredAttributes([])
    }
  }, [source?.type, sourceResAtt, uniqueSourceValues, value])

  useEffect(() => {
    if (dataMappingData.isNotes) {
      setIsNotes(dataMappingData.isNotes)
    }
  }, [dataMappingData])

  // Restore saved current tab when component loads
  useEffect(() => {
    if (dataMappingData?.currentTab && targetData?.length > 0) {
      // Find the tab index by name
      const savedTabName = dataMappingData.currentTab
      const savedTabIndex = targetData.findIndex(target => target.name === savedTabName)

      // Set the tab if found, otherwise stay at current tab
      if (savedTabIndex !== -1) {
        setValue(savedTabIndex)
        // Ensure migration state reflects the restored tab name (in case it's missing/ stale)
        if (migrationState.dataMappingData?.currentTab !== savedTabName) {
          setMigrationState(prev => ({
            ...prev,
            dataMappingData: {
              ...prev.dataMappingData,
              currentTab: savedTabName,
            },
          }))
        }
      }
    }
  }, [dataMappingData, targetData])

  // Keep currentTab name in sync whenever the active tab index changes
  useEffect(() => {
    const activeName = targetData?.[value]?.name
    if (activeName && migrationState.dataMappingData?.currentTab !== activeName) {
      setMigrationState(prev => ({
        ...prev,
        dataMappingData: {
          ...prev.dataMappingData,
          currentTab: activeName,
        },
      }))
    }
  }, [value, targetData, migrationState.dataMappingData?.currentTab, setMigrationState])

  useEffect(() => {
    setTargetData([...targets])
  }, [targets])

  const isRequired = useCallback(
    (targetfield) => {
      if (targetExeRes && targetExeRes.ticketFields) {
        return targetExeRes.ticketFields.some((field) => field.name === targetfield && field.required_for_agents)
      }
      return false
    },
    [targetExeRes],
  )

  const handleValueMappingDialog = (mapping, target) => {
    setOpenDialog(true)
    setSelectedMapping(mapping)
    setSelectedTarget(target)
  }

  const getStatusIcon = useCallback(
    (mapping) => {
      if (!targetData[value]) {
        return null
      }
      const status = showAlertIcon(mapping, targetData[value]) === false ? "success" : "error"
      if (status === "success") {
        return (
          <div className={globalStyles.statusIconSuccess}>
            <span className={globalStyles.iconText}>✓</span>
          </div>
        )
      } else if (status === "error") {
        return (
          <div className={globalStyles.statusIconError}>
            <span className={globalStyles.iconText}>×</span>
          </div>
        )
      }
      return null
    },
    [targetData, value],
  )

  const getConfidenceIndicator = useCallback((mapping) => {
    // Only show confidence if sourcefield is set
    if (!mapping.sourcefield) return null
    const confidence = mapping.matchConfidence || 0
    const method = mapping.matchMethod || "exact"
    if (confidence === 0 || method === "exact") return null
    let color = "#e1683b"
    let label = "High-confidence automap"
    if (confidence < 0.6) {
      color = "#A43E18"
      label = "Low-confidence automap"
    } else if (confidence < 0.8) {
      color = "rgb(227 137 103)"
      label = "Medium-confidence automap"
    }
    return (
      <span
        style={{
          fontSize: "13px", // Increased font size
          padding: "2px 8px",
          borderRadius: "3px",
          backgroundColor: color,
          color: "white",
          marginLeft: "8px",
          fontWeight: "bold",
          fontFamily: "Inter, sans-serif",
          fontSize: "10px",
          letterSpacing: "0.2px",
          display: "inline-block",
          verticalAlign: "middle",
        }}
        title={`Match confidence: ${Math.round(confidence * 100)}% (${method})`}
      >
        {label}
      </span>
    )
  }, [])

  // Helper function to check for exact field name matches (case-insensitive)
  const isExactFieldMatch = useCallback((sourceField, targetField) => {
    if (!sourceField || !targetField) return false
    return sourceField.toLowerCase().trim() === targetField.toLowerCase().trim()
  }, [])

  const getEnhancedStatusIcon = useCallback(
    (mapping) => {
      if (!targetData[value]) {
        return null
      }
      const status = showAlertIcon(mapping, targetData[value]) === false ? "success" : "error"
      const confidenceIndicator = getConfidenceIndicator(mapping)
      // Create enhanced tooltip content based on status and auto-mapping info
      let tooltipContent = ""
      if (status === "success") {
        if (mapping.sourcefield) {
          const isExact = isExactFieldMatch(mapping.sourcefield, mapping.targetfield)
          if (isExact) {
            tooltipContent = `✓ Exact match: ${mapping.sourcefield} ↔ ${mapping.targetfield}`
          } else {
            tooltipContent = `✓ Successfully mapped: ${mapping.sourcefield} → ${mapping.targetfield}`
          }
          if (mapping.matchConfidence) {
            tooltipContent += `\nConfidence: ${Math.round(mapping.matchConfidence * 100)}%`
          }
          if (mapping.autoMapped) {
            tooltipContent += `\n Auto-mapped`
          }
        } else if (mapping.default) {
          tooltipContent = `✓ Default value set: "${mapping.default}"`
        } else if (mapping.combinedFields?.length) {
          tooltipContent = `✓ Combined fields: ${mapping.combinedFields.join(", ")}`
        } else {
          tooltipContent = "✓ Field mapped successfully"
        }
      } else {
        if (mapping.required && !mapping.sourcefield && !mapping.default && !mapping.combinedFields?.length) {
          tooltipContent = "✗ Required field needs mapping"
        } else if (mapping.mappingEnabled && (!mapping.mappings || mapping.mappings.length === 0)) {
          tooltipContent = "✗ Value mapping required"
        } else {
          tooltipContent = "✗ Field has mapping issues"
        }
      }

      // Add skip information to tooltip
      if (mapping.skip) {
        if (mapping.matchMethod === "no_match") {
          tooltipContent += "\n Auto-skipped (no match found)"
        } else if (mapping.matchConfidence && mapping.matchConfidence < 0.6) {
          tooltipContent += "\n Auto-skipped (low confidence match)"
        } else {
          tooltipContent += "\n Skipped"
        }
      }
      if (status === "success") {
        return (
          <div
            style={{ display: "flex", alignItems: "center", position: "relative" }}
            onMouseEnter={() => setShowTooltip(mapping.targetfield)}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <div className={globalStyles.statusIconSuccess}>
              <span className={globalStyles.iconText}>✓</span>
            </div>
            {confidenceIndicator}
            {showTooltip === mapping.targetfield && (
              <div
                style={{
                  position: "absolute",
                  top: "-8px",
                  left: "100%",
                  marginLeft: "8px",
                  backgroundColor: "#170903",
                  color: "#F8F8F7",
                  padding: "8px 12px",
                  borderRadius: "6px",
                  fontSize: "12px",
                  fontFamily: "Inter, sans-serif",
                  fontWeight: "400",
                  lineHeight: "1.4",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                  zIndex: 1000,
                  whiteSpace: "pre-line",
                  minWidth: "200px",
                  maxWidth: "300px",
                }}
              >
                {tooltipContent}
                <div
                  style={{
                    position: "absolute",
                    left: "-6px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    width: "0",
                    height: "0",
                    borderTop: "6px solid transparent",
                    borderBottom: "6px solid transparent",
                    borderRight: "6px solid #170903",
                  }}
                />
              </div>
            )}
          </div>
        )
      } else if (status === "error") {
        return (
          <div
            style={{ display: "flex", alignItems: "center", position: "relative" }}
            onMouseEnter={() => setShowTooltip(mapping.targetfield)}
            onMouseLeave={() => setShowTooltip(null)}
          >
            <div className={globalStyles.statusIconError}>
              <span className={globalStyles.iconText}>×</span>
            </div>
            {confidenceIndicator}
            {showTooltip === mapping.targetfield && (
              <div
                style={{
                  position: "absolute",
                  top: "-8px",
                  left: "100%",
                  marginLeft: "8px",
                  backgroundColor: "#170903",
                  color: "#F8F8F7",
                  padding: "8px 12px",
                  borderRadius: "6px",
                  fontSize: "12px",
                  fontFamily: "Inter, sans-serif",
                  fontWeight: "400",
                  lineHeight: "1.4",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                  zIndex: 1000,
                  whiteSpace: "pre-line",
                  minWidth: "200px",
                  maxWidth: "300px",
                }}
              >
                {tooltipContent}
                <div
                  style={{
                    position: "absolute",
                    left: "-6px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    width: "0",
                    height: "0",
                    borderTop: "6px solid transparent",
                    borderBottom: "6px solid transparent",
                    borderRight: "6px solid #170903",
                  }}
                />
              </div>
            )}
          </div>
        )
      }
      return null
    },
    [targetData, value, getConfidenceIndicator, showTooltip],
  )
  // useEffect(() => {
  //   const deepCopyTargets = JSON.parse(JSON.stringify(targets))
  //   setTargetData(deepCopyTargets)
  // }, [targets])

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1)
  }

  useEffect(() => {
    if (targetData[value]?.fieldMappings) {
      setCurrentTabData(targetData[value].fieldMappings)
    } else {
      setCurrentTabData([])
    }
  }, [value, targetData])

  const filterData = useCallback(
    (data) => {
      if (selectedFilter === "See all") {
        return data
      }
      return data.filter((item) => {
        const target = targetData[value]
        switch (selectedFilter) {
          case "Mapped successfully":
            return item.sourcefield && !item.skip && !showAlertIcon(item, target)
          case "Errors in mapping":
            return showAlertIcon(item, target)
          case "Data mismatch":
            return false
          case "Unmapped fields":
            return !item.sourcefield && !item.skip && !item.combinedFields?.length
          case "Skipped fields":
            return item.skip
          case "Required fields":
            return item.required
          default:
            return true
        }
      })
    },
    [selectedFilter, targetData, value],
  )

  const filteredTabData = useMemo(() => {
    let data = currentTabData
    if (searchTerm && data.length > 0) {
      data = data.filter(
        (item) =>
          item.targetfield.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.sourcefield && item.sourcefield.toLowerCase().includes(searchTerm.toLowerCase())),
      )
    }
    return filterData(data)
  }, [currentTabData, searchTerm, filterData])

  //   const filteredTabData = useMemo(() => {
  //   let data = currentTabData
  //   if (searchTerm && data.length > 0) {
  //     data = data.filter(
  //       (item) =>
  //         item.targetfield.toLowerCase().includes(searchTerm.toLowerCase()) ||
  //         (item.sourcefield && item.sourcefield.toLowerCase().includes(searchTerm.toLowerCase())),
  //     )
  //   }
  //   if (data.length !== 0 && !isDataReceived.current) {
  //     isDataReceived.current = true
  //     return filterData(data)
  //   }
  //   return [...data]
  // }, [currentTabData, searchTerm, filterData])
  const totalPages = Math.ceil(filteredTabData.length / itemsPerPage)
  const paginatedData = useMemo(() => {
    const data = filteredTabData.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
    return data
  }, [filteredTabData, currentPage, itemsPerPage])

  // Update toggle state based on current paginated data
  // This useEffect serves as a backup to ensure toggle state is always accurate
  useEffect(() => {
    if (paginatedData && paginatedData.length > 0) {
      // Find all optional fields in the current page
      const optionalFields = paginatedData.filter(field => !field.required)
      
      if (optionalFields.length === 0) {
        // If there are no optional fields, set toggle to false
        setIsSkipAllOptional(false)
      } else {
        // Check if ALL optional fields are skipped
        const allOptionalSkipped = optionalFields.every(field => field.skip === true)
        setIsSkipAllOptional(allOptionalSkipped)
      }
    } else {
      // If no data, set toggle to false
      setIsSkipAllOptional(false)
    }
  }, [paginatedData]) // Removed targetData and targets to prevent conflicts

  const handlePageChange = useCallback(
    (page) => {
      if (page >= 1 && page <= totalPages) {
        setCurrentPage(page)
      }
    },
    [totalPages],
  )

  // FIXED: handleCheckboxChange function - moved after paginatedData definition
  const handleCheckboxChange = useCallback(
    (tabIndex, displayIndex) => {
      // Add bounds checking
      if (!paginatedData || displayIndex >= paginatedData.length || displayIndex < 0) {
        console.error("Invalid display index or paginatedData not available")
        return
      }
      // Get the actual mapping from the filtered/paginated data
      const actualMapping = paginatedData[displayIndex]
      if (!actualMapping || !actualMapping.targetfield) {
        console.error("Invalid mapping data")
        return
      }
      // Find the original index in the full dataset
      const originalIndex = targetData[tabIndex].fieldMappings.findIndex(
        (mapping) => mapping.targetfield === actualMapping.targetfield,
      )
      if (originalIndex === -1) {
        console.error("Could not find original mapping index")
        return
      }
      const newData = [...targetData]
      const newSkipValue = !newData[tabIndex].fieldMappings[originalIndex].skip
      if (newData[tabIndex].fieldMappings[originalIndex].required && newSkipValue) {
        toast.error("Required fields cannot be skipped.", {
          position: "top-right",
        })
        return
      }
      newData[tabIndex].fieldMappings[originalIndex].skip = newSkipValue
      if (newSkipValue) {
        newData[tabIndex].fieldMappings[originalIndex].status = "skipped"
      } else {
        const field = newData[tabIndex].fieldMappings[originalIndex]
        const hasValidMapping =
          field.sourcefield ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)
        field.status = hasValidMapping || !field.required ? "success" : "error"
      }
      setTargetData(newData)
      const newTargets = [...targets]
      newTargets[tabIndex].fieldMappings[originalIndex].skip = newSkipValue
      if (newSkipValue) {
        newTargets[tabIndex].fieldMappings[originalIndex].status = "skipped"
      } else {
        const field = newTargets[tabIndex].fieldMappings[originalIndex]
        const hasValidMapping =
          field.sourcefield ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)
        field.status = hasValidMapping || !field.required ? "success" : "error"
      }
      setTargets(newTargets)
    },
    [targetData, targets, paginatedData],
  )

  // FIXED: handleRequiredFieldChange function - moved after paginatedData definition
  const handleRequiredFieldChange = useCallback(
    (tabIndex, displayIndex, newRequiredValue) => {
      // Add bounds checking
      if (!paginatedData || displayIndex >= paginatedData.length || displayIndex < 0) {
        console.error("Invalid display index or paginatedData not available")
        return
      }
      // Get the actual mapping from the filtered/paginated data
      const actualMapping = paginatedData[displayIndex]
      if (!actualMapping || !actualMapping.targetfield) {
        console.error("Invalid mapping data")
        return
      }
      // Find the original index in the full dataset
      const originalIndex = targetData[tabIndex].fieldMappings.findIndex(
        (mapping) => mapping.targetfield === actualMapping.targetfield,
      )
      if (originalIndex === -1) {
        console.error("Could not find original mapping index")
        return
      }
      const newData = [...targetData]
      newData[tabIndex].fieldMappings[originalIndex].required = newRequiredValue
      if (newRequiredValue) {
        const field = newData[tabIndex].fieldMappings[originalIndex]
        const hasValidMapping =
          field.sourcefield ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)
        if (field.skip && newRequiredValue) {
          field.skip = false
        }
        field.status = hasValidMapping ? "success" : "error"
      }
      setTargetData(newData)
      const newTargets = [...targets]
      newTargets[tabIndex].fieldMappings[originalIndex].required = newRequiredValue
      if (newRequiredValue) {
        const field = newTargets[tabIndex].fieldMappings[originalIndex]
        if (field.skip) {
          field.skip = false
        }
        const hasValidMapping =
          field.sourcefield ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)
        field.status = hasValidMapping ? "success" : "error"
      }
      setTargets(newTargets)
    },
    [targetData, targets, paginatedData],
  )

  const handleSourceFieldChange = useCallback(
    (tabIndex, displayIndex, newValue) => {
      // Add bounds checking
      if (!paginatedData || displayIndex >= paginatedData.length || displayIndex < 0) {
        console.error("Invalid display index or paginatedData not available")
        return
      }
      // Get the actual mapping from the filtered/paginated data
      const actualMapping = paginatedData[displayIndex]
      if (!actualMapping || !actualMapping.targetfield) {
        console.error("Invalid mapping data")
        return
      }
      // Find the original index in the full dataset
      const originalIndex = targetData[tabIndex].fieldMappings.findIndex(
        (mapping) => mapping.targetfield === actualMapping.targetfield,
      )
      if (originalIndex !== -1) {
        const newData = [...targetData]
        const newTargets = [...targets]

        // Handle "None" selection by setting to null or empty string
        const finalValue = newValue === "None" ? null : newValue
        newData[tabIndex].fieldMappings[originalIndex].sourcefield = finalValue
        newTargets[tabIndex].fieldMappings[originalIndex].sourcefield = finalValue

        // ENHANCED: Implement automatic skip/unskip logic based on field matching
        const field = newData[tabIndex].fieldMappings[originalIndex]

        if (finalValue === null || finalValue === "") {
          // ENHANCED: No source field selected - automatically skip non-required fields
          if (!field.required) {
            field.skip = true
            field.status = "skipped"
            newTargets[tabIndex].fieldMappings[originalIndex].skip = true
            newTargets[tabIndex].fieldMappings[originalIndex].status = "skipped"
          } else {
            // Required fields cannot be skipped
            field.status = "error"
            newTargets[tabIndex].fieldMappings[originalIndex].status = "error"
          }
        } else {
          // ENHANCED: Source field selected - check if it's an exact match
          const isExactMatch = field.targetfield.toLowerCase().trim() === finalValue.toLowerCase().trim()

          if (isExactMatch) {
            // ENHANCED: Exact match found - automatically unskip
            field.skip = false
            field.status = "success"
            field.matchMethod = "exact"
            field.matchConfidence = 1.0

            newTargets[tabIndex].fieldMappings[originalIndex].skip = false
            newTargets[tabIndex].fieldMappings[originalIndex].status = "success"
            newTargets[tabIndex].fieldMappings[originalIndex].matchMethod = "exact"
            newTargets[tabIndex].fieldMappings[originalIndex].matchConfidence = 1.0
          } else {
            // Non-exact match - just update status, preserve existing skip state unless user manually set it
            field.status = "success"
            newTargets[tabIndex].fieldMappings[originalIndex].status = "success"

            // Only auto-unskip if the field was previously auto-skipped due to no match
            if (field.skip && field.matchMethod === "no_match") {
              field.skip = false
              newTargets[tabIndex].fieldMappings[originalIndex].skip = false
            }
          }
        }

        setTargetData(newData)
        setTargets(newTargets)
      }
    },
    [targetData, targets, paginatedData],
  )

  const handleShowPage = useCallback((newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)
  }, [])

  const handleFilterSelect = (filter) => {
    setSelectedFilter(filter)
    setShowFilterDropdown(false)
    setCurrentPage(1)
  }

  const goToNextStep = async () => {
    if (isSaving) {
      return
    }
    if (getErrorFieldsCount(targets[value]) > 0) {
      toast.error("Resolve the issues in order to move to the next step.", {
        position: "top-right",
      })
      return
    }
    try {
      // Set saving state to show loading indicator
      setIsSaving(true)
      setSavingMessage("Saving mapping configuration...")
      // Save the current state
      await saveMigration({ targets })
      setSavingMessage("Updating migration state...")
      const obj = {
        targets: targets,
        sourceExeRes: sourceExeRes,
        targetExeRes: targetExeRes,
        sourceMapRes: sourceMapRes,
        sourceResAtt: sourceResAtt,
        isNotes: isNotes,
      }
      setMigrationState((prevState) => ({
        ...prevState,
        dataMappingData: obj,
      }))
      const newValue = value + 1
      if (newValue < targets.length) {
        setSavingMessage("Moving to next entity...")
        toast.success("Mapping saved successfully!", {
          position: "top-right",
          autoClose: 2000,
        })
        setValue(newValue)
        setCurrentPage(1)
        if (targetData[newValue]?.fieldMappings) {
          setCurrentTabData(targetData[newValue].fieldMappings)
        }
      } else if (newValue === targets.length) {
        setSavingMessage("Finalizing mapping process...")
        setSelectedTab("5")
        const activityPayload = {
          email: email,
          activity: "Field Mapping Completed",
          // timestamp: Date.now(),
        }
        await postActivity(activityPayload)
        toast.success("Field mapping completed successfully!", {
          position: "top-right",
        })
      }
    } catch (error) {
      console.error("Error saving migration plan:", error)
      toast.error("Failed to save mapping. Please try again.", {
        position: "top-right",
      })
    } finally {
      setIsSaving(false)
      setSavingMessage("Saving...")
    }
  }

  const saveMigration = useCallback(async (obj) => {
    try {
      if (!obj || !obj.targets || obj.targets.length === 0) {
        throw new Error("Invalid mapping data. Please check your configuration.")
      }
      // Persist a flag indicating Smart Auto-Map has been executed (replaces prior localStorage persistence)
      // We store only the identifier (planId || templateName || 'default') so backend/other components can infer uniqueness
      const smartAutoMapRunKey = planId || templateName || 'default'
      const payload = {
        plan_name: templateName,
        migration_objects: [],
        updatedAt: Date.now(),
        email_id: email,
        additional_details: {
          sourceObjData: source,
          targetData: target,
          dataTypeData: dataTypeData,
          dataMappingData: obj,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
          sourceMapRes: sourceMapRes,
          sourceResAtt: sourceResAtt,
          currentTab: targetData[value]?.name || null, // Save current tab name instead of index
          smartAutoMapRunKey, // new persistence replacing localStorage smartAutoMapRun_*
        },
      }
      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
      }
      const res = await saveMigrationPlan(payload)
      if (res?.message === "plan created successfully" && res?.response?.id) {
        setPlanId(res.response.id)
        return true
      } else if (res?.message === "plan updated successfully") {
        return true
      } else {
        console.warn("Unexpected response from saveMigrationPlan:", res)
        return true
      }
    } catch (error) {
      console.error("Error saving migration plan:", error)
      toast.error(
        error?.response?.data?.message || error?.message || "Failed to save migration plan. Please try again.",
        { position: "top-right" },
      )
      throw error
    }
  }, [planId, templateName, email, source, target, dataTypeData, migrationState, sourceExeRes, targetExeRes, sourceMapRes, sourceResAtt, targetData, value])

  // Handle Skip All toggle
  const handleSkipAllChange = useCallback(
    async (event) => {
      const skipAll = event.target.checked
      
      const currentTarget = targetData[value]
      if (!currentTarget || !currentTarget.fieldMappings || !paginatedData || paginatedData.length === 0) return
      
      const newData = [...targetData]
      const newTargets = [...targets]
      let affectedCount = 0
      
      // Update only the optional fields that are currently visible in the paginated table
      paginatedData.forEach((paginatedMapping) => {
        if (!paginatedMapping.required) {
          // Find the original index in the full dataset
          const originalIndex = currentTarget.fieldMappings.findIndex(
            (mapping) => mapping.targetfield === paginatedMapping.targetfield
          )
          
          if (originalIndex !== -1) {
            newData[value].fieldMappings[originalIndex].skip = skipAll
            newTargets[value].fieldMappings[originalIndex].skip = skipAll
            
            if (skipAll) {
              newData[value].fieldMappings[originalIndex].status = "skipped"
              newTargets[value].fieldMappings[originalIndex].status = "skipped"
            } else {
              const field = newData[value].fieldMappings[originalIndex]
              const hasValidMapping =
                field.sourcefield ||
                (field.default !== undefined && field.default !== "") ||
                (field.combinedFields && field.combinedFields.length > 0)
              field.status = hasValidMapping || !field.required ? "success" : "error"
              newTargets[value].fieldMappings[originalIndex].status = field.status
            }
            affectedCount++
          }
        }
      })
      
      setTargetData(newData)
      setTargets(newTargets)
      
      // Save changes to database
      try {
        await saveMigration({ targets: newTargets })
        const obj = {
          targets: newTargets,
          sourceExeRes: sourceExeRes,
          targetExeRes: targetExeRes,
          sourceMapRes: sourceMapRes,
          sourceResAtt: sourceResAtt,
          isNotes: isNotes,
        }
        setMigrationState((prevState) => ({
          ...prevState,
          dataMappingData: obj,
        }))
      } catch (error) {
        console.error("Error saving skip all changes:", error)
        toast.error("Failed to save changes. Please try again.", {
          position: "top-right",
        })
        return
      }
      
      // Show feedback message with count of affected fields from current page
      if (skipAll) {
        toast.success(`${affectedCount} optional fields on this page have been skipped and saved.`, {
          position: "top-right",
          autoClose: 3000,
          style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
        })
      } else {
        toast.success(`${affectedCount} fields on this page have been unskipped and saved.`, {
          position: "top-right",
          autoClose: 3000,
          style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
        })
      }
      
      // Manually update the toggle state to ensure it reflects the new data immediately
      // This prevents race conditions with the useEffect
      const updatedOptionalFields = paginatedData.filter(field => !field.required)
      if (updatedOptionalFields.length > 0) {
        setIsSkipAllOptional(skipAll)
      } else {
        setIsSkipAllOptional(false)
      }
    },
    [targetData, targets, value, paginatedData, saveMigration, sourceExeRes, targetExeRes, sourceMapRes, sourceResAtt, isNotes, setMigrationState, setIsSkipAllOptional]
  )

  const handleSave = async () => {
    if (isSaving) {
      return
    }
    try {
      setIsSaving(true)
      setSavingMessage("Saving mapping configuration...")
      // Save the current state
      await saveMigration({ targets })
      const obj = {
        targets: targets,
        sourceExeRes: sourceExeRes,
        targetExeRes: targetExeRes,
        sourceMapRes: sourceMapRes,
        sourceResAtt: sourceResAtt,
        isNotes: isNotes,
      }
      setMigrationState((prevState) => ({
        ...prevState,
        dataMappingData: obj,
      }))
      toast.success("Mapping saved successfully!", {
        position: "top-right",
        autoClose: 2000,
      })
    } catch (error) {
      console.error("Error saving mapping:", error)
      toast.error("Failed to save mapping. Please try again.", {
        position: "top-right",
      })
    } finally {
      setIsSaving(false)
      setSavingMessage("Saving...")
    }
  }

  const getAttributesFromResponseAttributes = useCallback((attributeNames, responseAttributes, parent = "") => {
    responseAttributes?.forEach((attr) => {
      const fullPath = parent ? `${parent}.${attr.attributeName}` : attr.attributeName
      attributeNames.push(fullPath)
      if (attr.subType && attr.subType.length > 0) {
        getAttributesFromResponseAttributes(attributeNames, attr.subType, fullPath)
      }
    })
  }, [])

  // Enhanced fuzzy logic matching functions - Optimized and centralized
  const normalizeString = useCallback((str) => {
    return String(str)
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9]/g, "")
  }, [])

  const levenshteinDistance = useCallback((str1, str2) => {
    // Optimized with early termination for performance
    if (str1 === str2) return 0
    if (str1.length === 0) return str2.length
    if (str2.length === 0) return str1.length
    // Early termination if strings are too different
    const maxLen = Math.max(str1.length, str2.length)
    const minLen = Math.min(str1.length, str2.length)
    if (maxLen - minLen > maxLen * 0.7) return maxLen // Return max if too different
    const matrix = Array.from({ length: str1.length + 1 }, (_, i) =>
      Array.from({ length: str2.length + 1 }, (_, j) => (i === 0 ? j : j === 0 ? i : 0)),
    )
    for (let i = 1; i <= str1.length; i++) {
      for (let j = 1; j <= str2.length; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1, // deletion
          matrix[i][j - 1] + 1, // insertion
          matrix[i - 1][j - 1] + cost, // substitution
        )
      }
    }
    return matrix[str1.length][str2.length]
  }, [])

  const jaroWinklerSimilarity = useCallback((str1, str2) => {
    if (str1 === str2) return 1.0
    if (str1.length === 0 || str2.length === 0) return 0.0
    const matchDistance = Math.floor(Math.max(str1.length, str2.length) / 2) - 1
    const str1Matches = new Array(str1.length).fill(false)
    const str2Matches = new Array(str2.length).fill(false)
    let matches = 0
    let transpositions = 0
    // Find matches
    for (let i = 0; i < str1.length; i++) {
      const start = Math.max(0, i - matchDistance)
      const end = Math.min(i + matchDistance + 1, str2.length)
      for (let j = start; j < end; j++) {
        if (str2Matches[j] || str1[i] !== str2[j]) continue
        str1Matches[i] = true
        str2Matches[j] = true
        matches++
        break
      }
    }
    if (matches === 0) return 0.0
    // Find transpositions
    let k = 0
    for (let i = 0; i < str1.length; i++) {
      if (!str1Matches[i]) continue
      while (!str2Matches[k]) k++
      if (str1[i] !== str2[k]) transpositions++
      k++
    }
    const jaro = (matches / str1.length + matches / str2.length + (matches - transpositions / 2) / matches) / 3.0
    // Jaro-Winkler enhancement
    let prefix = 0
    for (let i = 0; i < Math.min(str1.length, str2.length, 4); i++) {
      if (str1[i] === str2[i]) prefix++
      else break
    }
    return jaro + 0.1 * prefix * (1 - jaro)
  }, [])

  // Optimized semantic similarity with caching
  const semanticMappings = useMemo(
    () => ({
      name: ["title", "label", "full_name", "display_name", "firstname", "lastname"],
      email: ["mail", "email_address", "e_mail", "contact_email"],
      phone: ["telephone", "mobile", "contact_number", "phone_number", "tel"],
      address: ["location", "street", "postal_address", "mailing_address"],
      date: ["time", "timestamp", "created_at", "updated_at", "modified_date"],
      status: ["state", "condition", "stage", "phase"],
      priority: ["urgency", "importance", "level", "severity"],
      category: ["type", "classification", "group", "class"],
      description: ["details", "notes", "comments", "summary", "content"],
      id: ["identifier", "key", "reference", "number", "code"],
      user: ["person", "individual", "member", "account", "profile"],
      company: ["organization", "business", "enterprise", "firm", "corp"],
      department: ["division", "team", "unit", "section", "group"],
      role: ["position", "job", "function", "responsibility", "title"],
      country: ["nation", "territory", "region"],
      city: ["town", "municipality", "locality"],
      zip: ["postal_code", "postcode", "zipcode"],
      custom: ["cf", "custom_field", "additional", "extra", "extended"],
    }),
    [],
  )

  const calculateSemanticSimilarity = useCallback(
    (key, fieldName) => {
      const normalizedKey = normalizeString(key)
      const normalizedField = normalizeString(fieldName)
      let maxSimilarity = 0
      // Check direct semantic mappings
      for (const [baseWord, synonyms] of Object.entries(semanticMappings)) {
        const baseWordNormalized = normalizeString(baseWord)
        if (normalizedKey.includes(baseWordNormalized) || normalizedField.includes(baseWordNormalized)) {
          for (const synonym of synonyms) {
            const synonymNormalized = normalizeString(synonym)
            if (normalizedKey.includes(synonymNormalized) || normalizedField.includes(synonymNormalized)) {
              maxSimilarity = Math.max(maxSimilarity, 0.8)
            }
          }
        }
      }
      return maxSimilarity
    },
    [normalizeString, semanticMappings],
  )

  // Enhanced field matching with improved scoring algorithm and exact match detection
  const findBestMatch = useCallback(
    (key, fieldNames, threshold = 0.6) => {
      const normalizedKey = normalizeString(key)
      let bestMatch = { target: "", similarity: 0, confidence: 0, isExactMatch: false }
      if (!key || !fieldNames || fieldNames.length === 0) return bestMatch

      const candidates = fieldNames.filter((fieldName) => {
        const normalizedField = normalizeString(fieldName)
        const lengthRatio = Math.min(normalizedKey.length, normalizedField.length) / Math.max(normalizedKey.length, normalizedField.length)
        return lengthRatio >= 0.3
      })

      for (let i = 0; i < candidates.length; i++) {
        const fieldName = candidates[i]
        const normalizedField = normalizeString(fieldName)
        let totalSimilarity = 0
        let weights = 0

        // 1. Enhanced exact match detection (case-insensitive) - Early return
        const isExactMatch = key.toLowerCase().trim() === fieldName.toLowerCase().trim() ||
          normalizedKey === normalizedField
        if (isExactMatch) {
          bestMatch = { target: fieldName, similarity: 1.0, confidence: 1.0, isExactMatch: true }
          break
        }
        // 2. Substring matching (high priority)
        let substringScore = 0
        if (normalizedField.includes(normalizedKey)) {
          substringScore = normalizedKey.length / normalizedField.length
        } else if (normalizedKey.includes(normalizedField)) {
          substringScore = normalizedField.length / normalizedKey.length
        }
        if (substringScore > 0) {
          totalSimilarity += substringScore * 0.35
          weights += 0.35
        }
        // 3. Levenshtein distance similarity (optimized)
        const maxLength = Math.max(normalizedKey.length, normalizedField.length)
        if (maxLength > 0) {
          const distance = levenshteinDistance(normalizedKey, normalizedField)
          const levenshteinSim = Math.max(0, 1 - distance / maxLength)
          totalSimilarity += levenshteinSim * 0.25
          weights += 0.25
        }
        // 4. Jaro-Winkler similarity
        const jaroWinklerSim = jaroWinklerSimilarity(normalizedKey, normalizedField)
        totalSimilarity += jaroWinklerSim * 0.25
        weights += 0.25
        // 5. Semantic similarity (enhanced weight)
        const semanticSim = calculateSemanticSimilarity(key, fieldName)
        if (semanticSim > 0) {
          totalSimilarity += semanticSim * 0.15
          weights += 0.15
        }
        // Calculate final similarity score
        const finalSimilarity = weights > 0 ? totalSimilarity / weights : 0
        // Enhanced confidence calculation
        let confidence = finalSimilarity
        if (normalizedKey.includes("custom") && normalizedField.includes("cf")) {
          confidence = Math.min(confidence + 0.2, 1.0)
        }
        const keyWords = normalizedKey.split(/\d+/).filter((w) => w.length > 2)
        const fieldWords = normalizedField.split(/\d+/).filter((w) => w.length > 2)
        const wordMatches = keyWords.filter((word) => fieldWords.some((fieldWord) => word === fieldWord)).length
        if (wordMatches > 0 && keyWords.length > 0) {
          confidence = Math.min(confidence + (wordMatches / keyWords.length) * 0.2, 1.0)
        }
        if (normalizedKey.length < 3 || normalizedField.length < 3) {
          confidence *= 0.8
        }
        if (finalSimilarity > bestMatch.similarity && finalSimilarity >= threshold) {
          bestMatch = { target: fieldName, similarity: finalSimilarity, confidence }
        }
      }
      return bestMatch
    },
    [normalizeString, levenshteinDistance, jaroWinklerSimilarity, calculateSemanticSimilarity],
  )

  const findChoicesForDependentField = useCallback((choices, level) => {
    const result = []
    const traverse = (options, currentLevel) => {
      if (currentLevel === level) {
        options.forEach((option) => {
          if (option.nested_options) {
            result.push(...option.nested_options)
          }
        })
      } else if (currentLevel < level) {
        options.forEach((option) => {
          if (option.nested_options && option.nested_options.length > 0) {
            traverse(option.nested_options, currentLevel + 1)
          }
        })
      }
    }
    traverse(choices, 1)
    return result
  }, [])

  const generateObjectForNestedFields = useCallback(
    (ticketFields) => {
      return ticketFields?.flatMap((field) => {
        if (!field.nested_fields || field.nested_fields.length === 0) {
          return [field]
        }
        const parentProps = {
          id: field.id,
          label: field.label,
          description: field.description,
          field_type: field.field_type,
          created_at: field.created_at,
          updated_at: field.updated_at,
          position: field.position,
          required_for_closure: field.required_for_closure,
          workspace_id: field.workspace_id,
          name: field.name,
          default_field: field.default_field,
          required_for_agents: field.required_for_agents,
          customers_can_edit: field.customers_can_edit,
          label_for_customers: field.label_for_customers,
          required_for_customers: field.required_for_customers,
          displayed_to_customers: field.displayed_to_customers,
          belongs_to_section: field.belongs_to_section,
          choices: field.choices,
          nested_fields: field.nested_fields,
          sections: field.sections,
        }
        const result = [parentProps]
        field.nested_fields.forEach((nestedField) => {
          result.push({
            ...parentProps,
            id: nestedField.id,
            label: nestedField.label,
            name: nestedField.name,
            description: nestedField.description,
            created_at: nestedField.created_at,
            updated_at: nestedField.updated_at,
            field_type: "dependent_field",
            choices: findChoicesForDependentField(field.choices, nestedField.level - 1),
            nested_fields: [],
            sections: [],
          })
        })
        return result
      })
    },
    [findChoicesForDependentField],
  )

  // Optimized fuzzy value matching for dropdown/choice fields
  const findBestValueMatch = useCallback(
    (sourceValue, targetValues, threshold = 0.6) => {
      if (!sourceValue || !targetValues || targetValues.length === 0) return null
      const normalizedSource = normalizeString(sourceValue)
      let bestMatch = { target: null, similarity: 0, confidence: 0 }

      // Pre-filter candidates for performance
      const candidates = targetValues.filter((targetValue) => {
        let valueToCompare = targetValue
        if (typeof targetValue === "object") {
          valueToCompare = targetValue.value || targetValue.label || targetValue.name || String(targetValue)
        }
        const normalizedTarget = normalizeString(valueToCompare)
        return normalizedTarget.length > 0 && normalizedTarget.length <= normalizedSource.length * 3
      })
      candidates.forEach((targetValue) => {
        // Handle different target value formats
        let valueToCompare = targetValue
        if (typeof targetValue === "object") {
          valueToCompare = targetValue.value || targetValue.label || targetValue.name || String(targetValue)
        }
        const normalizedTarget = normalizeString(valueToCompare)
        // Skip empty values
        if (!normalizedTarget) return
        let totalSimilarity = 0
        let weights = 0
        // 1. Exact match (highest priority) - Early return
        if (normalizedSource === normalizedTarget) {
          bestMatch = { target: targetValue, similarity: 1.0, confidence: 1.0 }
          return
        }
        // 2. Substring matching (enhanced)
        let substringScore = 0
        if (normalizedTarget.includes(normalizedSource)) {
          substringScore = normalizedSource.length / normalizedTarget.length
        } else if (normalizedSource.includes(normalizedTarget)) {
          substringScore = normalizedTarget.length / normalizedSource.length
        }
        if (substringScore > 0) {
          totalSimilarity += substringScore * 0.4
          weights += 0.4
        }
        // 3. Levenshtein distance similarity (optimized)
        const maxLength = Math.max(normalizedSource.length, normalizedTarget.length)
        if (maxLength > 0 && maxLength <= 50) {
          // Skip for very long strings
          const distance = levenshteinDistance(normalizedSource, normalizedTarget)
          const levenshteinSim = Math.max(0, 1 - distance / maxLength)
          totalSimilarity += levenshteinSim * 0.3
          weights += 0.3
        }
        // 4. Jaro-Winkler similarity
        if (maxLength <= 50) {
          // Skip for very long strings
          const jaroWinklerSim = jaroWinklerSimilarity(normalizedSource, normalizedTarget)
          totalSimilarity += jaroWinklerSim * 0.3
          weights += 0.3
        }
        // Calculate final similarity score
        const finalSimilarity = weights > 0 ? totalSimilarity / weights : 0
        // Enhanced confidence calculation with word-based matching
        let confidence = finalSimilarity
        // Boost confidence for partial word matches
        const sourceWords = normalizedSource.split(/\s+/).filter((w) => w.length > 1)
        const targetWords = normalizedTarget.split(/\s+/).filter((w) => w.length > 1)
        if (sourceWords.length > 0 && targetWords.length > 0) {
          const wordMatches = sourceWords.filter((word) =>
            targetWords.some(
              (targetWord) =>
                word === targetWord ||
                targetWord.includes(word) ||
                word.includes(targetWord) ||
                (Math.abs(word.length - targetWord.length) <= 1 && levenshteinDistance(word, targetWord) <= 1),
            ),
          ).length
          if (wordMatches > 0) {
            const wordMatchRatio = wordMatches / Math.max(sourceWords.length, targetWords.length)
            confidence = Math.min(confidence + wordMatchRatio * 0.2, 1.0)
          }
        }
        // Update best match if this is better
        if (finalSimilarity > bestMatch.similarity && finalSimilarity >= threshold) {
          bestMatch = { target: targetValue, similarity: finalSimilarity, confidence }
        }
      })
      return bestMatch.target ? bestMatch : null
    },
    [normalizeString, levenshteinDistance, jaroWinklerSimilarity],
  )

  // FIXED: Updated getTargetConnectionFields to preserve existing field states
  const getTargetConnectionFields = useCallback(() => {
    targets.forEach((targets) => {
      targets.mappings = []
      if (["freshservice", "atomicworks"].includes(target.target.name)) {
        targets.fieldMappings.forEach((field) => {
          if (field.targetfield === "workspace_id") {
            field.default = target.formData.workspace_id
          }
        })
      }
    })
    if (!targetExeRes || !targetExeRes.ticketFields || filteredAttributes.length === 0) {
      return
    }
    const localMappedSourceFields = new Set(mappedSourceFields)
    const ticketFields = targetExeRes.ticketFields
    let target_custom_objects = []
    target_custom_objects = ticketFields
      ?.filter(
        (field) =>
          field.field_type?.startsWith("custom_") ||
          field.name?.startsWith("cf") ||
          field.field_type?.includes("nested_field") ||
          field.field_type?.startsWith("default_"),
      )
      .flatMap((field) => {
        let obj_type = "string"
        switch (field.field_type) {
          case "custom_number":
          case "number":
          case "custom_decimal":
          case "default_number":
          case "default_status":
          case "default_urgency":
          case "default_impact":
          case "default_priority":
          case "default_product":
          case "default_group":
          case "default_agent":
          case "default_department":
          case "default_source":
          case "default_workspace":
          case "default_requester":
          case "default_category":
            obj_type = "number"
            break
          case "custom_checkbox":
          case "checkbox":
          case "default_checkbox":
            obj_type = "boolean"
            break
          case "default_subject":
          case "default_description":
          case "default_ticket_type":
            obj_type = "string"
            break
          case "custom_multi_select_dropdown":
          case "multi_select_dropdown":
          case "default_multi_select_dropdown":
            obj_type = "array"
            break
          case "custom_date":
          case "date":
          case "default_date":
          case "custom_datetime":
            obj_type = "datetime"
            break
          case "custom_dropdown":
          case "dropdown":
          case "default_dropdown":
            // Check if dropdown contains numeric values to determine if it should be number type
            if (field.choices && field.choices.length > 0) {
              const hasNumericValues = field.choices.some((choice) => {
                const value = choice.value || choice.id || choice
                return !isNaN(Number(value)) && value !== "" && value !== null
              })
              if (hasNumericValues) {
                obj_type = "number"
              } else {
                obj_type = "string"
              }
            } else {
              obj_type = "string"
            }
            break
          default:
            // For all fields, check if choices contain numeric values to determine type
            if (
              field.choices &&
              (Array.isArray(field.choices) ? field.choices.length > 0 : Object.keys(field.choices).length > 0)
            ) {
              const choicesArray = Array.isArray(field.choices) ? field.choices : Object.values(field.choices)
              const hasNumericValues = choicesArray.some((choice) => {
                const value = choice.value || choice.id || choice
                return !isNaN(Number(value)) && value !== "" && value !== null
              })
              if (hasNumericValues) {
                obj_type = "number"
              } else {
                obj_type = "string"
              }

            } else {
              obj_type = "string"
            }
            break
        }
        if (isDependencyAdded && (field.field_type === "custom_dropdown" || field.field_type === "dropdown")) {
          // Re-evaluate obj_type for dropdowns even when dependency is added
          if (field.choices && field.choices.length > 0) {
            const hasNumericValues = field.choices.some((choice) => {
              const value = choice.value || choice.id || choice
              return !isNaN(Number(value)) && value !== "" && value !== null
            })
            obj_type = hasNumericValues ? "number" : "string"
          } else {
            obj_type = "string"
          }
        }
        // Check if this is a default field that needs different processing
        const isDefaultField = field.field_type?.startsWith("default_")
        const generateFieldObject = (field, parentFieldName = null, level = 1, isNested = false) => {
          const bestMatch = findBestMatch(field.name, filteredAttributes, 0.5) // Use 0.5 threshold for custom fields
          let matchedSourceField = ""
          let shouldSkip = false

          // ENHANCED: Apply 0.6 confidence threshold - skip fields with low confidence unless required
          if (bestMatch?.target && bestMatch.similarity > 0.5 && !localMappedSourceFields.has(bestMatch.target)) {
            const isRequired = field.required_for_agents
            const isExactMatch = bestMatch.isExactMatch || bestMatch.similarity > 0.9

            // Only set source field if confidence is >= 0.6 OR field is required OR exact match
            if (bestMatch.confidence >= 0.6 || isRequired || isExactMatch) {
              localMappedSourceFields.add(bestMatch.target)
              matchedSourceField = bestMatch.target
            } else {
              // Low confidence match - skip the field unless required
              shouldSkip = !isRequired
              // FIXED: Don't set matchedSourceField for low confidence matches
              matchedSourceField = ""
            }
          } else if (bestMatch?.target && bestMatch.similarity <= 0.5) {
            // Very low similarity - skip unless required
            shouldSkip = !field.required_for_agents
            // FIXED: Don't set matchedSourceField for very low similarity matches
            matchedSourceField = ""
          }

          return {
            sourcefield: matchedSourceField,
            targetfield: field.name,
            override: [],
            attribute: field.name,
            type: obj_type,
            description: field.description,
            required: field.required_for_agents ? field.required_for_agents : false,
            default: "",
            isSourceFieldAvailable: true,
            mappingEnabled: isNested || field.choices?.length > 0,
            skip: shouldSkip,
            showFieldOnInitialScreen: true,
            is_custom_field: !isDefaultField, // Only set true for non-default fields
            custom_field_key: isDefaultField ? null : selectedEntityData?.customFieldKey || "custom_fields", // Only set for custom fields
            parent_field_name: parentFieldName,
            nested_level: level,
            is_nested: isNested,
            // Add fuzzy matching metadata
            matchConfidence: bestMatch?.confidence || 0,
            matchMethod:
              bestMatch?.similarity > 0.9
                ? "exact"
                : bestMatch?.similarity > 0.7
                  ? "high_similarity"
                  : bestMatch?.similarity > 0.5
                    ? "fuzzy_match"
                    : "no_match",
          }
        }
        const result = [generateFieldObject(field)]
        if (field.field_type?.includes("nested_field") && field.nested_fields?.length > 0) {
          result.push(
            ...field.nested_fields.map((nestedField) =>
              generateFieldObject(nestedField, field.name, nestedField.level, true),
            ),
          )
        }
        return result
      })
    const processedTicketFields = generateObjectForNestedFields(ticketFields)
    const updatedTargets = JSON.parse(JSON.stringify(targets))
    processedTicketFields?.forEach((field) => {
      // if (field.required_for_agents) {
      updatedTargets.forEach((target) => {
        if (target.name === selectedEntityData?.mainTarget?.[0]?.name) {
          target.fieldMappings?.forEach((temp_field) => {
            if (temp_field.targetfield === field.name) {
              // temp_field.required = true
              temp_field.showFieldOnInitialScreen = true
              temp_field.skip = false
            }
          })
        }
      })
      // }
    })

    // FIXED: Enhanced logic to preserve existing field states when updating targets
    // updatedTargets.forEach((target) => {
    //   target.automap = true
    //   if (target.name === selectedEntityData?.mainTarget?.[0]?.name && target_custom_objects.length > 0) {
    //     // Replace matching fieldMappings with objects from target_custom_objects
    //     if (target.fieldMappings && target.fieldMappings.length > 0) {
    //       target.fieldMappings = target.fieldMappings.map((existingObj) => {
    //         const replacement = target_custom_objects.find(
    //           (customObj) =>
    //             customObj.sourcefield === existingObj.sourcefield &&
    //             customObj.targetfield === existingObj.targetfield
    //         );
    //         return replacement ? replacement : existingObj;
    //       });
    //     }
    //     // Add any newMappings (target_custom_objects not already present by targetfield)
    //     const newMappings = target_custom_objects.filter(
    //       (customObj) =>
    //         !target.fieldMappings?.some((existingObj) => existingObj.targetfield === customObj.targetfield),
    //     )

    //     if (target.fieldMappings && newMappings.length > 0) {
    //       target.fieldMappings = [...target.fieldMappings, ...newMappings]
    //     } else if (newMappings.length > 0) {
    //       target.fieldMappings = [...newMappings]
    //     }
    //   }
    //   autoMap(target)
    // })
    updatedTargets.forEach((target) => {
      target.automap = true
      if (target.name === selectedEntityData?.mainTarget?.[0]?.name && target_custom_objects.length > 0) {
        // Replace matching fieldMappings with objects from target_custom_objects
        if (target.fieldMappings && target.fieldMappings.length > 0) {
          target.fieldMappings = target.fieldMappings.map((existingObj) => {
            const replacement = target_custom_objects.find(
              (customObj) => customObj.targetfield === existingObj.targetfield,
            )
            // If replacement found, merge it with existing object to preserve ALL existing data
            if (replacement) {
              return {
                ...replacement, // Start with replacement as base
                // Preserve ALL existing field states - this is the key fix
                override: existingObj.override || replacement.override || [], // Preserve existing override array
                sourcefield: existingObj.sourcefield !== undefined ? existingObj.sourcefield : replacement.sourcefield, // Preserve existing sourcefield
                mappings: existingObj.mappings || replacement.mappings, // Preserve existing mappings
                default: existingObj.default !== undefined ? existingObj.default : replacement.default, // Preserve existing default
                combinedFields: existingObj.combinedFields || replacement.combinedFields, // Preserve existing combinedFields
                skip: existingObj.skip !== undefined ? existingObj.skip : replacement.skip, // Preserve existing skip - THIS IS THE MAIN FIX
                required: existingObj.required !== undefined ? existingObj.required : replacement.required, // Preserve existing required
                status: existingObj.status || replacement.status, // Preserve existing status
                mappingEnabled:
                  existingObj.mappingEnabled !== undefined ? existingObj.mappingEnabled : replacement.mappingEnabled, // Preserve mappingEnabled
                // Preserve any other custom properties that might exist
                ...Object.keys(existingObj).reduce((acc, key) => {
                  if (!replacement.hasOwnProperty(key) && existingObj[key] !== undefined) {
                    acc[key] = existingObj[key]
                  }
                  return acc
                }, {}),
              }
            }
            return existingObj
          })
        }
        // Add any newMappings (target_custom_objects not already present by targetfield)
        const newMappings = target_custom_objects
          .filter(
            (customObj) =>
              !target.fieldMappings?.some((existingObj) => existingObj.targetfield === customObj.targetfield),
          )
          .map((customObj) => ({
            ...customObj,
            override: customObj.override || [], // Ensure new mappings have override array
          }))

        // Additional duplicate check before adding new mappings
        const uniqueNewMappings = []
        const existingTargetFields = new Set(target.fieldMappings?.map(f => f.targetfield) || [])

        newMappings.forEach((mapping) => {
          if (!existingTargetFields.has(mapping.targetfield)) {
            existingTargetFields.add(mapping.targetfield)
            uniqueNewMappings.push(mapping)
          } else {
            console.warn(`Preventing duplicate target field: ${mapping.targetfield}`)
          }
        })

        if (target.fieldMappings && uniqueNewMappings.length > 0) {
          target.fieldMappings = [...target.fieldMappings, ...uniqueNewMappings]
        } else if (uniqueNewMappings.length > 0) {
          target.fieldMappings = [...uniqueNewMappings]
        }
      }
      // FIXED: Only run autoMap for targets that haven't been initialized from saved data
      // or for fields that don't have existing configurations
      if (!isInitializedFromSavedData) {
        autoMap(target)
      } else {
        // For initialized data, only auto-map fields that are completely new (no sourcefield, no skip, etc.)
        const fieldsToAutoMap =
          target.fieldMappings?.filter(
            (field) =>
              !field.sourcefield &&
              field.skip === false &&
              !field.default &&
              (!field.combinedFields || field.combinedFields.length === 0),
          ) || []

        if (fieldsToAutoMap.length > 0) {
          // Create a temporary target with only unmapped fields for auto-mapping
          const tempTarget = {
            ...target,
            fieldMappings: fieldsToAutoMap,
          }
          const autoMappedTarget = autoMap(tempTarget)

          // Merge the auto-mapped results back into the original target
          autoMappedTarget.fieldMappings.forEach((autoMappedField) => {
            const originalFieldIndex = target.fieldMappings.findIndex(
              (field) => field.targetfield === autoMappedField.targetfield,
            )
            if (originalFieldIndex !== -1) {
              // Only update fields that were successfully auto-mapped
              if (autoMappedField.sourcefield && autoMappedField.sourcefield !== "") {
                target.fieldMappings[originalFieldIndex] = {
                  ...target.fieldMappings[originalFieldIndex],
                  sourcefield: autoMappedField.sourcefield,
                  matchConfidence: autoMappedField.matchConfidence,
                  matchMethod: autoMappedField.matchMethod,
                  autoMapped: autoMappedField.autoMapped,
                  status: autoMappedField.status,
                }
              }
            }
          })
        }
      }
    })
    setMappedSourceFields(localMappedSourceFields)
    const cleanedTargets = removeDuplicateTargetFields(updatedTargets)
    return cleanedTargets
  }, [
    targetExeRes,
    filteredAttributes,
    mappedSourceFields,
    targets,
    findBestMatch,
    isDependencyAdded,
    selectedEntityData,
    generateObjectForNestedFields,
    findBestValueMatch,
    isInitializedFromSavedData, // Add this dependency
  ])

  // FIXED: Updated useEffect to only refresh when necessary and not override saved data
  useEffect(() => {
    // Only refresh if we have the necessary data and either:
    // 1. We haven't initialized from saved data yet, OR
    // 2. We have new target/source data that requires processing
    if (targets.length > 0 && filteredAttributes.length > 0 && targetExeRes?.ticketFields) {
      // Don't refresh if we've already initialized from saved data unless there's new data
      if (!isInitializedFromSavedData || shouldRefreshTargets) {
        setShouldRefreshTargets(true)
      }
    }
  }, [targets?.length, filteredAttributes?.length, targetExeRes?.ticketFields, isInitializedFromSavedData])

  useEffect(() => {
    const refreshTargetData = async () => {
      if (shouldRefreshTargets) {
        // getTargetConnectionFields already returns cleaned targets (it calls removeDuplicateTargetFields internally)
        // Previously we were calling removeDuplicateTargetFields twice, which could over-prune fieldMappings
        const newTargets = getTargetConnectionFields()
        if (newTargets) {
          setTargets(newTargets)
          await saveMigration({ targets: newTargets })
        }
        setShouldRefreshTargets(false)
      }
    }
    refreshTargetData()
  }, [shouldRefreshTargets, getTargetConnectionFields])

  const calculateStats = useCallback(() => {
    let successful = 0
    let errors = 0
    let mismatches = 0
    let unmapped = 0
    targetData.forEach((target) => {
      target.fieldMappings?.forEach((mapping) => {
        if (mapping.required && !mapping.sourcefield) {
          errors++
        } else if (mapping.status === "error") {
          mismatches++
        } else if (!mapping.sourcefield && !mapping.skip) {
          unmapped++
        } else if (mapping.sourcefield && !mapping.skip) {
          successful++
        }
      })
    })
    return { successful, errors, mismatches, unmapped }
  }, [targetData])

  const calculateMappingStats = useCallback((target) => {
    let mapped = 0
    let highConfidence = 0
    let fuzzyMatches = 0
    let exactMatches = 0
    let autoMapped = 0
    target.fieldMappings?.forEach((mapping) => {
      if (mapping.sourcefield && !mapping.skip) {
        mapped++
        const confidence = mapping.matchConfidence || 0
        const method = mapping.matchMethod || "exact"
        if (confidence >= 0.8) {
          highConfidence++
        }
        if (method === "fuzzy_match" || method === "high_similarity") {
          fuzzyMatches++
        } else if (method === "exact") {
          exactMatches++
        }
        if (mapping.autoMapped) {
          autoMapped++
        }
      }
    })
    return { mapped, highConfidence, fuzzyMatches, exactMatches, autoMapped }
  }, [])

  // Helper function to get newly auto-mapped fields for better user feedback
  const getAutoMappedFields = useCallback((target) => {
    return target.fieldMappings?.filter((f) => f.autoMapped && f.sourcefield && !f.skip) || []
  }, [])

  const getValuesByColumnName = (columnName) => {
    if (uniqueSourceValues) {
      const sourceDropValueArray = Object.values(uniqueSourceValues)
      const columnObject = sourceDropValueArray[value].find((obj) => obj.column === columnName)
      return columnObject ? columnObject.values : []
    }
    return []
  }

  const getChoiceMappingAttribute = (sourceMappingConfig) => {
    return sourceMappingConfig?.mappingAttribute?.trim() ? sourceMappingConfig.mappingAttribute : "title"
  }

  const getSourceFieldDropdownValues = (name, attribute) => {
    if (source.type === "csv") {
      if (uniqueSourceValues) {
        const key = attribute.sourcefield !== "" ? attribute.sourcefield : undefined
        return getValuesByColumnName(key)
      }
    } else if (source.type === "api") {
      const sourceMappingKey = getSourceMappingObjectKey(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )
      const choiceKey = getChoiceMappingAttribute(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )
      const fieldName = getSourceMappingFieldName(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )
      if (sourceExeRes) {
        if (name === "Departments") {
          return sourceExeRes[name].map((obj) => obj.title)
        } else if (name === selectedEntityData?.mainTarget[0].name) {
          if (sourceExeRes[sourceMappingKey] && sourceExeRes[sourceMappingKey].length > 0) {
            for (const field of sourceExeRes[sourceMappingKey]) {
              if (attribute.sourcefield === field[fieldName]) {
                return field.choices.map((choice) => choice[choiceKey])
              }
            }
          }
        } else {
          if (sourceExeRes[name]) {
            return sourceExeRes[name].map((obj) => obj.name)
          }
          return []
        }
      }
      return []
    }
    return []
  }

  const getTargetFieldDropdownValues = (temp) => {
    if (targetExeRes && targetExeRes[temp]) {
      return targetExeRes[temp]
    }
    return []
  }

  const getTargetFieldDropdownValues1 = (name, field) => {
    const temp = name.toLowerCase()
    let searchFieldName = "label"
    if (field.is_custom_field) {
      searchFieldName = "name"
    }
    if (name === selectedEntityData?.mainTarget[0].name && targetExeRes && targetExeRes.ticketFields) {
      return getValuesByColumnName1(targetExeRes.ticketFields, field.targetfield, searchFieldName, "choices")
    }
    return []
  }

  const getValuesByColumnName1 = (arr, columnName, searchFieldName, returnFieldName) => {
    if (arr) {
      if (columnName !== "sub_category" && columnName !== "item_category") {
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === columnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        // Ensure we always return an array
        const result = columnObject ? columnObject[returnFieldName] : []
        // Convert object to array of objects if needed
        if (result && typeof result === "object" && !Array.isArray(result)) {
          return Object.entries(result).map(([key, value]) => ({
            id: key,
            value: Array.isArray(value) ? value[0] : value, // Take first value if array
            label: Array.isArray(value) ? value[1] || value[0] : value, // Take second value as label or fallback to first
          }))
        }
        return Array.isArray(result) ? result : []
      } else if (columnName == "sub_category") {
        const tempColumnName = "category"
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === tempColumnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        const choices = columnObject ? columnObject[returnFieldName] : []
        const sub_categories = []
        if (choices && choices.length > 0) {
          for (const item of choices) {
            if (item.nested_options && item.nested_options.length > 0) {
              for (const options of item.nested_options) {
                sub_categories.push(options)
              }
            }
          }
        }
        return sub_categories
      } else if (columnName == "item_category") {
        const tempColumnName = "category"
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === tempColumnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        const choices = columnObject ? columnObject[returnFieldName] : []
        const item_category = []
        if (choices && choices.length > 0) {
          for (const item of choices) {
            if (item.nested_options && item.nested_options.length > 0) {
              for (const options of item.nested_options) {
                if (options.nested_options && options.nested_options.length > 0) {
                  for (const item1 of options.nested_options) {
                    item_category.push(item1)
                  }
                }
              }
            }
          }
        }
        return item_category
      }
    }
    return []
  }

  const getTargetFieldDropdownDisplayValues = (group, name) => {
    const selectedObject = selectedEntityData?.mappingTargets?.find((obj) => obj.name === name)
    if (selectedObject.mappingDisplayField) {
      return group[selectedObject.mappingDisplayField]
    }
    return ""
  }

  // FIXED: Updated autoMap function to preserve existing field states
  const autoMap = (target, emptyColumns = []) => {
    const updatedTarget = JSON.parse(JSON.stringify(target))
    if (updatedTarget.fieldMappings) {
      for (const field of updatedTarget.fieldMappings) {
        // FIXED: Only auto-map fields that don't already have configurations
        // Skip fields that have been manually configured (have sourcefield, skip=true, default values, etc.)
        const hasExistingConfiguration =
          field.sourcefield ||
          field.skip === true ||
          (field.default !== undefined && field.default !== "") ||
          (field.combinedFields && field.combinedFields.length > 0)

        // Enhanced fuzzy field mapping logic - only for unconfigured fields
        if (!hasExistingConfiguration) {
          // Try to find the best fuzzy match for this target field
          const bestMatch = findBestMatch(field.targetfield, filteredAttributes, 0.5)
          if (bestMatch && bestMatch.target && bestMatch.similarity > 0.5) {
            field.sourcefield = bestMatch.target
            field.matchConfidence = bestMatch.confidence
            field.matchMethod =
              bestMatch.similarity > 0.9 ? "exact" : bestMatch.similarity > 0.7 ? "high_similarity" : "fuzzy_match"
            field.autoMapped = true

            // ENHANCED: If exact match found, automatically unskip the field
            if (bestMatch.isExactMatch) {
              field.skip = false
              field.status = "success"
              field.matchMethod = "exact"
            } else {
              // ENHANCED: Auto-skip fields with low confidence (<0.6) unless they are required
              if (bestMatch.confidence < 0.6 && !field.required) {
                field.skip = true
                field.status = "skipped"
                field.sourcefield = null // Clear the source field for skipped low-confidence matches
              } else {
                // Update status based on confidence for non-skipped fields
                if (field.required) {
                  field.status = bestMatch.confidence > 0.7 ? "success" : "warning"
                } else {
                  field.status = "success"
                }
              }
            }
          } else {
            // ENHANCED: No good match found - automatically skip non-required fields
            if (field.required) {
              field.status = "error"
              field.skip = false
            } else {
              // For non-required fields with no match, automatically skip
              field.skip = true
              field.status = "skipped"
            }
            field.matchMethod = "no_match"
            field.matchConfidence = 0
          }
        } else {
          // Field already has a configuration - validate and enhance it if needed
          if (field.sourcefield && filteredAttributes.includes(field.sourcefield)) {
            // ENHANCED: Check if this is an exact match (case-insensitive)
            const exactMatch = field.targetfield.toLowerCase().trim() === field.sourcefield.toLowerCase().trim()
            if (exactMatch) {
              field.matchConfidence = 1.0
              field.matchMethod = "exact"
              field.status = "success"
              // ENHANCED: Exact matches should never be skipped
              if (field.skip === true) {
                field.skip = false
              }
            } else {
              // Calculate confidence for existing mapping
              const bestMatch = findBestMatch(field.targetfield, [field.sourcefield], 0.0)
              field.matchConfidence = bestMatch.confidence || 0.5
              field.matchMethod =
                bestMatch.similarity > 0.9 ? "exact" : bestMatch.similarity > 0.7 ? "high_similarity" : "fuzzy_match"
            }
            field.status = field.matchConfidence > 0.6 ? "success" : "warning"

            // ENHANCED: Auto-skip fields with low confidence (<0.6) unless they are required
            if (field.matchConfidence < 0.6 && !field.required && field.matchMethod !== "exact") {
              field.skip = true
              field.status = "skipped"
              field.sourcefield = null // Clear the source field for skipped low-confidence matches
            }

            // Don't change skip status for existing configurations unless it's an exact match or low confidence
          }
        }

        // Handle skip logic for fields that should be skipped - but preserve existing skip states
        if (
          (((field.sourcefield === "" || field.sourcefield === null) &&
            field.default === "" &&
            (!field.combinedFields || field.combinedFields.length === 0)) ||
            emptyColumns.includes(field.sourcefield)) &&
          !field.required &&
          field.skip !== true // Don't override existing skip=true
        ) {
          field.skip = true
          field.status = "skipped"
        }

        // Set required fields based on target configuration
        if (updatedTarget.name === selectedEntityData?.mainTarget[0].name) {
          if (isRequired(field.targetfield)) {
            field.required = true
            // Don't allow skipping required fields
            if (field.skip && field.required) {
              field.skip = false
            }
          }
        }

        // Handle value mapping for dropdown/choice fields - only for fields with sourcefield
        if (field.mappingEnabled && field.sourcefield && !field.mappings) {
          const sourceGroups = getSourceFieldDropdownValues(updatedTarget.name, field)
          let targetGroups = []
          if (updatedTarget.name !== selectedEntityData?.mainTarget[0].name) {
            targetGroups = getTargetFieldDropdownValues(updatedTarget.name)
          } else {
            targetGroups = getTargetFieldDropdownValues1(updatedTarget.name, field)
          }
          const matchedGroups = []
          // Enhanced fuzzy matching for value mapping
          sourceGroups.forEach((sourceGroup) => {
            // Try fuzzy matching first
            const fuzzyMatch = findBestValueMatch(sourceGroup, targetGroups, 0.6)
            if (fuzzyMatch) {
              if (field.type === "array") {
                matchedGroups.push({
                  sourcevalue: [sourceGroup],
                  targetvalue: [fuzzyMatch],
                  matchMethod: "fuzzy",
                  confidence: fuzzyMatch.confidence || 0.6,
                })
              } else {
                matchedGroups.push({
                  sourcevalue: [sourceGroup],
                  targetvalue: fuzzyMatch,
                  matchMethod: "fuzzy",
                  confidence: fuzzyMatch.confidence || 0.6,
                })
              }
            } else {
              // Fallback to original exact matching logic
              const lowercaseSourceGroup = sourceGroup ? sourceGroup.toLowerCase() : ""
              let targetIndex = -1
              if (updatedTarget.name !== selectedEntityData?.mainTarget[0].name) {
                targetIndex = targetGroups.findIndex(
                  (targetGroup) =>
                    getTargetFieldDropdownDisplayValues(targetGroup, updatedTarget.name).toLowerCase() ===
                    lowercaseSourceGroup,
                )
              } else {
                targetIndex = targetGroups.findIndex(
                  (targetGroup) =>
                    typeof targetGroup?.value === "string" && targetGroup.value.toLowerCase() === lowercaseSourceGroup,
                )
              }
              if (targetIndex !== -1) {
                if (field.type === "array") {
                  matchedGroups.push({
                    sourcevalue: [sourceGroup],
                    targetvalue: [targetGroups[targetIndex]],
                    matchMethod: "exact",
                    confidence: 1.0,
                  })
                } else {
                  matchedGroups.push({
                    sourcevalue: [sourceGroup],
                    targetvalue: targetGroups[targetIndex],
                    matchMethod: "exact",
                    confidence: 1.0,
                  })
                }
              }
            }
          })
          if (matchedGroups.length > 0) {
            field.mappings = matchedGroups
            // Set status based on matching confidence
            const avgConfidence =
              matchedGroups.reduce((sum, match) => sum + (match.confidence || 0), 0) / matchedGroups.length
            field.status = avgConfidence > 0.8 ? "success" : avgConfidence > 0.5 ? "warning" : "error"
            field.autoMappingConfidence = avgConfidence
          }
        }
      }
    }
    return updatedTarget
  }

  // NEW: Full remap logic that re-evaluates unmapped fields, ensuring each source field is used at most once.
  // For API sources, preserves existing mappings for any fields that are already mapped.
  const remapAllFields = (target) => {
    if (!target || !target.fieldMappings || !Array.isArray(filteredAttributes) || filteredAttributes.length === 0) {
      return target
    }
    const cloned = JSON.parse(JSON.stringify(target))
    const usedSources = new Set()

    // For API sources, first preserve existing mappings for any fields that are already mapped
    if (source.type === "api") {
      cloned.fieldMappings.forEach((field) => {
        if (field.sourcefield && field.sourcefield !== "" && field.sourcefield !== null) {
          // Keep the existing mapping for any mapped fields in API sources
          usedSources.add(field.sourcefield)
          // Don't mark as autoMapped since it was already mapped
          field.autoMapped = field.autoMapped || false
        }
      })
    }

    // Collect best candidate for every field that needs remapping
    const candidates = cloned.fieldMappings.map((field) => {
      // Skip evaluation for API source fields that are already mapped
      if (source.type === "api" && field.sourcefield && field.sourcefield !== "" && field.sourcefield !== null) {
        return { field, bestMatch: null, skipEvaluation: true }
      }

      const bestMatch = findBestMatch(field.targetfield, filteredAttributes, 0.0) // allow low similarity; we'll threshold later
      return { field, bestMatch, skipEvaluation: false }
    })

    // Sort by confidence descending (fall back to similarity if confidence missing)
    candidates.sort((a, b) => {
      if (a.skipEvaluation) return 1 // move to end
      if (b.skipEvaluation) return -1
      const ca = a.bestMatch?.confidence ?? a.bestMatch?.similarity ?? 0
      const cb = b.bestMatch?.confidence ?? b.bestMatch?.similarity ?? 0
      return cb - ca
    })

    // First pass: assign unique highest confidence for unmapped fields
    candidates.forEach(({ field, bestMatch, skipEvaluation }) => {
      if (skipEvaluation) return // Skip already mapped fields for API sources

      const confidence = bestMatch?.confidence ?? bestMatch?.similarity ?? 0
      const source = bestMatch?.target
      // Minimum similarity threshold (retain original 0.5 baseline) unless field is required
      if (source && !usedSources.has(source) && (confidence >= 0.5 || field.required)) {
        field.sourcefield = source
        field.matchConfidence = confidence
        field.matchMethod = bestMatch.similarity > 0.9 ? 'exact' : bestMatch.similarity > 0.7 ? 'high_similarity' : 'fuzzy_match'
        field.autoMapped = true
        field.skip = false
        field.status = 'success'
        usedSources.add(source)
      } else {
        field.sourcefield = null
      }
    })

    // Second pass: handle required fields without assignment (try relaxed assignment allowing duplicate if needed)
    candidates.forEach(({ field, bestMatch, skipEvaluation }) => {
      if (skipEvaluation) return // Skip already mapped fields for API sources

      if (field.required && !field.sourcefield) {
        const source = bestMatch?.target
        const confidence = bestMatch?.confidence ?? bestMatch?.similarity ?? 0
        if (source && confidence >= 0.3) { // relaxed for required
          field.sourcefield = source
          field.matchConfidence = confidence
          field.matchMethod = bestMatch.similarity > 0.9 ? 'exact' : bestMatch.similarity > 0.7 ? 'high_similarity' : 'fuzzy_match'
          field.autoMapped = true
          field.skip = false
          field.status = confidence >= 0.6 ? 'success' : 'warning'
        } else {
          field.skip = true // Set skip to true if no mapping found
          field.status = 'skipped'
          field.matchMethod = 'no_match'
          field.matchConfidence = 0
        }
      }
    })

    // Third pass: finalize all fields without mapping
    candidates.forEach(({ field, skipEvaluation }) => {
      if (skipEvaluation) return // Skip already mapped fields for API sources

      if (!field.sourcefield) {
        field.skip = true
        field.status = 'skipped'
        field.matchMethod = field.matchMethod || 'no_match'
        field.matchConfidence = field.matchConfidence || 0
      }
    })
    return cloned
  }

  const getMappedFieldsCount = (target) => {
    return (
      target?.fieldMappings?.filter(
        (field) =>
          field.showFieldOnInitialScreen &&
          !field.skip &&
          !showAlertIcon(field, target) &&
          ((field.sourcefield && field.sourcefield !== "") ||
            field.override?.length > 0 ||
            (field.default ?? "") !== "" ||
            field.combinedFields?.length > 0),
      ).length || 0
    )
  }

  const getErrorFieldsCount = (target) => {
    return target?.fieldMappings?.filter((field) => showAlertIcon(field, target)).length || 0
  }

  const getTotalFieldsCount = (target) =>
    target?.fieldMappings?.filter((mapping) => mapping.showFieldOnInitialScreen).length || 0

  const getSkippedFieldsCount = (fieldMappings) =>
    fieldMappings?.filter((field) => field.showFieldOnInitialScreen && field.skip).length || 0

  // Helper function to calculate unmapped fields count
  const getUnmappedFieldsCount = (target) => {
    if (!target) return 0
    return getTotalFieldsCount(target) - getMappedFieldsCount(target) - getSkippedFieldsCount(target?.fieldMappings)
  }


  const handleSmartAutoMapClick = () => {
    // Check if there are unmapped fields before showing confirmation
    const currentTarget = targets[value]
    const unmappedCount = getUnmappedFieldsCount(currentTarget)
    if (unmappedCount === 0) {
      toast.success("All fields have been mapped successfully! No unmapped fields to auto-map.", {
        position: "top-right",
        autoClose: 4000,
        style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
      })
      return
    }
    setShowSmartAutoMapConfirm(true)
  }

  const executeSmartAutoMap = () => {
    const currentTarget = targets[value]
    if (!currentTarget || !currentTarget.fieldMappings) {
      return
    }
    // Check if there are unmapped fields before proceeding
    const unmappedCount = getUnmappedFieldsCount(currentTarget)
    if (unmappedCount === 0) {
      toast.success("All fields have been mapped successfully! No unmapped fields to auto-map.", {
        position: "top-right",
        autoClose: 4000,
        style: { fontFamily: "Inter, sans-serif", fontSize: 15 },
      })
      setShowSmartAutoMapConfirm(false)
      return
    }
    const beforeMappingCount = currentTarget.fieldMappings?.filter((f) => f.sourcefield && !f.skip).length || 0
    // Clear any previous auto-mapping flags, but preserve existing mappings for mapped fields in API sources
    currentTarget.fieldMappings.forEach((f) => {
      // For API sources, don't clear autoMapped flag for any fields that are already mapped
      if (source.type === "api" && f.sourcefield && f.sourcefield !== "" && f.sourcefield !== null) {
        // Keep the existing mapping for any mapped API fields
        return
      }
      // Only clear autoMapped flag for unmapped fields that will be processed
      if (!f.sourcefield || f.sourcefield === "" || f.sourcefield === null) {
        f.autoMapped = false
      }
    })
    // Use new full remap logic instead of incremental autoMap
    const updated = remapAllFields(currentTarget)
    if (updated) {
      const newTargets = [...targets]
      newTargets[value] = updated
      const cleanedTargets = removeDuplicateTargetFields(newTargets)
      setTargets(cleanedTargets)
      setTargetData(cleanedTargets)
      // Calculate detailed mapping statistics
      const stats = calculateMappingStats(updated)
      const autoMappedFields = getAutoMappedFields(updated)
      const totalNewMappings = autoMappedFields.length
      // Show detailed feedback based on results
      if (totalNewMappings > 0) {
        const fieldNames = autoMappedFields
          .slice(0, 3)
          .map((f) => f.targetfield)
          .join(", ")
        const moreText = autoMappedFields.length > 3 ? ` and ${autoMappedFields.length - 3} more` : ""
        const apiNote = source.type === "api" ? " (Existing field mappings were preserved)" : ""
        toast.success(
          `Smart Auto-Map completed! Mapped ${totalNewMappings} fields: ${fieldNames}${moreText}. High confidence: ${stats.highConfidence}, Fuzzy matches: ${stats.fuzzyMatches}${apiNote}`,
          {
            position: "top-right",
            autoClose: 5000,
          },
        )
      } else {
        const apiNote = source.type === "api" ? " (Existing field mappings were preserved)" : ""
        toast.info(`Smart Auto-Map completed! All possible fields are already mapped. Total mapped: ${stats.mapped}${apiNote}`, {
          position: "top-right",
          autoClose: 5000,
        })
      }
    }
    setShowSmartAutoMapConfirm(false)
  }

  // FIXED: Remove the useEffect that was running executeSmartAutoMap on component mount
  // This was causing the issue where fields were being auto-mapped when revisiting the page
  // useEffect(() => {
  //   // Only run smart auto-map if there are unmapped fields
  //   const currentTarget = targets[value]
  //   const unmappedCount = getUnmappedFieldsCount(currentTarget)
  //   if (unmappedCount > 0) {
  //     executeSmartAutoMap()
  //   }
  // }, [])

  const initialAutoMapDoneRef = useRef(false)
  useEffect(() => {
    const init = async () => {// Only run smart auto-map if there are unmapped fields
      const currentTarget = targets[value]
      const unmappedCount = getUnmappedFieldsCount(currentTarget)
      if (unmappedCount > 0) {
        let initData = await getMigrationPlanByIdandFields(planId, "initializationCompleted", true)
        initData = initData.response?.[0]?.initializationCompleted
        // New persistence: check migrationState/additional_details instead of localStorage
        // const persistedKey = migrationState?.smartAutoMapRunKey || migrationState?.dataMappingData?.smartAutoMapRunKey
        // const currentKey = planId || templateName || 'default'
        // if (persistedKey === currentKey) {
        //   initialAutoMapDoneRef.current = true
        //   return
        // }
        if (initData == null || initData === false || initData === undefined) {
          executeSmartAutoMap()
          const patchData = await patchMigrationPlanById(planId, { initializationCompleted: true })
          console.log('Smart Auto-Map executed successfully:', patchData)
        }
        // After execution, update migrationState with the key so it gets persisted on save
        // setMigrationState((prev) => ({
        //   ...prev,
        //   smartAutoMapRunKey: currentKey,
        //   dataMappingData: {
        //     ...(prev.dataMappingData || {}),
        //     smartAutoMapRunKey: currentKey,
        //     targets: targets,
        //   },
        // }))
        // handleSave()

        initialAutoMapDoneRef.current = true
        console.log('Initial Smart Auto-Map completed successfully (persisted to additional_details).')
      }
    }
    init()
  }, [targets])

  // Debug logging to understand step completion status
  useEffect(() => {
    if (completedSteps) {
      console.log('DataMapping - Step completion status:', completedSteps);
    }
  }, [completedSteps]);

  return (
    <div style={{ position: "relative" }}>
      {isSaving && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(255, 255, 255, 0.85)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 4000,
            backdropFilter: "blur(1px)",
          }}
        >
          <LoaderSpinner fullpage={true} />
        </div>
      )}
      <p></p>
      <div className={styles.dFlex}>
        <div style={{ display: "flex", gap: "20px", marginLeft: "auto" }}>
          <div className={styles.dFlex}>
            <img
              src="/assets/help.png"
              alt="Help"
              className={globalStyles.helpIcon}
              onClick={() => {
                displayArticle("Why do all these tabs appear?")
              }}
            />
            <span className={globalStyles.guideName}>Why do all these tabs appear?</span>
          </div>
        </div>
      </div>
      <Tabs
        value={value}
        onChange={handleChange}
        variant="fullWidth"
        allowScrollButtonsMobile
        aria-label="data mapping tabs"
        className={styles.tab}
        sx={{
          width: "100%",
          "& .MuiTabs-flexContainer": {
            width: "100%",
            display: "flex",
          },
          "& .MuiTabs-scroller": {
            overflow: "visible !important",
          },
          "& .MuiTabs-indicator": {
            backgroundColor: "#FFFFFF",
          },
          "& .MuiTabs-scrollButtons": {
            display: "none",
          },
          minHeight: "35px",
        }}
      >
        {targetData
          .filter((item) => item.isMappingTarget === "false")
          .map((item, index) => {
            const tabEnabled = isTabEnabled(index, item.name);
            return (
              <Tab
                key={index}
                label={item.name}
                style={{
                  pointerEvents: tabEnabled ? "auto" : "none",
                  fontSize: "11px",
                  fontWeight: "bold",
                  opacity: tabEnabled ? 1 : 0.6,
                }}
                sx={{
                  minHeight: "35px",
                  color: tabEnabled ? "#DCDAD9" : "#D6D3D1",
                  borderRight: "1px solid #DCDAD9",
                  borderBottom: "1px solid #DCDAD9",
                  borderTop: "1px solid #DCDAD9",
                  borderLeft: index === 0 ? "1px solid #DCDAD9" : "none",
                  borderRadius: 0,
                  minWidth: "0",
                  maxWidth: "none",
                  padding: "10px 15px",
                  flex: 1,
                  "&.Mui-selected": {
                    color: "#EA5822",
                    fontWeight: "700",
                    fontSize: "12px",
                    backgroundColor: "#FFFFFF",
                    boxShadow: "3px -5px 7px 0px #00000026 !important",
                  },
                  "&.Mui-disabled": {
                    color: "#D6D3D1 !important",
                    opacity: "0.6 !important",
                  },
                }}
                disabled={!tabEnabled}
              />
            );
          })}
      </Tabs>
      <div style={{ padding: "20px 0px 20px 20px" }}>
        <div className={styles.dFlex}>
          <div className={globalStyles.selectionName}>MAP YOUR DATA FIELDS FOR MIGRATION</div>
          <div style={{ display: "flex", gap: "20px", marginLeft: "auto", alignItems: "center" }}>
            <button
              className={`${globalStyles.mainButton} ${styles.saveBtn}`}
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? "Saving..." : "Save"}
            </button>
          </div>
        </div>
        {/* <div className={styles.dFlex} style={{ justifyContent: "stretch", gap: "10px" }}>
          <div className={styles.container}>
            <CheckIcon className={styles.iconStyle} />
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "30px" }}>
              {getMappedFieldsCount(targets[value])}
            </div>
            <div className={styles.boxName}>
              Successfully <br /> mapped
            </div>
          </div>
          <div className={styles.container}>
            <span style={{ fontWeight: "bold", fontSize: "20px", color: "#746b68" }}>!</span>
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "30px" }}>
              {getErrorFieldsCount(targets[value])}
            </div>
            <div className={styles.boxName}>
              Errors in <br /> mapping
            </div>
          </div>
          <div className={styles.container}>
            <HiNoSymbol className={styles.iconStyle} />
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "30px" }}>
              {getTotalFieldsCount(targets[value]) -
                getMappedFieldsCount(targets[value]) -
                getSkippedFieldsCount(targets[value]?.fieldMappings)}
            </div>
            <div className={styles.boxName}>
              Unmapped <br /> fields
            </div>
          </div>
        </div> */}
        <div className={styles.dFlex} style={{ justifyContent: "stretch", gap: "8px" }}>
          <div className={styles.container} style={{ padding: "5px", height: "40px" }}>
            <CheckIcon className={styles.iconStyle} style={{ marginLeft: "10px", width: "20px", height: "20px" }} />
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "24px" }}>
              {getMappedFieldsCount(targets[value])}
            </div>
            <div className={styles.boxName} style={{ fontSize: "12px" }}>
              Successfully mapped
            </div>
          </div>
          <div className={styles.container} style={{ padding: "5px", height: "40px" }}>
            <span style={{ marginLeft: "10px", fontWeight: "bold", fontSize: "24px", color: "#746b68" }}>!</span>
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "24px" }}>
              {getErrorFieldsCount(targets[value])}
            </div>
            <div className={styles.boxName} style={{ fontSize: "12px" }}>
              Errors in mapping
            </div>
          </div>
          <div className={styles.container} style={{ padding: "5px", height: "40px" }}>
            <HiNoSymbol className={styles.iconStyle} style={{ marginLeft: "10px", width: "20px", height: "20px" }} />
            <div className={globalStyles.poppinsHeaderStyle} style={{ fontSize: "24px" }}>
              {getTotalFieldsCount(targets[value]) -
                getMappedFieldsCount(targets[value]) -
                getSkippedFieldsCount(targets[value]?.fieldMappings)}
            </div>
            <div className={styles.boxName} style={{ fontSize: "12px" }}>
              Unmapped fields
            </div>
          </div>
        </div>
        <div className={styles.dFlex} style={{ marginTop: "10px" }}>
          <button
            className={`${styles.dFlex} ${styles.buttonStyle} ${styles.smartAutoMapBtn}`}
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              gap: "5px",
              color: "white",
              border: "none",
              fontFamily: "Inter",
              width: "200px",
              height: "35px",
            }}
            onClick={handleSmartAutoMapClick}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9L15 5V9H19Z" />
            </svg>
            Smart Auto-Map
          </button>
          <div className={styles.dFlex} style={{ marginLeft: "20px" }}>
            <img
              src="/assets/help.png"
              alt="Help"
              className={globalStyles.helpIcon}
              onClick={() => {
                displayArticle("How do I fix errors and mismatched data?")
              }}
            />
            <span className={globalStyles.guideName}>How do I fix errors and mismatched data?</span>
          </div>
          <div className={styles.dFlex} style={{ marginLeft: "auto", justifyContent: "center", alignItems: "center" }}>
            <div className={globalStyles.searchWrapper}>
              <SearchIcon className={globalStyles.searchIcon} />
              <input
                type="text"
                placeholder="Search..."
                className={globalStyles.searchInput}
                value={searchTerm}
                onChange={handleSearchChange}
                onFocus={(e) => {
                  e.target.style.width = "200px"
                  e.target.placeholder = "Typing..."
                }}
                onBlur={(e) => {
                  e.target.style.width = "80px"
                  e.target.placeholder = "Search..."
                }}
              />
            </div>
            <div className={styles.filterContainer} ref={filterDropdownRef}>
              <button
                className={`${styles.dFlex} ${styles.buttonStyle}`}
                style={{ gap: "10px", marginBottom: "10px" }}
                onClick={() => setShowFilterDropdown(!showFilterDropdown)}
              >
                <HiOutlineFunnel className={styles.iconStyle} />
                {selectedFilter}
              </button>
              {showFilterDropdown && (
                <div className={styles.filterDropdown}>
                  {getFilterOptions().map((option, index) => (
                    <div key={index} className={styles.filterOption} onClick={() => handleFilterSelect(option)}>
                      {option}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        {targetData[value]?.name === "Conversations" && (
          <div style={{ marginTop: "20px", display: "flex", alignItems: "center", gap: "10px" }}>
            <input
              type="checkbox"
              checked={isNotes}
              onChange={(e) => setIsNotes(e.target.checked)}
              aria-label="Create conversations as notes"
              style={{
                width: "16px",
                height: "16px",
                cursor: "pointer",
                accentColor: "black",
              }}
            />
            <label className={globalStyles.interStyle} style={{ paddingBottom: "10px", cursor: "pointer" }}>
              Create conversations as notes
            </label>
          </div>
        )}
        <div className={styles.tableContainer}>
          <table className={globalStyles.table}>
            <thead className={globalStyles.tableHeader}>
              <tr className={globalStyles.rowStyles}>
                <th className={globalStyles.headerCell}>Status</th>
                <th className={globalStyles.headerCell}>Source field</th>
                <th className={globalStyles.headerCell}>Target field</th>
                <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>
                  <div style={{ display: "flex", alignItems: "center", justifyContent: "center", gap: "8px" }}>
                    Skip field
                    <SkipAllOptionalToggle
                      checked={isSkipAllOptional}
                      onChange={handleSkipAllChange}
                    />
                  </div>
                </th>
                <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Advanced</th>
              </tr>
            </thead>
            <tbody>
              {paginatedData.length > 0 ? (
                paginatedData.map(
                  (mapping, index) =>
                    mapping.showFieldOnInitialScreen && (
                      <tr key={index} className={globalStyles.tableRow}>
                        <td className={globalStyles.cell}>
                          <div className={globalStyles.centerContent}>{getEnhancedStatusIcon(mapping)}</div>
                        </td>
                        <td className={globalStyles.cell} style={{ zIndex: "9999", fontSize: "12px" }}>
                          <div className={styles.fieldWithIcon}>
                            <div
                              style={{ position: "relative", display: "inline-block" }}
                              onMouseEnter={() => setDataTypeTooltip({ key: `source-${index}`, type: mapping.type })}
                              onMouseLeave={() => setDataTypeTooltip(null)}
                              onClick={() => setDataTypeTooltip({ key: `source-${index}`, type: mapping.type })}
                            >
                              <img
                                src={getDataTypeIcon(mapping.type) || "/placeholder.svg"}
                                alt={mapping.type}
                                className={styles.dataTypeIcon}
                                style={{ cursor: "pointer" }}
                              />
                              {dataTypeTooltip?.key === `source-${index}` && (
                                <div
                                  style={{
                                    position: "absolute",
                                    top: "-32px",
                                    left: 0,
                                    background: "#170903",
                                    color: "#F8F8F7",
                                    padding: "4px 8px",
                                    borderRadius: "4px",
                                    fontSize: "11px",
                                    fontFamily: "Inter, sans-serif",
                                    whiteSpace: "nowrap",
                                    zIndex: 2000,
                                    boxShadow: "0 2px 6px rgba(0, 0, 0, 0.2)",
                                  }}
                                >
                                  {mapping.type || "text"}
                                  <div
                                    style={{
                                      position: "absolute",
                                      bottom: "-5px",
                                      left: "8px",
                                      width: 0,
                                      height: 0,
                                      borderLeft: "5px solid transparent",
                                      borderRight: "5px solid transparent",
                                      borderTop: "5px solid #170903",
                                    }}
                                  />
                                </div>
                              )}
                            </div>
                            <Dropdown
                              value={mapping.sourcefield}
                              options={["None", ...filteredAttributes]}
                              onChange={(newValue) =>
                                handleSourceFieldChange(value, index, newValue === "None" ? null : newValue)
                              }
                              placeholder="Select Source"
                              required={mapping.required}
                              none={true}
                            />
                          </div>
                        </td>
                        <td className={globalStyles.cell}>
                          <div className={styles.fieldWithIcon}>
                            <div
                              style={{ position: "relative", display: "inline-block" }}
                              onMouseEnter={() => setDataTypeTooltip({ key: `target-${index}`, type: mapping.type })}
                              onMouseLeave={() => setDataTypeTooltip(null)}
                              onClick={() => setDataTypeTooltip({ key: `target-${index}`, type: mapping.type })}
                            >
                              <img
                                src={getDataTypeIcon(mapping.type) || "/placeholder.svg"}
                                alt={mapping.type}
                                className={styles.dataTypeIcon}
                                style={{ cursor: "pointer" }}
                              />
                              {dataTypeTooltip?.key === `target-${index}` && (
                                <div
                                  style={{
                                    position: "absolute",
                                    top: "-32px",
                                    left: 0,
                                    background: "#170903",
                                    color: "#F8F8F7",
                                    padding: "4px 8px",
                                    borderRadius: "4px",
                                    fontSize: "11px",
                                    fontFamily: "Inter, sans-serif",
                                    whiteSpace: "nowrap",
                                    zIndex: 2000,
                                    boxShadow: "0 2px 6px rgba(0, 0, 0, 0.2)",
                                  }}
                                >
                                  {mapping.type || "text"}
                                  <div
                                    style={{
                                      position: "absolute",
                                      bottom: "-5px",
                                      left: "8px",
                                      width: 0,
                                      height: 0,
                                      borderLeft: "5px solid transparent",
                                      borderRight: "5px solid transparent",
                                      borderTop: "5px solid #170903",
                                    }}
                                  />
                                </div>
                              )}
                            </div>
                            <span className={styles.fieldName}>{mapping.targetfield || "Select Target"} </span>
                            <span className={styles.fieldName}> {mapping.required ? "*" : ""} </span>
                          </div>
                        </td>
                        <td className={globalStyles.cell}>
                          <div className={globalStyles.centerContent}>
                            {mapping.required === false && (
                              <div>
                                <FormControlLabel
                                  style={{ marginLeft: "auto" }}
                                  control={
                                    <Switch
                                      checked={mapping.skip}
                                      onChange={() => handleCheckboxChange(value, index)}
                                      sx={{
                                        width: 36,
                                        height: 18,
                                        padding: 0,
                                        "& .MuiSwitch-switchBase": {
                                          padding: 0,
                                          margin: "2px",
                                          transitionDuration: "300ms",
                                          "&.Mui-checked": {
                                            transform: "translateX(16px)",
                                            color: "#fff",
                                            "& + .MuiSwitch-track": {
                                              backgroundColor: "#E97451",
                                              opacity: 1,
                                              border: 0,
                                            },
                                            "& .MuiSwitch-thumb": {
                                              backgroundColor: "#fff",
                                              width: 14,
                                              height: 14,
                                              backgroundImage: "url('/assets/skip.png')",
                                              backgroundSize: "16px 16px",
                                              backgroundRepeat: "no-repeat",
                                              backgroundPosition: "top 0.5px left 0.4px",
                                            },
                                            "&.Mui-disabled + .MuiSwitch-track": {
                                              opacity: 0.5,
                                            },
                                          },
                                        },
                                        "& .MuiSwitch-thumb": {
                                          backgroundColor: "#fff",
                                          boxSizing: "border-box",
                                          width: 14,
                                          height: 14,
                                          borderRadius: "50%",
                                          transition: "width 0.2s, height 0.2s",
                                          backgroundImage: mapping.skip ? "url('/assets/skip.png')" : "none",
                                          backgroundSize: "16px 16px",
                                          backgroundRepeat: "no-repeat",
                                          backgroundPosition: "top 0.5px left 0.4px",
                                        },
                                        "& .MuiSwitch-track": {
                                          borderRadius: 20,
                                          backgroundColor: "#B9B5B3",
                                          opacity: 1,
                                          transition: "background-color 0.5s",
                                        },
                                      }}
                                    />
                                  }
                                />
                              </div>
                            )}
                          </div>
                        </td>
                        <td className={globalStyles.cell}>
                          <div className={globalStyles.centerContent}>
                            <div className={styles.settingsContainer}>
                              <CogIcon
                                className={globalStyles.closeIcon}
                                onClick={() => {
                                  setActiveSettingIndex(index)
                                  setShowSettingsDropdown((prev) => ({
                                    ...prev,
                                    [index]: !prev[index],
                                  }))
                                }}
                                ref={(el) => {
                                  settingsDropdownRef.current[index] = el
                                }}
                              />
                              {showSettingsDropdown[index] && activeSettingIndex === index && (
                                <div
                                  className={styles.settingsDropdown}
                                  ref={(el) => {
                                    settingsDropdownRef.current.dropdown = el
                                  }}
                                >
                                  <div
                                    className={styles.settingsOption}
                                    style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
                                  >
                                    <span>Required field</span>
                                    <Switch
                                      checked={mapping.required}
                                      onChange={(event) => handleRequiredFieldChange(value, index, event.target.checked)}
                                      sx={{
                                        width: 36,
                                        height: 18,
                                        padding: 0,
                                        "& .MuiSwitch-switchBase": {
                                          padding: 0,
                                          margin: "2px",
                                          transitionDuration: "300ms",
                                          "&.Mui-checked": {
                                            transform: "translateX(16px)",
                                            color: "#fff",
                                            "& + .MuiSwitch-track": {
                                              backgroundColor: "#E97451",
                                              opacity: 1,
                                              border: 0,
                                            },
                                            "& .MuiSwitch-thumb": {
                                              backgroundColor: "#fff",
                                              width: 14,
                                              height: 14,
                                              backgroundImage: "url('/assets/locked.png')",
                                              backgroundSize: "16px 16px",
                                              backgroundRepeat: "no-repeat",
                                              backgroundPosition: "top 0.5px left 0.4px",
                                            },
                                            "&.Mui-disabled + .MuiSwitch-track": {
                                              opacity: 0.5,
                                            },
                                          },
                                        },
                                        "& .MuiSwitch-thumb": {
                                          backgroundColor: "#fff",
                                          boxSizing: "border-box",
                                          width: 14,
                                          height: 14,
                                          borderRadius: "50%",
                                          transition: "width 0.2s, height 0.2s",
                                          backgroundImage: mapping.required ? "url('/assets/locked.png')" : "none",
                                          backgroundSize: "16px 16px",
                                          backgroundRepeat: "no-repeat",
                                          backgroundPosition: "top 0.5px left 0.4px",
                                        },
                                        "& .MuiSwitch-track": {
                                          borderRadius: 20,
                                          backgroundColor: "#B9B5B3",
                                          opacity: 1,
                                          transition: "background-color 0.5s",
                                        },
                                      }}
                                    />
                                  </div>
                                  <div
                                    className={styles.settingsOption}
                                    style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
                                  >
                                    <span>Mappable Field</span>
                                    <Switch
                                      checked={!!mapping.mappingEnabled}
                                      onChange={() => {
                                        // Find the original index in the full dataset
                                        const actualMapping = paginatedData[index]
                                        if (!actualMapping || !actualMapping.targetfield) return
                                        const originalIndex = targetData[value].fieldMappings.findIndex(
                                          (m) => m.targetfield === actualMapping.targetfield,
                                        )
                                        if (originalIndex === -1) return
                                        // Update in targetData
                                        const newTargetData = [...targetData]
                                        newTargetData[value] = {
                                          ...newTargetData[value],
                                          fieldMappings: [...newTargetData[value].fieldMappings],
                                        }
                                        const newMappingEnabled = !newTargetData[value].fieldMappings[originalIndex].mappingEnabled
                                        newTargetData[value].fieldMappings[originalIndex] = {
                                          ...newTargetData[value].fieldMappings[originalIndex],
                                          mappingEnabled: newMappingEnabled,
                                          status: newMappingEnabled ? "success" : "error",
                                        }
                                        setTargetData(newTargetData)
                                        // Update in targets as well for consistency
                                        const newTargets = [...targets]
                                        newTargets[value] = {
                                          ...newTargets[value],
                                          fieldMappings: [...newTargets[value].fieldMappings],
                                        }
                                        newTargets[value].fieldMappings[originalIndex] = {
                                          ...newTargets[value].fieldMappings[originalIndex],
                                          mappingEnabled: newMappingEnabled,
                                          status: newMappingEnabled ? "success" : "error",
                                        }
                                        setTargets(newTargets)
                                      }}
                                      sx={{
                                        width: 36,
                                        height: 18,
                                        padding: 0,
                                        "& .MuiSwitch-switchBase": {
                                          padding: 0,
                                          margin: "2px",
                                          transitionDuration: "300ms",
                                          "&.Mui-checked": {
                                            transform: "translateX(16px)",
                                            color: "#fff",
                                            "& + .MuiSwitch-track": {
                                              backgroundColor: "#E97451",
                                              opacity: 1,
                                              border: 0,
                                            },
                                            "& .MuiSwitch-thumb": {
                                              backgroundColor: "#fff",
                                              width: 14,
                                              height: 14,
                                            },
                                            "&.Mui-disabled + .MuiSwitch-track": {
                                              opacity: 0.5,
                                            },
                                          },
                                        },
                                        "& .MuiSwitch-thumb": {
                                          backgroundColor: "#fff",
                                          boxSizing: "border-box",
                                          width: 14,
                                          height: 14,
                                          borderRadius: "50%",
                                          transition: "width 0.2s, height 0.2s",
                                        },
                                        "& .MuiSwitch-track": {
                                          borderRadius: 20,
                                          backgroundColor: "#B9B5B3",
                                          opacity: 1,
                                          transition: "background-color 0.5s",
                                        },
                                      }}
                                    />
                                  </div>
                                  {mapping.mappingEnabled ? (
                                    <div
                                      className={styles.settingsOption}
                                      onClick={() => {
                                        // Show Value Mapping dialog if mappingEnabled is ON
                                        if (targetData[value].name && mapping.mappingTarget === "Groups") {
                                         
                                          const updatedTarget = selectedEntityData?.mappingTargets.find(
                                            (item) => item.name === "Groups",
                                          )
                                          handleValueMappingDialog(mapping, updatedTarget)
                                        } else if (targetData[value].name && mapping.mappingTarget === "Agents") {
                                          const updatedTarget = selectedEntityData?.mappingTargets.find(
                                            (item) => item.name === "Agents",
                                          )
                                          handleValueMappingDialog(mapping, updatedTarget)
                                        } else {
                                          handleValueMappingDialog(mapping, targetData[value])
                                        }
                                      }}
                                    >
                                      Value Mapping
                                    </div>
                                  ) : (
                                    <div
                                      className={styles.settingsOption}
                                      onClick={() => handleOverrideValuesDialog(mapping, targetData[value])}
                                    >
                                      Override values
                                    </div>
                                  )}
                                  <div
                                    className={styles.settingsOption}
                                    onClick={() => handleDefaultMappingDialog(mapping, targetData[value])}
                                  >
                                    Default Values
                                  </div>
                                  {(!mapping.mappingEnabled && (!mapping.sourcefield || mapping.sourcefield === "")) && (
                                    <div
                                      className={styles.settingsOption}
                                      onClick={() => handleCombineFieldsDialog(mapping, targetData[value])}
                                    >
                                      Combine Fields
                                    </div>
                                  )}
                                  {mapping?.isLookupEnabled && (
                                    <div
                                      className={styles.settingsOption}
                                      onClick={() => handleAdvancedMappingDialog(mapping, targetData[value])}
                                    >
                                      Advanced
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                      </tr>
                    ),
                )
              ) : (
                <tr>
                  <td colSpan={5} className={styles.noDataCell}>
                    <div className={styles.noDataFound}>No data found for the selected filter</div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          <Dialog open={openDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <ValueMapping
                close={() => setOpenDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                selectedEntityData={selectedEntityData}
                sourceMapRes={sourceMapRes}
                source={source}
                uniqueSourceValues={uniqueSourceValues}
                targetData={target}
                selectedObjectData={selectedObjectData}
                targetExeRes={targetExeRes}
                onSave={handleValueMappingSave}
              />
            </DialogContent>
          </Dialog>
          <Dialog open={openCombineFieldsDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <CombineFields
                close={() => setOpenCombineFieldsDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                onSave={handleSaveCombinedFields}
                sourceFields={filteredAttributes}
              />
            </DialogContent>
          </Dialog>
          <Dialog open={openOverrideDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <OverrideValues
                close={() => setOpenOverrideDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                onSave={handleSaveOverrideValues}
              />
            </DialogContent>
          </Dialog>
          <Dialog open={openAdvancedDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <Advanced
                close={() => setOpenAdvancedDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                onSave={handleSaveAdvancedCode}
              />
            </DialogContent>
          </Dialog>
          <Dialog open={openDefaultDialog} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <DefaultValue
                key={`${selectedMapping?.targetfield}-${target?.formData?.workspace_id || ''}`}
                close={() => setOpenDefaultDialog(false)}
                attribute={selectedMapping}
                target={selectedTarget}
                onSave={handleSaveDefaultValue}
              />
            </DialogContent>
          </Dialog>
          <div style={{ display: "flex", alignItems: "center", gap: "10px", marginTop: "30px" }}>
            <span className={globalStyles.interStyle} style={{ color: "#170903", width: "80px" }}>
              Page no
            </span>
            {[...Array(totalPages)].map((_, index) => (
              <button
                key={index + 1}
                className={` ${currentPage === index + 1 ? globalStyles.activePage : globalStyles.pageButton}`}
                onClick={() => handlePageChange(index + 1)}
              >
                {String(index + 1).padStart(2, "0")}
              </button>
            ))}
            <span className={globalStyles.interStyle} style={{ color: "#170903", marginLeft: "30px" }}>
              Show
            </span>
            {showPages.map((pageLimit, index) => (
              <button
                key={index}
                className={`${itemsPerPage === pageLimit ? globalStyles.activePage : globalStyles.pageButton}`}
                onClick={() => handleShowPage(pageLimit)}
              >
                {String(pageLimit).padStart(2, "0")}
              </button>
            ))}
            <div style={{ display: "flex", justifyContent: "flex-end", width: "100%", marginBottom: "30px" }}>
              <button
                className={globalStyles.mainButton}
                style={{
                  marginLeft: "auto",
                  margin: "0",
                  opacity: isSaving ? 0.7 : 1,
                  cursor: isSaving ? "not-allowed" : "pointer",
                }}
                onClick={goToNextStep}
                disabled={isSaving}
              >
                {isSaving ? savingMessage : "Confirm mapping & Review"}
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* Smart Auto-Map Confirmation Dialog */}
      <Dialog
        open={showSmartAutoMapConfirm}
        maxWidth="sm"
        fullWidth
        onClose={() => setShowSmartAutoMapConfirm(false)}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#1a0d07", 
            fontFamily: "Inter, sans-serif",
            color: "#fff",
            padding: 0,
          },
        }}
      >
        <div style={{ padding: "32px 24px 24px 24px", textAlign: "center" }}>
          <div
            style={{
              fontSize: 20,
              fontWeight: 600,
              marginBottom: 12,
              fontFamily: "Inter, sans-serif",
              color: "#ff7a3c", 
              letterSpacing: 0.2,
            }}
          >
            Confirm Smart Auto-Map
          </div>
          <div style={{ fontSize: 15, color: "#fff", marginBottom: 28, fontFamily: "Inter, sans-serif" }}>
            Running Smart Auto-Map will <b style={{ color: '#ff7a3c' }}>overwrite existing mappings</b> and may change mapped fields. Are you sure you want to continue?
          </div>
          <div style={{ display: "flex", justifyContent: "center", gap: 16 }}>
            <button
              onClick={() => setShowSmartAutoMapConfirm(false)}
              style={{
                fontFamily: "Inter, sans-serif",
                color: "#fff",
                background: "#1a0d07",
                border: "1px solid #fff",
                borderRadius: 6,
                padding: "8px 24px",
                fontWeight: 500,
                fontSize: 15,
                cursor: "pointer",
                transition: 'background 0.2s',
              }}
            >
              Cancel
            </button>
            <button
              onClick={executeSmartAutoMap}
              style={{
                fontFamily: "Inter, sans-serif",
                color: "black",
                background: "#ff7a3c",
                border: "none",
                borderRadius: 6,
                padding: "8px 24px",
                fontWeight: 500,
                fontSize: 15,
                cursor: "pointer",
                transition: 'background 0.2s',
              }}
            >
              Continue
            </button>
          </div>
        </div>
      </Dialog>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        style={{
          fontFamily: "Inter",
        }}
        toastStyle={{
          fontFamily: "Inter",
          fontWeight: "bold",
        }}
      />
    </div>
  )
}
