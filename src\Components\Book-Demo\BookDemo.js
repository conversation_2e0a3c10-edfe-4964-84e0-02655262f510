import { useEffect, useRef, useState } from "react"
import globalStyles from "../globalStyles.module.css"
import styles from "./BookDemo.module.css"
import { FormControl, MenuItem, Select } from "@mui/material"
import DatePicker from "react-datepicker"
import { HiChevronRight } from "react-icons/hi"
import { getUser, sendSlackNotification } from "../apiService"
import LoaderSpinner from "../loaderspinner"
import { toast, ToastContainer } from "react-toastify"
import "react-toastify/dist/ReactToastify.css"

export default function BookDemo({ close }) {
  const [email, setEmail] = useState(localStorage.getItem("email"))

  const countryCodes = [
    { code: "+91", name: "India" },
    { code: "+1", name: "United States" },
    { code: "+44", name: "United Kingdom" },
    { code: "+61", name: "Australia" },
    { code: "+1", name: "Canada" },
  ]
  const [selectedRange, setSelectedRange] = useState("")
  const [selectedTimeZone, setSelectedTimeZone] = useState("")
  const [selectedTime, setSelectedTime] = useState("10:00")
  const [selectedPeriod, setSelectedPeriod] = useState("AM")
  const [isLoading, setIsLoading] = useState(false)
  const apiCalledRef = useRef(null)

  // Add validation states
  const [errors, setErrors] = useState({
    name: "",
    email: "",
    phone_number: "",
    company_name: "",
    records: "",
    timeZone: "",
    time: "",
    period: "",
  })

  // Validation functions
  const validateEmail = (email) => {
    const re =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return re.test(String(email).toLowerCase())
  }

  const validatePhone = (phone) => {
    const re = /^\d{10}$/
    return re.test(String(phone))
  }

  // Function to get timezone offset in minutes
  const getTimezoneOffset = (timezone) => {
    const timezoneOffsets = {
      'PST': -8 * 60,      // UTC-8
      'EST': -5 * 60,      // UTC-5
      'GMT': 0,            // UTC±0
      'CET': 1 * 60,       // UTC+1
      'GST': 4 * 60,       // UTC+4
      'IST': 5.5 * 60,     // UTC+5:30
      'SGT': 8 * 60,       // UTC+8
      'JST': 9 * 60,       // UTC+9
      'AEST': 11 * 60      // UTC+11
    }
    return timezoneOffsets[timezone] || 0
  }

  // Function to detect user's timezone and map to our supported timezones
  const detectUserTimezone = () => {
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const currentOffset = new Date().getTimezoneOffset() * -1 // getTimezoneOffset returns negative values
    
    // Map common timezones to our supported ones
    const timezoneMapping = {
      // IST timezone detection
      'Asia/Kolkata': 'IST',
      'Asia/Calcutta': 'IST',
      
      // GMT timezone detection
      'Europe/London': 'GMT',
      'GMT': 'GMT',
      'UTC': 'GMT',
      
      // PST timezone detection
      'America/Los_Angeles': 'PST',
      'America/Vancouver': 'PST',
      'US/Pacific': 'PST',
      
      // EST timezone detection
      'America/New_York': 'EST',
      'America/Toronto': 'EST',
      'US/Eastern': 'EST',
      
      // CET timezone detection
      'Europe/Berlin': 'CET',
      'Europe/Paris': 'CET',
      'Europe/Rome': 'CET',
      'Europe/Madrid': 'CET',
      
      // SGT timezone detection
      'Asia/Singapore': 'SGT',
      'Asia/Kuala_Lumpur': 'SGT',
      
      // JST timezone detection
      'Asia/Tokyo': 'JST',
      'Japan': 'JST',
      
      // AEST timezone detection
      'Australia/Sydney': 'AEST',
      'Australia/Melbourne': 'AEST',
      'Australia/Brisbane': 'AEST',
    }
    
    // First try to match by timezone name
    if (timezoneMapping[userTimezone]) {
      return timezoneMapping[userTimezone]
    }
    
    // If no direct match, try to match by offset
    const offsetToTimezone = {
      330: 'IST',    // UTC+5:30
      0: 'GMT',      // UTC+0
      '-480': 'PST', // UTC-8
      '-300': 'EST', // UTC-5
      60: 'CET',     // UTC+1
      240: 'GST',    // UTC+4
      480: 'SGT',    // UTC+8
      540: 'JST',    // UTC+9
      660: 'AEST',   // UTC+11
    }
    
    return offsetToTimezone[currentOffset] || 'GMT' // Default to GMT if no match
  }

  // Function to check if a specific time option should be disabled
  const isTimeOptionDisabled = (hour, period) => {
    if (!selectedDate || !selectedTimeZone) return false
    
    // Create a date object for the selected date and time
    const selectedDateTime = new Date(selectedDate)
    
    let selectedHours = hour
    
    // Convert to 24-hour format
    if (period === 'PM' && selectedHours !== 12) {
      selectedHours += 12
    } else if (period === 'AM' && selectedHours === 12) {
      selectedHours = 0
    }
    
    selectedDateTime.setHours(selectedHours, 0, 0, 0)
    
    // Check if it's today
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const selectedDateOnly = new Date(selectedDate)
    selectedDateOnly.setHours(0, 0, 0, 0)
    
    // If it's a future date, don't disable any times
    if (selectedDateOnly.getTime() > today.getTime()) {
      return false
    }
    
    // If it's today, check if the selected time is at least 1 hour ahead in the selected timezone
    if (selectedDateOnly.getTime() === today.getTime()) {
      const now = new Date()
      
      // Get the current time in the selected timezone
      const timezoneOffsetMinutes = getTimezoneOffset(selectedTimeZone)
      const browserOffsetMinutes = now.getTimezoneOffset() * -1
      
      // Convert current time to selected timezone
      const currentTimeInSelectedTZ = new Date(now.getTime() + ((timezoneOffsetMinutes - browserOffsetMinutes) * 60 * 1000))
      
      // Add 1 hour buffer
      const oneHourLaterInSelectedTZ = new Date(currentTimeInSelectedTZ.getTime() + 60 * 60 * 1000)
      
      // Create selected time in the same timezone context
      const selectedTimeInTZ = new Date(selectedDateTime)
      
      return selectedTimeInTZ.getTime() < oneHourLaterInSelectedTZ.getTime()
    }
    
    // For past dates, disable all times
    return true
  }

  // Function to check if selected time is at least 1 hour ahead
  const isTimeSlotValid = (date, time, period, timeZone) => {
    if (!date || !time || !period || !timeZone) return false
    
    const selectedDateTime = new Date(date)
    
    // Parse the selected time
    const [hours] = time.split(':')
    let selectedHours = parseInt(hours)
    
    // Convert to 24-hour format
    if (period === 'PM' && selectedHours !== 12) {
      selectedHours += 12
    } else if (period === 'AM' && selectedHours === 12) {
      selectedHours = 0
    }
    
    selectedDateTime.setHours(selectedHours, 0, 0, 0)
    
    // Check if it's today
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const selectedDateOnly = new Date(date)
    selectedDateOnly.setHours(0, 0, 0, 0)
    
    // If it's a future date, it's valid
    if (selectedDateOnly.getTime() > today.getTime()) {
      return true
    }
    
    // If it's today, check if the selected time is at least 1 hour ahead in the selected timezone
    if (selectedDateOnly.getTime() === today.getTime()) {
      const now = new Date()
      
      // Get the current time in the selected timezone
      const timezoneOffsetMinutes = getTimezoneOffset(timeZone)
      const browserOffsetMinutes = now.getTimezoneOffset() * -1
      
      // Convert current time to selected timezone
      const currentTimeInSelectedTZ = new Date(now.getTime() + ((timezoneOffsetMinutes - browserOffsetMinutes) * 60 * 1000))
      
      // Add 1 hour buffer
      const oneHourLaterInSelectedTZ = new Date(currentTimeInSelectedTZ.getTime() + 60 * 60 * 1000)
      
      // Create selected time in the same timezone context
      const selectedTimeInTZ = new Date(selectedDateTime)
      
      return selectedTimeInTZ.getTime() >= oneHourLaterInSelectedTZ.getTime()
    }
    
    // For past dates, it's not valid
    return false
  }

  const validateField = (field, value) => {
    let errorMessage = ""

    switch (field) {
      case "name":
        if (!value.trim()) errorMessage = "Name is required"
        break
      case "email":
        if (!value.trim()) errorMessage = "Email is required"
        else if (!validateEmail(value)) errorMessage = "Please enter a valid email"
        break
      case "phone_number":
        if (!value.trim()) errorMessage = "Phone number is required"
        else if (!validatePhone(value)) errorMessage = "Please enter a valid 10-digit phone number"
        break
      case "records":
        if (!value) errorMessage = "Please select number of records"
        break
      case "timeZone":
        if (!value) errorMessage = "Please select a time zone"
        break
      case "time":
        if (!value) errorMessage = "Please select a time"
        break
      case "period":
        if (!value) errorMessage = "Please select AM/PM"
        break
      default:
        break
    }

    return errorMessage
  }

  const validateForm = () => {
    const newErrors = {
      name: validateField("name", data.name),
      email: validateField("email", data.email),
      phone_number: validateField("phone_number", data.phone_number),
      records: validateField("records", selectedRange),
      timeZone: validateField("timeZone", selectedTimeZone),
      time: validateField("time", selectedTime),
      period: validateField("period", selectedPeriod),
    }

    setErrors(newErrors)

    return !Object.values(newErrors).some((error) => error !== "")
  }

  const confirm = async () => {
    if (!validateForm()) {
      return
    }

    // Additional check for time slot validation before proceeding
    if (!isTimeSlotValid(selectedDate, selectedTime, selectedPeriod, selectedTimeZone)) {
      toast.error(`Please choose a time slot at least 1 hour ahead of the current time in ${selectedTimeZone} timezone to ensure proper scheduling.`, {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      })
      return
    }

    setIsLoading(true)

    const payload = {
      name: data.name,
      email_id: data.email,
      country_code: data.country_code,
      phone_number: data.phone_number,
      company_name: data.company_name,
      number_of_records: selectedRange,
      time_zone: selectedTimeZone,
      time_slot: selectedTime + selectedPeriod,
      selected_date: selectedDate.toISOString().split("T")[0],
    }

    try {
      // Send Slack notification using the apiService function
      const result = await sendSlackNotification(payload)

      if (result.success) {
        console.log("Slack notification sent successfully")
        setIsChanged(true)
        setTimeout(() => {
          close()
        }, 3000)
      } else {
        console.error("API error:", result.error)
        alert("Failed to send notification. Please try again.")
      }
    } catch (error) {
      console.error("Error processing demo request:", error)
      alert("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSelection = (value) => {
    setSelectedRange(value)
    setErrors((prev) => ({ ...prev, records: "" }))
  }

  const [isChanged, setIsChanged] = useState(false)
  const defaultDate = new Date()
  const [selectedDate, setSelectedDate] = useState(defaultDate)

  defaultDate.setHours(10, 0, 0, 0)
  const minDate = new Date()
  const [data, setData] = useState({
    name: "",
    email: "",
    password: "",
    company_name: "",
    designation: "",
    country_code: "+91",
    phone_number: "",
  })

  const handleInputChange = (field, value) => {
    setData((prev) => ({ ...prev, [field]: value }))
    setErrors((prev) => ({ ...prev, [field]: validateField(field, value) }))
  }

  useEffect(() => {
    // Auto-detect user's timezone when component loads
    const detectedTimezone = detectUserTimezone()
    setSelectedTimeZone(detectedTimezone)
  }, [])

  useEffect(() => {
    const fetchUser = async () => {
      if (!apiCalledRef.current) {
        setIsLoading(true)
        apiCalledRef.current = true
        try {
          const res = await getUser(email)
          setIsLoading(false)
          setData((prevState) => ({
            ...prevState,
            name: res.name,
            password: res.password,
            email: res.email,
            company_name: res.company_name || "",
            designation: res.designation || "",
            country_code: res.country_code || "+91",
            phone_number: res.phone_number || "",
          }))
        } catch (error) {
          setIsLoading(false)
        }
      }
    }

    fetchUser()
  }, [])

  const CustomHeader = ({
    date,
    changeYear,
    changeMonth,
    decreaseMonth,
    increaseMonth,
    prevMonthButtonDisabled,
    nextMonthButtonDisabled,
  }) => {
    const currentYear = new Date().getFullYear()
    const years = Array.from({ length: 2 }, (_, i) => currentYear + i)

    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ]

    return (
      <div className={styles.headerStyle}>
        <div className={styles.dFlex} style={{ justifyContent: "space-between", width: "100%" }}>
          <FormControl>
            <Select
              value={date.getFullYear()}
              onChange={(e) => changeYear(Number.parseInt(e.target.value))}
              displayEmpty
              inputProps={{
                sx: {
                  padding: "0 32px 0 0 !important",
                  color: "#E37B52",
                },
              }}
              sx={{
                backgroundColor: "transparent",
                color: "white",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#514742",
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#514742",
                },
              }}
            >
              <MenuItem value="" disabled>
                Year
              </MenuItem>
              {years.map((year) => (
                <MenuItem key={year} value={year}>
                  {year}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <div>
            <FormControl>
              <Select
                value={months[date.getMonth()]}
                onChange={({ target: { value } }) => {
                  const monthIndex = months.indexOf(value)
                  changeMonth(monthIndex)
                }}
                displayEmpty
                inputProps={{
                  sx: {
                    padding: "0 32px 0 0 !important",
                    color: "#E37B52",
                    textTransform: "uppercase",
                  },
                }}
                sx={{
                  backgroundColor: "transparent",
                  color: "#E37B52",
                  textTransform: "uppercase",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#514742",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#514742",
                  },
                }}
              >
                <MenuItem value="" disabled>
                  Month
                </MenuItem>
                {months.map((month) => (
                  <MenuItem key={month} value={month}>
                    {month}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
          <button
            onClick={increaseMonth}
            disabled={nextMonthButtonDisabled}
            className={styles.headerSelectStyle}
            style={{ color: "#E37B52" }}
          >
            <HiChevronRight style={{ color: "#E37B52" }} />
          </button>
        </div>
      </div>
    )
  }

  const ErrorMessage = ({ message }) => {
    if (!message) return null

    return (
      <div
        className="error-container"
        style={{
          textAlign: "left",
          padding: "0",
          margin: "2px 0 0 150px",
        }}
      >
        <span
          style={{
            color: "#f56c6c",
            fontSize: "11px",
            fontFamily: "Inter",
          }}
        >
          {message}
        </span>
      </div>
    )
  }

  return (
    <div>
      {isLoading ? (
        <LoaderSpinner />
      ) : (
        <div>
          {!isChanged ? (
            <div>
              <div className={styles.container}>
                <style>
                  {`
          .react-datepicker {
            background-color: #170903 !important;
            color: white !important;
            border: none !important;
            font-family: inter !important;
            width: 100% !important;
          }
          .react-datepicker__month-container {
            background-color: #170903 !important;
            width: 100% !important;
          }
          .react-datepicker__header {
            background-color: #170903 !important;
            border-bottom: none !important;
            width: 100% !important;
          }
          .react-datepicker__day-names {
            display: flex !important;
            justify-content: space-between !important;
            width: 100% !important;
            padding: 0 10px !important;
          }
          .react-datepicker__day-name {
            color: white !important;
            margin: 0 !important;
            width: 36px !important;
          }
          .react-datepicker__month {
            margin: 0 !important;
            width: 100% !important;
          }
          .react-datepicker__week {
            display: flex !important;
            justify-content: space-between !important;
            width: 100% !important;
            padding: 0 10px !important;
          }
          .react-datepicker__day {
            color: #AAAAAA !important;
            margin: 0 !important;
            width: 36px !important;
            height: 36px !important;
            line-height: 36px !important;
            display: inline-flex !important;
            justify-content: center !important;
            align-items: center !important;
          }
          .react-datepicker__day:hover {
            background-color: #333333 !important;
           
          }
          .react-datepicker__day--selected {
            background-color: #E37B52 !important;
            color: #1A1A1A !important;
            font-weight: bold !important;
          }
          .react-datepicker__day--keyboard-selected {
            background-color: #E37B52 !important;
            color: #1A1A1A !important;
            
          }
          .react-datepicker__day--disabled {
            color: #444444 !important;
            cursor: not-allowed !important;
          }
          .react-datepicker__navigation--previous, .react-datepicker__navigation--next {
            display: none !important;
          }
          .react-datepicker__triangle {
            display: none !important;
          }
          .react-datepicker-popper {
            background-color: #1A1A1A !important;
            width: 100% !important;
          }
          .inputError{
            box-shadow: inset 0px 0px 6px #ea5822, inset 0px 0px 6px #ec3535 !important;
            outline:none;
          }
        `}
                </style>
                <div className={styles.dFlex} style={{ justifyContent: "space-between" }}>
                  <h2 className={globalStyles.headerStyle} style={{ paddingLeft: "0px" }}>
                    Book a demo
                  </h2>
                  <button className={styles.closeButton} onClick={close}>
                    ×
                  </button>
                </div>
                <div>
                  <div className={globalStyles.selectionName}>ENTER DETAILS BELOW</div>
                  <div>
                    <div style={{ marginBottom: "20px", marginTop: "20px" }}>
                      <div
                        className={styles.dFlex}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          width: "100%",
                        }}
                      >
                        <label
                          className={globalStyles.poppinsHeaderStyle}
                          style={{ width: "150px", textAlign: "left" }}
                        >
                          Your name*
                        </label>
                        <input
                          style={{ flex: "1", width: "100%" }}
                          className={`form-control ${styles.profileInput} ${errors.name ? "inputError" : ""}`}
                          type="text"
                          value={data.name}
                          placeholder="Your name"
                          onChange={(e) => handleInputChange("name", e.target.value)}
                        />
                      </div>
                      <ErrorMessage message={errors.name} />
                    </div>

                    <div style={{ marginBottom: "20px" }}>
                      <div
                        className={styles.dFlex}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          width: "100%",
                        }}
                      >
                        <label
                          className={globalStyles.poppinsHeaderStyle}
                          style={{ width: "150px", textAlign: "left" }}
                        >
                          Your Email ID*
                        </label>
                        <input
                          style={{ flex: "1", width: "100%" }}
                          className={`form-control ${styles.profileInput} ${errors.email ? "inputError" : ""}`}
                          type="text"
                          value={data.email}
                          placeholder="Your email ID"
                          onChange={(e) => handleInputChange("email", e.target.value)}
                        />
                      </div>
                      <ErrorMessage message={errors.email} />
                    </div>

                    <div style={{ marginBottom: "20px" }}>
                      <div
                        className={styles.dFlex}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          width: "100%",
                        }}
                      >
                        <label
                          className={globalStyles.poppinsHeaderStyle}
                          style={{ width: "200px", textAlign: "left" }}
                        >
                          Updates sent to*
                        </label>
                        <div style={{ display: "flex", gap: "10px", width: "100%" }}>
                          <FormControl className={globalStyles.customDropdownContainer} style={{ width: "25%" }}>
                            <Select
                              displayEmpty
                              value={data.country_code}
                              onChange={(e) => handleInputChange("country_code", e.target.value)}
                              className={globalStyles.customDropdownSelect}
                              renderValue={(selected) => (
                                <span className={globalStyles.guideName} style={{ fontSize: "14px" }}>
                                  {data.country_code || "+91"}
                                </span>
                              )}
                            >
                              {countryCodes.map((country) => (
                                <MenuItem key={country.code} value={country.code}>
                                  {country.code} ({country.name})
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                          <input
                            className={`form-control ${errors.phone_number ? "inputError" : ""}`}
                            style={{ width: "100%", marginTop: "12px" }}
                            placeholder="Enter the phone number"
                            value={data.phone_number}
                            onChange={(e) => handleInputChange("phone_number", e.target.value)}
                          />
                        </div>
                      </div>
                      <ErrorMessage message={errors.phone_number} />
                    </div>
                  </div>
                </div>
                <div>
                  <div className={globalStyles.selectionName} style={{ marginBottom: "20px" }}>
                    ENTER YOUR REQUIREMENTS
                  </div>
                  <div style={{ marginBottom: "20px" }}>
                    <div
                      className={styles.dFlex}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <label className={globalStyles.poppinsHeaderStyle} style={{ width: "150px", textAlign: "left" }}>
                        Company Name
                      </label>
                      <input
                        style={{ flex: "1", width: "60%" }}
                        className={`form-control ${styles.profileInput}`}
                        type="text"
                        placeholder="Company name"
                        value={data.company_name}
                        onChange={(e) => handleInputChange("company_name", e.target.value)}
                      />
                    </div>
                  </div>
                </div>
                <div>
                  <div style={{ marginBottom: "20px" }}>
                    <div
                      className={styles.dFlex}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <label className={globalStyles.poppinsHeaderStyle} style={{ width: "150px", textAlign: "left" }}>
                        Number of records for migration*
                      </label>
                      {["0-500 records", "500-10,000 records", "Above 10,000 records"].map((value, index) => (
                        <input
                          key={index}
                          type="button"
                          style={{
                            flex: "1",
                            width: "20%",
                            marginLeft: index !== 0 ? "5px" : "0",
                            backgroundColor: selectedRange === value ? "#EF8963" : "white",
                            cursor: "pointer",
                          }}
                          className={`form-control ${errors.records && index === 0 ? "inputError" : ""}`}
                          value={value}
                          onClick={() => handleSelection(value)}
                        />
                      ))}
                    </div>
                    <ErrorMessage message={errors.records} />
                  </div>
                </div>
                <div className={globalStyles.poppinsHeaderStyle}>Select your preferred slot</div>
                <div style={{ marginBottom: "20px", width: "100%", backgroundColor: "#170903" }}>
                  <DatePicker
                    selected={selectedDate}
                    onChange={(date) => {
                      setSelectedDate(date)
                      // Revalidate time when date changes
                      if (selectedTime && selectedPeriod) {
                        const timeError = validateField("time", selectedTime)
                        if (timeError) {
                          setErrors((prev) => ({ ...prev, time: timeError }))
                        } else {
                          setErrors((prev) => ({ ...prev, time: "" }))
                        }
                      }
                    }}
                    inline
                    renderCustomHeader={CustomHeader}
                    calendarClassName="dark-calendar"
                    fixedHeight
                    minDate={minDate}
                    dayClassName={() => "custom-day"}
                    monthClassName={() => "custom-month"}
                    weekDayClassName={() => "custom-weekday"}
                    className="full-width-datepicker"
                  />
                </div>
                <div style={{ marginTop: "20px", marginBottom: "10px" }}>
                  <div className={styles.dFlex} style={{ gap: "10px" }}>
                    <label className={globalStyles.poppinsHeaderStyle} style={{ width: "150px", textAlign: "left" }}>
                      Select time slot
                    </label>
                    <div className={styles.dropdownContainer} style={{ flex: 1 }}>
                      <FormControl fullWidth error={Boolean(errors.timeZone)}>
                        <Select
                          value={selectedTimeZone}
                          onChange={(e) => {
                            setSelectedTimeZone(e.target.value)
                            setErrors((prev) => ({ ...prev, timeZone: "" }))
                            // Revalidate time when timezone changes
                            if (selectedTime && selectedPeriod) {
                              const timeError = validateField("time", selectedTime)
                              if (timeError) {
                                setErrors((prev) => ({ ...prev, time: timeError }))
                              } else {
                                setErrors((prev) => ({ ...prev, time: "" }))
                              }
                            }
                          }}
                          displayEmpty
                          inputProps={{
                            sx: {
                              padding: "10px !important",
                              color: "white",
                            },
                          }}
                          sx={{
                            backgroundColor: "transparent",
                            color: "white",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#514742",
                            },
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#514742",
                            },
                          }}
                        >
                          <MenuItem value="" disabled>
                            Zone
                          </MenuItem>
                          <MenuItem value="PST">PST (UTC-8)</MenuItem>
                          <MenuItem value="EST">EST (UTC-5)</MenuItem>
                          <MenuItem value="GMT">GMT (UTC ±0)</MenuItem>
                          <MenuItem value="CET">CET (UTC +1)</MenuItem>
                          <MenuItem value="GST">GST (UTC +4)</MenuItem>
                          <MenuItem value="IST">IST (UTC +5:30)</MenuItem>
                          <MenuItem value="SGT">SGT (UTC +8)</MenuItem>
                          <MenuItem value="JST">JST (UTC +9)</MenuItem>
                          <MenuItem value="AEST">AEST (UTC +11)</MenuItem>
                        </Select>
                      </FormControl>
                    </div>

                    <div className={styles.dropdownContainer1} style={{ flex: 2 }}>
                      <FormControl fullWidth error={Boolean(errors.time)}>
                        <Select
                          value={selectedTime}
                          onChange={(e) => {
                            setSelectedTime(e.target.value)
                            setErrors((prev) => ({ ...prev, time: "" }))
                            // Validate the selected time immediately
                            const timeError = validateField("time", e.target.value)
                            if (timeError) {
                              setErrors((prev) => ({ ...prev, time: timeError }))
                            }
                          }}
                          displayEmpty
                          inputProps={{
                            sx: {
                              padding: "10px !important",
                              color: "white",
                            },
                          }}
                          sx={{
                            backgroundColor: "transparent",
                            color: "white",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#514742",
                            },
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#514742",
                            },
                          }}
                        >
                          <MenuItem value="" disabled>
                            Select time
                          </MenuItem>
                          {Array.from({ length: 12 }, (_, i) => {
                            const hour = i + 1
                            const isAMDisabled = isTimeOptionDisabled(hour, 'AM')
                            const isPMDisabled = isTimeOptionDisabled(hour, 'PM')
                            const shouldDisable = selectedPeriod === 'AM' ? isAMDisabled : selectedPeriod === 'PM' ? isPMDisabled : (isAMDisabled && isPMDisabled)
                            
                            return (
                              <MenuItem 
                                key={hour} 
                                value={`${hour}:00`}
                                disabled={shouldDisable}
                                sx={{
                                  color: shouldDisable ? '#666666 !important' : 'inherit',
                                  '&.Mui-disabled': {
                                    color: '#666666 !important'
                                  }
                                }}
                              >
                                {hour}:00 {shouldDisable ? '(Not available)' : ''}
                              </MenuItem>
                            )
                          })}
                        </Select>
                      </FormControl>
                    </div>

                    <div className={styles.dropdownContainer} style={{ flex: 1 }}>
                      <FormControl fullWidth error={Boolean(errors.period)}>
                        <Select
                          value={selectedPeriod}
                          onChange={(e) => {
                            setSelectedPeriod(e.target.value)
                            setErrors((prev) => ({ ...prev, period: "", time: "" }))
                            // Clear time validation when period changes
                            if (selectedTime) {
                              const timeError = validateField("time", selectedTime)
                              if (timeError) {
                                setErrors((prev) => ({ ...prev, time: timeError }))
                              }
                            }
                          }}
                          displayEmpty
                          inputProps={{
                            sx: {
                              padding: "10px !important",
                              color: "white",
                            },
                          }}
                          sx={{
                            backgroundColor: "transparent",
                            color: "white",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#514742",
                            },
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#514742",
                            },
                          }}
                        >
                          <MenuItem value="" disabled>
                            AM/PM
                          </MenuItem>
                          <MenuItem value="AM">AM</MenuItem>
                          <MenuItem value="PM">PM</MenuItem>
                        </Select>
                      </FormControl>
                    </div>
                  </div>
                  <div className={styles.dFlex} style={{ gap: "10px", marginTop: "3px" }}>
                    <div style={{ width: "150px" }}></div>
                    <div style={{ flex: 1 }}>
                      <ErrorMessage message={errors.timeZone} />
                    </div>
                    <div style={{ flex: 2 }}>
                      <ErrorMessage message={errors.time} />
                    </div>
                    <div style={{ flex: 1 }}>
                      <ErrorMessage message={errors.period} />
                    </div>
                  </div>
                </div>
                <button
                  className={globalStyles.mainButton}
                  style={{ width: "100%", marginTop: "20px" }}
                  onClick={confirm}
                  disabled={isLoading}
                >
                  {isLoading ? "Scheduling..." : "Schedule my demo"}
                </button>
              </div>
            </div>
          ) : (
            <div className={styles.container} style={{ margin: "20px" }}>
              <div className={`${globalStyles.selectionName} ${styles.dFlex}`} style={{ justifyContent: "center" }}>
                DEMO HAS BEEN SCHEDULED!
              </div>
              <div
                className={globalStyles.interSummaryStyle}
                style={{
                  color: "#F8F8F7",
                  margin: "auto",
                  display: "flex",
                  justifyContent: "center",
                  marginTop: "10px",
                }}
              >
                One of our experts will reach out to you shortly!
              </div>
            </div>
          )}
        </div>
      )}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </div>
  )
}
