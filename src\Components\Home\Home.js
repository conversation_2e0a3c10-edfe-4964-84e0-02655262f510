import { useEffect, useState } from 'react';
import "./Home.css";
import Sidebar from "../Sidebar/Sidebar";
import "../global_styles.css";
import { GlobeIcon, SearchIcon } from '@heroicons/react/solid';
import GuideMe from "../Guide-Me/Guide-Me";
import ActivityLog from "../Activity-log/Activity-log";
import TemplateLog from "../Template-log/Template-log";
import GuideTour from "../Guide-tour/Guide-tour";
import globalStyles from "../globalStyles.module.css"
import { useLocation, useNavigate } from "react-router-dom";
import { displayArticle } from "../../Helper/helper";

export default function Home() {
    const location = useLocation();
    const newUser = location.state?.newUser || false;
    const [isNewUser, setIsNewUser] = useState(false);
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
        return localStorage.getItem('isSidebarCollapsed') === 'true';
    });
    const [selectedGuideIndex, setSelectedGuideIndex] = useState(null);
    const [isGuideTourOpen, setIsGuideTourOpen] = useState(false);
    const navigate = useNavigate();

    const handleGuideSelect = (guide) => {
        setSelectedGuideIndex(guide - 1);
        setIsGuideTourOpen(true);
    };

    useEffect(() => {
        setIsNewUser(newUser);
    }, [newUser]);

    const closeGuideTour = () => {
        setIsGuideTourOpen(false);
    };

    const handleStartClick = () => {
        // Clear all migration-related data from localStorage and sessionStorage
        localStorage.removeItem('dataMigrationState');
        localStorage.removeItem('sourcePlatform');
        localStorage.removeItem('targetPlatform');
        localStorage.removeItem('migrationProgress');
        localStorage.removeItem('migrationId');
        localStorage.removeItem('sampleMigrationButtonClicked');
        localStorage.removeItem('migrationInProgress');
        localStorage.removeItem('migrationTag');

        sessionStorage.removeItem('dataMigrationState');
        sessionStorage.removeItem('selectedSourcePlatform');
        sessionStorage.removeItem('sourceSelectionData');

        // Use navigate instead of direct URL change to pass state
        navigate('/data-migration', { state: { newTemplate: true, clearAll: true } });
    };

    return (
        <div>
            <Sidebar
                isCollapsed={isSidebarCollapsed}
                setIsCollapsed={setIsSidebarCollapsed}
            />
            <div className={`main-section ${isSidebarCollapsed ? 'expanded' : ''} ${isGuideTourOpen ? 'guide-bg' : ''}`}>
                <div className={globalStyles.searchBarContainer} style={{ width: "100%" }}>
                    <div className={globalStyles.searchBar}>
                        {/* Search functionality temporarily hidden
                            <div className={globalStyles.searchWrapper}>
                                <SearchIcon className={globalStyles.searchIcon}/>
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className={globalStyles.searchInput}
                                    onFocus={(e) => {
                                        e.target.style.width = '200px';
                                        e.target.placeholder = 'Typing...';
                                    }}
                                    onBlur={(e) => {
                                        e.target.style.width = '80px';
                                        e.target.placeholder = 'Search...';
                                    }}
                                />
                            </div>
                            */}

                        <div className="search-wrapper">
                            <GlobeIcon className="search-icon" />
                            <input type="text" value="Eng" className={globalStyles.searchInput} readOnly />
                        </div>
                    </div>
                </div>


                {isNewUser ? (
                    <div>
                        <div className="banner-container">
                            <img src="/assets/HomeBanner.png" alt="Banner" className="banner-image" />
                            <div className="welcome">
                                <div className="welcome-msg">Welcome!</div>
                                <div className="welcome-text">Simplify your data migration journey with migrateGenie
                                </div>
                                <div style={{ display: "flex", gap: "15px", marginTop: "15px", alignItems: "center" }}>
                                    <button
                                        className="main-button"
                                        style={{ width: "300px" }}
                                        onClick={handleStartClick}
                                    >
                                        Let's get started!
                                    </button>
                                    <button
                                        className="main-button"
                                        style={{ 
                                            width: "300px", // match Let's get started button
                                            display: "flex", 
                                            alignItems: "center", 
                                            justifyContent: "center", 
                                            gap: "8px",
                                            fontWeight: 600
                                        }}
                                        onClick={() => displayArticle("Creating & using templates for faster migration")}
                                    >
                                        ▶ Watch Video
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div style={{ paddingLeft: "24px", paddingRight: "24px", width: "100%", boxSizing: "border-box" }}>
                            <GuideMe />
                        </div>
                    </div>
                ) : (
                    <div>
                        <div className="banner-container">
                            <img src="/assets/HomeBanner.png" alt="Banner" className="banner-image" />
                            <div className="welcome">
                                <div className="welcome-msg">Welcome Back!</div>
                                <div className="welcome-text">Simplify your data migration journey with migrateGenie
                                </div>
                                <div style={{ display: "flex", gap: "15px", marginTop: "15px", alignItems: "center" }}>
                                    <button
                                        className="main-button"
                                        onClick={handleStartClick}
                                    >
                                        Let's get started!
                                    </button>
                                    <button
                                        className="main-button"
                                        style={{ 
                                            display: "flex", 
                                            alignItems: "center", 
                                            justifyContent: "center", 
                                            gap: "8px",
                                            width: "300px", 
                                            fontWeight: 500
                                        }}
                                        onClick={() => displayArticle("Creating & using templates for faster migration")}
                                    >
                                        ▶ Watch Video
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div style={{ paddingTop: "20px", paddingLeft: "24px" }}>
                            <TemplateLog />
                        </div>
                        <div style={{ paddingTop: "20px", paddingLeft: "24px", paddingBottom: "15px" }}>
                            <ActivityLog />
                        </div>

                        <hr className="divider" />
                        <div style={{ paddingLeft: "24px", paddingRight: "24px", width: "100%", boxSizing: "border-box" }}>
                            <GuideMe onGuideSelect={handleGuideSelect} />
                            {isGuideTourOpen && <GuideTour
                                isOpen={isGuideTourOpen}
                                onClose={closeGuideTour}
                                selectedGuideIndex={selectedGuideIndex}
                            />}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}