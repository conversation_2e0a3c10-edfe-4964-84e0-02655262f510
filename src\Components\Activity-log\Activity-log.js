import React, { useEffect, useState } from "react";
import "./Activity-log.css";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { getActivityLog } from "../apiService";
import LoaderSpinner from "../loaderspinner";
import { useNavigate } from "react-router-dom";
import styles from "../ActivityandAnalytics/activity-and-analytics.module.css";

const ActivityLog = () => {
  const [view, setView] = useState('week');
  const [weeklyData, setWeeklyData] = useState([]);
  const [email, setEmail] = useState(localStorage.getItem('email'));
  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Default data for new users with no activity
  const defaultWeeklyData = [
    { name: '<PERSON>', usage: 0 },
    { name: 'Tu<PERSON>', usage: 0 },
    { name: 'Wed', usage: 0 },
    { name: 'Thu', usage: 0 },
    { name: 'Fri', usage: 0 },
    { name: 'Sat', usage: 0 },
    { name: 'Sun', usage: 0 }
  ];

  const quarterlyData = [
    { name: 'Jan', usage: 5200 },
    { name: 'Feb', usage: 6100 },
    { name: 'Mar', usage: 7300 }
  ];

  useEffect(() => {
    const fetchActivityLogs = async () => {
      setIsLoading(true);
      try {
        const userEmail = email;
        const response = await getActivityLog(userEmail);

        if (response && response.length > 0) {
          const chartData = processActivityLogs(response);
          setWeeklyData(chartData);
          
          // Sort activities by timestamp (newest to oldest)
          const sortedActivities = [...response].sort((a, b) => 
            new Date(a.timestamp) - new Date(b.timestamp)
          );
          
          // Take the 5 most recent activities
          setActivities(sortedActivities.slice(-5));
        } else {
          // Handle empty response - set default data for new users
          setWeeklyData(defaultWeeklyData);
          setActivities([]);
        }
      } catch (err) {
        console.error("Error fetching activity logs:", err);
        // Set default data in case of error
        setWeeklyData(defaultWeeklyData);
        setActivities([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchActivityLogs();
  }, [email]);

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const then = new Date(timestamp);
    const diffInMs = now - then;

    const diffInSeconds = Math.floor(diffInMs / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hours ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} days ago`;
    }
  };

  const processActivityLogs = (logs) => {
    // Create an array of the last 7 days
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      last7Days.push({
        date,
        name: date.toLocaleDateString("en-US", { weekday: "short" }),
        usage: 0
      });
    }

    // Count activities for each day
    logs.forEach(log => {
      const logDate = new Date(log.timestamp);
      // Only count logs from the last 7 days
      const dayIndex = last7Days.findIndex(day => 
        day.date.getDate() === logDate.getDate() &&
        day.date.getMonth() === logDate.getMonth() &&
        day.date.getFullYear() === logDate.getFullYear()
      );
      if (dayIndex !== -1) {
        last7Days[dayIndex].usage += 1;
      }
    });

    // Format data for chart
    return last7Days.map(day => ({
      name: day.name,
      usage: day.usage
    }));
  };

  return (
    <div className="activity-log-container">
      <div className="section">
        <div className="header-s" style={{ display: "flex" }}>
          <h2 className="title">USAGE TIMELINE</h2>
          <div className="view-buttons">
            <button
              onClick={() => setView('week')}
              className={`view-button ${view === 'week' ? 'active' : ''}`}
            >
              7-Day Activity
            </button>
          </div>
        </div>

        <div className="chart-container">
          <ResponsiveContainer>
            <AreaChart
              data={view === 'week' ? weeklyData : quarterlyData}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient id="colorUsage" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#DCDAD980" />
                  <stop offset="95%" stopColor="#DCDAD980" />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                dy={10}
                tick={{ fontSize: '14px', fontFamily: 'Inter', fontWeight: '600', fill: '#746B68' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                dx={-10}
                tick={{ fontSize: '14px', fontFamily: 'Poppins', fontWeight: '400', fill: '#B9B5B3' }}
                domain={[0, dataMax => Math.max(60, dataMax)]} // Set the maximum value to 60 or dataMax, whichever is greater
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontFamily: "inter",
                }}
              />
              <Area
                type="monotone"
                dataKey="usage"
                stroke="#EF8963"
                strokeWidth={4}
                fillOpacity={1}
                fill="url(#colorUsage)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
      <div className="section">
        <table className="activity-table">
          <thead>
            <tr>
              <th>User</th>
              <th>Activity log</th>
              <th>Time stamp</th>
            </tr>
          </thead>
          <tbody style={{ width: "100%" }}>
            {isLoading ? (
              <tr>
                <td colSpan="3" style={{ textAlign: "center", padding: "40px 0" }}>
                  <div style={{ display: "flex", justifyContent: "center" }}>
                    <LoaderSpinner height={70} fullpage={false} />
                  </div>
                </td>
              </tr>
            ) : activities.length === 0 ? (
              <tr>
                <td colSpan="3" style={{ textAlign: "center", padding: "40px 0" }}>
                  <div>No activity found. Start using the application to see your activities here.</div>
                </td>
              </tr>
            ) : (
              activities.map((activity, index) => (
                <tr key={index}>
                  <td>
                    <img src="/assets/profile-orange.png" alt="User Avatar" className="userAvatar" />
                  </td>
                  <td>{activity.activity}</td>
                  <td>{formatTimestamp(activity.timestamp)}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
        <button className="main-button" onClick={() => navigate('/activity-and-analytics')}>Go to Activity</button>
      </div>
    </div>
  );
};

export default ActivityLog;
