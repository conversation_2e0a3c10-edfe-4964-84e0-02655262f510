import React, { useState, useEffect } from "react";
import "../global_styles.css";
import styles from "./Sign-in.module.css";
import { EyeIcon, EyeOffIcon } from '@heroicons/react/solid';
import { useNavigate, useLocation } from 'react-router-dom';
import Header from "../Header/Header";
import { postActivity, signUpOrSignInUser } from "../apiService";
import CryptoJS from "crypto-js";
import globalStyles from "../globalStyles.module.css";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useSession } from "../../SessionContext"; // Import useSession
import Intercom from "@intercom/messenger-js-sdk";
import config from '../../Config/config.json';

export default function SignIn() {
    const [formSubmitted, setFormSubmitted] = useState(false);
    const [passwordVisible, setPasswordVisible] = useState(false);
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const { login } = useSession(); // Access the login function from SessionContext
    const trustedLogos = config.TRUSTED_BY_LOGOS;
    const navigate = useNavigate();
    const location = useLocation();

    // URL protection - redirect if accessed via sign-up URL
    useEffect(() => {
        if (location.pathname === '/sign-up' || window.location.href.includes('/sign-up')) {
            navigate('/sign-up', { replace: true });
            return;
        }
    }, [location.pathname, navigate]);

    // Don't render component if on sign-up URL
    if (location.pathname === '/sign-up' || window.location.href.includes('/sign-up')) {
        return null;
    }

    const togglePassword = () => {
        setPasswordVisible(!passwordVisible);
    };

    const signUpClick = () => {
        navigate('/sign-up');
    }

    const forgotPasswordClick = () => {
        navigate('/forgotpassword');
    }

    const encrypt = (text) => {
        console.log(text);
        const secretKey = CryptoJS.enc.Utf8.parse("sgmg22025sgmg220"); // 16-byte key
        const iv = CryptoJS.enc.Utf8.parse("1234567890123456"); // 16-byte IV

        return CryptoJS.AES.encrypt(text, secretKey, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }).toString();
    };

    const decrypt = (encryptedText) => {
        const secretKey = "sgmg22025";
        const bytes = CryptoJS.AES.decrypt(encryptedText, secretKey);
        return bytes.toString(CryptoJS.enc.Utf8);
    }

    const getUserFun = async (email) => {
        try {
            setIsLoading(true);
            const payload = {
                "email": email,
                "password": encrypt(password)
            }

            const res = await signUpOrSignInUser('login', payload);

            if (res.status === 'Valid user') {
                const activityPayload = {
                    email: email,
                    activity: 'User Logged In',
                    // timestamp: Date.now(),
                }
                postActivity(activityPayload);
                // Store user data in session context
                // const userData = { email }; // Add more user data if returned by API
                login(email); // Use the login function from SessionContext

                // Intercom initialization
                console.log("User Email:", email);
                const secretKey = config.INTERCOM_KEY;
                const hash = CryptoJS.HmacSHA256(email, secretKey).toString(CryptoJS.enc.Hex);
                const intercomConfig = {
                    app_id: config.INTERCOM_APP_ID,
                    user_id: email,
                    name: email,
                    email: email,
                    user_hash: hash
                };
                Intercom(intercomConfig);

                setIsLoading(false);
                navigate('/home', { state: { newUser: false } });
            }

        } catch (error) {
            setIsLoading(false);

            toast.error(error?.response.data.status || "Something went wrong!", {
                position: "top-right",
            });
        }
    }

    const handleSignIn = (e) => {
        e.preventDefault();
        setFormSubmitted(true);
        if (email && password && /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email)) {
            getUserFun(email);
        }
    };

    return (
        <div className="d-flex flex-column">
            <div>
                <Header />
            </div>

            <div className={styles.mainContainer}>
                <div className={styles.signInContainer}>
                    <div className={styles.leftContent}>
                        <div className={globalStyles.headerStyle}>
                            <div style={{ fontSize: '45px' }}>Effortless Data Migration</div>
                            <div style={{ fontSize: '45px' }}>Automated!</div>
                        </div>
                        <div className={globalStyles.interSummaryStyle} style={{ padding: '20px' }}>
                            With <b>migrateGenie</b>, move your data between platforms with confidence.
                            Automate mapping, track progress and ensure secure transactions - no headaches.
                        </div>
                        <img src="/assets/Login Animation.gif" alt="image" className={styles.loginImage} />
                    </div>
                    <div className={styles.rightSide}>
                        <div className={styles.container}>
                            <div className={styles.buttonContainer}>
                                {/* <button className="no-color-button" onClick={signUpClick}>Sign-up instead</button> */}
                            </div>
                            <div className="form-container" style={{ paddingTop: '0px' }}>
                                <p className={styles.description}>Enter details to Sign-in</p>
                                <form onSubmit={handleSignIn}>
                                    <div className="form-group-new">
                                        <input
                                            className={`form-control ${formSubmitted && (!email || !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email)) ? 'input-error' : ''}`}
                                            style={{ width: "100%", padding: "10px 10px" }}
                                            id="exampleInputEmail1"
                                            placeholder="Your email"
                                            value={email}
                                            maxLength={50}
                                            onChange={(e) => {
                                                setEmail(e.target.value);
                                                setFormSubmitted(false);
                                            }}
                                        />
                                        {formSubmitted && !email && <p className="error-message">Email is required</p>}
                                        {formSubmitted && email && !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email) &&
                                            <p className="error-message">Please enter a valid email address</p>}
                                    </div>
                                    <div className={styles.passwordContainer}>
                                        <input
                                            type={passwordVisible ? "text" : "password"}
                                            className={`${styles.passwordInput} ${formSubmitted && (!password) ? 'input-error' : ''}`}
                                            placeholder="Your password"
                                            value={password}
                                            maxLength={50}
                                            onChange={(e) => {
                                                setPassword(e.target.value);
                                                setFormSubmitted(false);
                                            }}
                                        />
                                        <button
                                            type="button"
                                            onClick={togglePassword}
                                            className={styles.passwordToggle}
                                            style={formSubmitted && !password ? { top: "50%" } : {}}
                                            aria-label={passwordVisible ? "Hide password" : "Show password"}
                                        >
                                            {passwordVisible ? (
                                                <EyeOffIcon className="eye-icon" />
                                            ) : (
                                                <EyeIcon className="eye-icon" />
                                            )}
                                        </button>
                                        {formSubmitted && !password && <p className="error-message">Password cannot be empty</p>}
                                    </div>
                                    <p className={styles.forgotPassword} onClick={forgotPasswordClick}>Forgot Password?</p>

                                    <button className={styles.signinButton} type="submit">
                                        {isLoading ? (
                                            <div className={globalStyles.loaderContainer}>
                                                <div className={globalStyles.loader}></div>
                                            </div>
                                        ) : (
                                            "Sign-in"
                                        )}
                                    </button>
                                    <p className={styles.supportText}>
                                        Want to learn more? Contact us at{' '}
                                        <a href="mailto:<EMAIL>" className={styles.supportLink}>
                                            <EMAIL>
                                        </a>
                                    </p>
                                </form>
                            </div>
                            <div className={styles.buttonContainerNew}>
                                {/* <button className="no-color-button" style={{ marginBottom: "15px", width: "100%" }}>
                                    <img src="/assets/img.png" alt="Google Icon" className={styles.buttonIcon} />Sign-in
                                    with Google
                                </button> */}
                                {/* <button className="no-color-button">
                                    <img src="/assets/img_1.png" alt="Google Icon" className={styles.buttonIcon} />
                                    Sign-in with Microsoft
                                </button> */}
                            </div>
                        </div>
                    </div>
                </div>

                <div className={styles.featuresSection}>
                    <div className={`${globalStyles.bottomContainer} ${globalStyles.poppinsStyle}`} style={{ fontWeight: "600" }}>
                        <div className={styles.featureItem}>
                            <div className={globalStyles.imageContainer}>
                                <img src="/assets/nano.png" alt="Smart auto-mapping" className={globalStyles.imageBottom} />
                            </div>
                            <span>Smart auto-mapping</span>
                        </div>
                        <div className={styles.featureItem}>
                            <div className={globalStyles.imageContainer}>
                                <img src="/assets/lock.png" alt="Secure with encryption" className={globalStyles.imageBottom} />
                            </div>
                            <span>Secure with encryption</span>
                        </div>
                        <div className={styles.featureItem}>
                            <div className={globalStyles.imageContainer}>
                                <img src="/assets/lightstrike.png" alt="No-code configurations" className={globalStyles.imageBottom} />
                            </div>
                            <span>No-code configurations</span>
                        </div>
                        <div className={styles.featureItem}>
                            <div className={globalStyles.imageContainer}>
                                <img src="/assets/analytics.png" alt="Migration reports" className={globalStyles.imageBottom} />
                            </div>
                            <span>Migration reports</span>
                        </div>
                    </div>

                    <div className={styles.trustedBySection}>
                        <hr className={globalStyles.signupHr} />
                        <div className={styles.trustedByContainer}>
                            <div className={styles.trustedByTitle}>Trusted by</div>
                            <div className={globalStyles.trustedLogos}>
                                {trustedLogos.map((logo, idx) =>
                                    logo.url ? (
                                        <a
                                            key={logo.alt}
                                            href={logo.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className={styles.trustedLogo}
                                            style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                                        >
                                            <img
                                                src={logo.src}
                                                alt={logo.alt}
                                                style={{ width: 100, height: 80, objectFit: "contain" }}
                                            />
                                        </a>
                                    ) : (
                                        <div
                                            key={logo.alt}
                                            className={styles.trustedLogo}
                                            style={{ animationDelay: `${0.1 * (idx + 1)}s` }}
                                        >
                                            <img
                                                src={logo.src}
                                                alt={logo.alt}
                                                style={{ width: 100, height: 80, objectFit: "contain" }}
                                            />
                                        </div>
                                    )
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ToastContainer
                position="top-right"
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="light"
                style={{
                    fontFamily: "Inter",
                }}
                toastStyle={{
                    fontFamily: "Inter",
                    fontWeight: "bold",
                }}
            />
        </div>
    );
}