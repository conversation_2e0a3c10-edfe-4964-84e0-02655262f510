import { useContext, useEffect, useRef, useState } from "react"
import styles from "./Review.module.css"
import globalStyles from "../../globalStyles.module.css"
import { Dialog, DialogContent, FormControl, FormControlLabel, MenuItem, Select, Switch } from "@mui/material"
import { HiCalendarDays, HiXMark } from "react-icons/hi2"
import { HiPlus } from "react-icons/hi"
import MigrationScheduler from "./MigrationScheduler/MigrationScheduler"
import { useNavigate, useLocation } from "react-router-dom"
import Pay from "./Pay/Pay"
import PaymentDetails from "./paymentdetails"
import { migrationTemplate, postActivity, saveMigrationPlan, postTransformation, postToQueue } from "../../apiService"
import { MigrationContext } from "../Data-Migration"
import { toast, ToastContainer } from "react-toastify"
import { displayArticle } from "../../../Helper/helper"
import LoaderSpinner from "../../loaderspinner"

export default function Review({ isSample, paymentSuccess, templateName, planId, setPlanId }) {
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const source = migrationState.sourceObjData
  const targetData = migrationState.targetData
  const dataMappingData = migrationState.dataMappingData
  const dataTypeData = migrationState.dataTypeData
  const selectedObjectData = migrationState.selectedObjectData
  const selectedEntityData = migrationState.selectedEntityData
  const sourceExeRes = migrationState.sourceExeRes
  const targetExeRes = migrationState.targetExeRes
  const [hasSampleMigration, setHasSampleMigration] = useState(false)
  const [hasAtleastOneSampleMigration, setHasAtleastOneSampleMigration] = useState(false)
  const [sampleMigrationCount, setSampleMigrationCount] = useState(0)
  const [hasAtleastOneMigration, setHasAtleastOneMigration] = useState(false)
  // const [sampleMigrationLimitReached, setSampleMigrationLimitReached] = useState(false)

  const timerRef = useRef(null)
  const [email, setEmail] = useState(localStorage.getItem("email"))

  const countryCodes = [
    { code: "+91", name: "India" },
    { code: "+1", name: "United States" },
    { code: "+44", name: "United Kingdom" },
    { code: "+61", name: "Australia" },
    { code: "+1", name: "Canada" },
  ]

  // Fixed state management for Step 5 input fields
  const [sampleBatchSize, setSampleBatchSize] = useState("")
  const [liveMigrationPayload, setLiveMigrationPayload] = useState({})
  const [isFromMigration, setIsFromMigration] = useState(false)

  // State for CSV validation
  const [isCSVSource, setIsCSVSource] = useState(false)
  const [mainEntityRecordCount, setMainEntityRecordCount] = useState(0)
  const [selectedObjectName, setSelectedObjectName] = useState("")

  const navigate = useNavigate()
  const location = useLocation()

  const [isSampleMigration, setIsSampleMigration] = useState(isSample !== undefined ? isSample : true)
  const [isPaymentSucces, setIsPaymentSucces] = useState(paymentSuccess)
  const [liveMigrationLoading, setLiveMigrationLoading] = useState(false)

  // Initialize CSV source detection and main entity record count
  useEffect(() => {
    if (source?.source?.name?.toLowerCase() === "csv") {
      setIsCSVSource(true)
    }

    if (dataTypeData) {
      setSelectedObjectName(dataTypeData.selectedObjectName || "")

      // Get main entity record count from uploaded files
      if (dataTypeData.uploadedFiles && dataTypeData.selectedObjectName) {
        const mainEntityFile = dataTypeData.uploadedFiles.find(
          (file) => file.entity === dataTypeData.selectedObjectName,
        )
        if (mainEntityFile && mainEntityFile.recordCount) {
          setMainEntityRecordCount(mainEntityFile.recordCount)
          console.log(`Main entity (${dataTypeData.selectedObjectName}) record count:`, mainEntityFile.recordCount)
        }
      }

      // Restore main entity record count from saved data
      if (dataTypeData.mainEntityRecordCount) {
        setMainEntityRecordCount(dataTypeData.mainEntityRecordCount)
      }
    }
  }, [source, dataTypeData])

  useEffect(() => {
    setIsPaymentSucces(paymentSuccess)
  }, [paymentSuccess])

  useEffect(() => {
    if (location.state && location.state.fromMigration) {
      setIsFromMigration(true)
    }
  }, [location.state])

  useEffect(() => {
    if (location.state) {
      if (location.state.templateName && location.state.templateName !== "Name of the Template") {
        setMigrationState((prevState) => ({
          ...prevState,
          templateName: location.state.templateName,
        }))
      }
      if (location.state.fromMigration) {
        if (location.state.migrationData) {
          const { sampleBatchSize: batchSize, tag: tagValue, migrationProgress } = location.state.migrationData
          if (batchSize) setSampleBatchSize(batchSize)
          if (tagValue) setTag(tagValue)

          if (migrationProgress === 0) {
            localStorage.removeItem("migrationProgress")
            localStorage.removeItem("migrationId")
            setIsFromMigration(false)
          } else {
            setIsFromMigration(true)
          }
        } else {
          setIsFromMigration(true)
        }
      }
    }
  }, [location.state, setMigrationState])

  useEffect(() => {
    if (migrationState.templateName && migrationState.templateName !== "Name of the Template") {
      setMigrationState((prevState) => ({
        ...prevState,
        templateName: templateName,
      }))
    }
  }, [templateName, setMigrationState])

  // useEffect(() => {
  //   if (!isSampleMigration) {
  //     const transformationPayload = initiateMigration()
  //     setLiveMigrationPayload(transformationPayload)
  //   }
  // }, [isSampleMigration])

  const [liveMigrationOption, setLiveMigrationOption] = useState("")
  const [contacts, setContacts] = useState([{ email: "", phone: "", code: "+91" }])
  const [sentNotification, setSentNotification] = useState(false)
  const [sentDetailsToMyself, setSentDetailsToMyself] = useState(false)
  const [migrationDate, setMigrationDate] = useState("")
  const [showPricing, setShowPricing] = useState(false)
  const [tag, setTag] = useState("")

  const handleTagChange = (event) => {
    const newValue = event.target.value
    if (newValue.length <= 32) {
      setTag(newValue)
    } else {
      toast.error("Tag cannot be more than 32 characters long", {
        position: "top-right",
      })
    }
  }

  const handleSourceValueChange = (newValue) => {
    setLiveMigrationOption(newValue)
  }

  const addContact = () => {
    setContacts([...contacts, { email: "", phone: "", code: "+91" }])
  }
  const removeContact = () => {
    setContacts(contacts.slice(0, -1))
  }
  const [open, setOpen] = useState(false)

  const saveMigration = async () => {
    try {
      const payload = {
        plan_name: templateName,
        migration_objects: [],
        updatedAt: Date.now(),
        email_id: email,
        isCompleted: true,
        additional_details: {
          sourceObjData: migrationState.sourceObjData,
          targetData: migrationState.targetData,
          dataTypeData: migrationState.dataTypeData,
          dataMappingData: migrationState.dataMappingData,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
        },
      }
      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
      }
      const res = await saveMigrationPlan(payload)

      if (res?.message === "plan created successfully" && res?.response?.id) {
        const newPlanId = res.response.id
        setPlanId(newPlanId)

        const url = new URL(window.location.href)
        url.searchParams.delete("planId")
        url.searchParams.set("plan_id", newPlanId)
        window.history.replaceState({}, "", url.toString())
      }
    } catch (error) {
      toast.error(error?.response.data.message || "Something went wrong!", {
        position: "top-right",
      })
    }
  }

  // Function to validate record count against main entity CSV data
  const validateRecordCount = (inputCount) => {
    if (!isCSVSource || !mainEntityRecordCount) return true

    const numInputCount = Number.parseInt(inputCount)
    if (isNaN(numInputCount)) return true

    return numInputCount <= mainEntityRecordCount
  }

  // Handle input change for sample batch size with validation
  const handleSampleBatchSizeChange = (event) => {
    const value = event.target.value

    // Allow only numbers
    if (value === "" || /^\d+$/.test(value)) {
      // Check if value exceeds 50 records
      if (Number(value) > 50) {
        toast.error("Number of records cannot exceed 50 for sample migration", {
          position: "top-right",
        })
        return
      }

      setSampleBatchSize(value)

      // Real-time validation for CSV sources
      if (isCSVSource && value && mainEntityRecordCount > 0) {
        const numValue = Number(value)
        if (numValue > mainEntityRecordCount) {
          toast.error(
            `Number of records cannot exceed ${mainEntityRecordCount.toLocaleString()} (total records in your ${selectedObjectName} CSV file)`,
            {
              position: "top-right",
            },
          )
          return
        }
      }

      // Existing validation for non-CSV sources
      if (!isCSVSource && value) {
        const totalRecords = Number(dataTypeData.numberOfRecords)
        if (Number(value) > totalRecords) {
          toast.error(
            `Number of records cannot exceed ${totalRecords.toLocaleString()} (total available records)`,
            {
              position: "top-right",
            },
          )
          return
        }
      }
    }
  }

  const handleLiveMigrationClick = async () => {
    setLiveMigrationLoading(true)

    // Commented out dropdown validation since dropdown is commented out
    /*
    if (!liveMigrationOption) {
      toast.error("Please select when you want to start the migration", {
        position: "top-right",
      })
      setLiveMigrationLoading(false)
      return
    }

    if (liveMigrationOption === "later" && !migrationDate) {
      toast.error("Please select a date and time for the scheduled migration", {
        position: "top-right",
      })
      setLiveMigrationLoading(false)
      return
    }
    */
    localStorage.setItem('liveMigrationButtonClicked', true);
    // Store forceStep5 in localStorage to ensure it's preserved across navigation
    localStorage.setItem('forceStep5', 'true');

    // Mark that at least one migration has been completed
    if (planId) {
      localStorage.setItem(`atleastOneMigration_${planId}`, "true");
    }

    // Increment totalMigrationCount in plan metadata
    try {
      if (planId) {
        console.log("Plan ID for live migration:", planId);
        const oldPlanData = await migrationTemplate.get({ id: planId });
        const inJson = JSON.parse(oldPlanData.metadata);

        const countUpdatedMetadata = {
          ...inJson,
          totalMigrationCount: (inJson?.totalMigrationCount || 0) + 1,
        };
        await migrationTemplate.update(planId, {
          metadata: JSON.stringify(countUpdatedMetadata),
        });
        console.log("Migration count incremented successfully");
      }
    } catch (error) {
      console.log("Error updating live migration count:", error);
    }

    // Call initiateMigration to generate the live migration payload
    let liveMigrationPayload = null;
    try {
      if (initiateMigration) {
        liveMigrationPayload = await initiateMigration();
      }
    } catch (error) {
      console.log("Error generating live migration payload:", error);
    }

    // Ensure liveMigrationPayload is serializable before navigation
    let serializablePayload = null;
    try {
      // If liveMigrationPayload is a Promise, await it
      const resolvedPayload = await Promise.resolve(liveMigrationPayload);
      // Create a clean, serializable copy of the payload
      serializablePayload = JSON.parse(JSON.stringify(resolvedPayload));
    } catch (error) {
      console.log("Error serializing liveMigrationPayload:", error);
      // Fallback to basic payload structure
      serializablePayload = {
        error: "Failed to serialize payload",
        timestamp: new Date().toISOString()
      };
    }

    navigate('/migration-results', {
      state: {
        sample: false,
        liveMigrationPayload: serializablePayload,
        forceStep5: true
      }
    });
    setLiveMigrationLoading(false);
  };

  const [showSampleLoader, setShowSampleLoader] = useState(false);

  const handleSampleMigration = async () => {
    setShowSampleLoader(true);
    // Check if sample migration limit has been reached
    // if (sampleMigrationLimitReached) {
    //   setShowSampleLoader(false);
    //   toast.error("Maximum sample migration limit (2) exceeded for this template", {
    //     position: "top-right",
    //   })
    //   return
    // }

    if (planId) {
      localStorage.setItem(`atleastOneSamplemigration_${planId}`, "true");
      localStorage.setItem(`atleastOneMigration_${planId}`, "true");
    }

    if (!migrationState.templateName || migrationState.templateName.trim() === "Name of the Template") {
      setShowSampleLoader(false);
      toast.error("Please enter the Template name before starting the migration", {
        position: "top-right",
      })
      return
    }

    if (!sampleBatchSize || sampleBatchSize === "") {
      setShowSampleLoader(false);
      toast.error("Please enter the number of records for sample migration", {
        position: "top-right",
      })
      return
    }

    // Validate against main entity record count for CSV sources
    if (isCSVSource && mainEntityRecordCount > 0) {
      const numBatchSize = Number.parseInt(sampleBatchSize)
      if (numBatchSize > mainEntityRecordCount) {
        setShowSampleLoader(false);
        toast.error(
          `Number of records cannot exceed ${mainEntityRecordCount.toLocaleString()} (total records in your ${selectedObjectName} CSV file)`,
          {
            position: "top-right",
            autoClose: 5000,
          },
        )
        return
      }
    }

    // Existing validation for non-CSV sources
    if (!isCSVSource) {
      const totalRecords = Number(dataTypeData.numberOfRecords)
      if (Number(sampleBatchSize) > totalRecords) {
        setShowSampleLoader(false);
        toast.error("Sample migration count cannot exceed total records count", {
          position: "top-right",
        })
        return
      }
    }

    // Update localStorage with sample migration count immediately after validation passes
    // if (planId) {
    //   setTimeout(() => {
    //     const countKey = `sampleMigrationCount_${planId}`;
    //     const currentCount = Number.parseInt(localStorage.getItem(countKey) || "0");
    //     const newCount = currentCount + 1;
    //     localStorage.setItem(countKey, newCount.toString());

    //     // Update the local state immediately
    //     setSampleMigrationCount(newCount);
    //     setSampleMigrationLimitReached(newCount >= 2);
    //   }, 5000);
    // }

    try {
      console.log("Plan ID:.............", planId)
      const oldPlanData = await migrationTemplate.get({ id: planId })
      const inJson = JSON.parse(oldPlanData.metadata)

      const countUpdatedMetadata = {
        ...inJson,
        totalMigrationCount: (inJson?.totalMigrationCount || 0) + 1,
      }
      await migrationTemplate.update(planId, {
        metadata: JSON.stringify(countUpdatedMetadata),
      })
    } catch (error) {
      console.log("Error saving migration plan:", error)
    }

    const transformationPayload = await initiateMigration()

    setShowSampleLoader(false);

    if (transformationPayload?.isSuccess) {
      localStorage.setItem("migrationInProgress", "true")

      localStorage.removeItem("migrationProgress")
      localStorage.removeItem("migrationId")

      console.log(transformationPayload)
      localStorage.setItem("sampleMigrationButtonClicked", "true")
      const activityPayload = {
        email: email,
        activity: "Sample Migration started",
      }
      postActivity(activityPayload)

      const searchParams = new URLSearchParams(location.search)
      searchParams.delete("planId")
      if (planId) {
        searchParams.set("plan_id", planId)
      }

      localStorage.setItem(
        "sampleMigrationData",
        JSON.stringify({
          sampleBatchSize: sampleBatchSize,
          tag: tag,
        }),
      )

      navigate(`/migration-results?${searchParams.toString()}`, {
        state: {
          sample: isSampleMigration,
          sampleBatchSize: sampleBatchSize,
          tag: tag,
          samplePayload: transformationPayload,
        },
      })
    } else {
      toast.error(transformationPayload?.message || "Failed to queue migration task", {
        position: "top-right",
      })
    }
  }

  const handleRerunPreviousBatch = async () => {
    // Get the latest migration data from localStorage
    const latestMigrationId = localStorage.getItem("latestMigrationId");
    const sampleMigrationData = localStorage.getItem("sampleMigrationData");
    
    if (!latestMigrationId && !sampleMigrationData) {
      toast.error("No previous migration found to rerun", {
        position: "top-right",
      });
      return;
    }

    let previousBatchSize = sampleBatchSize;
    let previousTag = tag;

    // Try to get previous migration settings
    if (sampleMigrationData) {
      try {
        const parsedData = JSON.parse(sampleMigrationData);
        previousBatchSize = parsedData.sampleBatchSize || sampleBatchSize;
        previousTag = parsedData.tag || tag;
      } catch (error) {
        console.log("Error parsing previous migration data:", error);
      }
    }

    // If no previous data found, use default values
    if (!previousBatchSize) {
      previousBatchSize = "10"; // Default batch size
    }
    if (!previousTag) {
      previousTag = "rerun-batch"; // Default tag
    }

    // Set the values from previous migration
    setSampleBatchSize(previousBatchSize);
    setTag(previousTag);

    // Validate the previous batch size
    if (Number(previousBatchSize) > 50) {
      toast.error("Previous batch size exceeds current limit of 50 records", {
        position: "top-right",
      });
      return;
    }

    // Validate against CSV record count if applicable
    if (isCSVSource && mainEntityRecordCount > 0) {
      const numBatchSize = Number.parseInt(previousBatchSize);
      if (numBatchSize > mainEntityRecordCount) {
        toast.error(
          `Previous batch size (${numBatchSize}) exceeds current CSV record count (${mainEntityRecordCount})`,
          {
            position: "top-right",
            autoClose: 5000,
          }
        );
        return;
      }
    }

    // Show confirmation toast
    toast.info(`Rerunning previous batch with ${previousBatchSize} records and tag "${previousTag}"`, {
      position: "top-right",
      autoClose: 3000,
    });

    // Call the migration logic directly with the previous values instead of using handleSampleMigration
    setShowSampleLoader(true);

    if (planId) {
      localStorage.setItem(`atleastOneSamplemigration_${planId}`, "true");
      localStorage.setItem(`atleastOneMigration_${planId}`, "true");
    }

    try {
      const oldPlanData = await migrationTemplate.get({ id: planId });
      const inJson = JSON.parse(oldPlanData.metadata);

      const countUpdatedMetadata = {
        ...inJson,
        totalMigrationCount: (inJson?.totalMigrationCount || 0) + 1,
      };
      await migrationTemplate.update(planId, {
        metadata: JSON.stringify(countUpdatedMetadata),
      });
    } catch (error) {
      console.log("Error saving migration plan:", error);
    }

    const transformationPayload = await initiateMigration();

    setShowSampleLoader(false);

    if (transformationPayload?.isSuccess) {
      localStorage.setItem("migrationInProgress", "true");
      localStorage.removeItem("migrationProgress");
      localStorage.removeItem("migrationId");

      const activityPayload = {
        email: email,
        activity: "Sample Migration started",
      };
      postActivity(activityPayload);

      const searchParams = new URLSearchParams(location.search);
      searchParams.delete("planId");
      if (planId) {
        searchParams.set("plan_id", planId);
      }

      localStorage.setItem(
        "sampleMigrationData",
        JSON.stringify({
          sampleBatchSize: previousBatchSize,
          tag: previousTag,
        })
      );

      navigate(`/migration-results?${searchParams.toString()}`, {
        state: {
          sample: isSampleMigration,
          sampleBatchSize: previousBatchSize,
          tag: previousTag,
          samplePayload: transformationPayload,
        },
      });
    } else {
      toast.error(transformationPayload?.message || "Failed to queue migration task", {
        position: "top-right",
      });
    }
  }



  // Check if atleastOneSamplemigration is set in localStorage for this plan
  useEffect(() => {
    if (planId) {
      const atleastOneSample = localStorage.getItem(`atleastOneSamplemigration_${planId}`) === "true";
      const atleastOneMigration = localStorage.getItem(`atleastOneMigration_${planId}`) === "true";
      // Also check if there's any latestMigrationId from any previous migration
      const hasLatestMigrationId = localStorage.getItem("latestMigrationId") !== null;
      // Check if there's previous sample migration data
      const hasSampleMigrationData = localStorage.getItem("sampleMigrationData") !== null;
      
      setHasAtleastOneSampleMigration(atleastOneSample);
      setHasAtleastOneMigration(atleastOneMigration || hasLatestMigrationId || hasSampleMigrationData);
    } else {
      setHasAtleastOneSampleMigration(false);
      setHasAtleastOneMigration(false);
    }
  }, [planId, isSampleMigration]);

  const initiateMigration = async () => {
    saveMigration()
    const emailFromStorage = localStorage.getItem("email")

    const getSafe = (fn, defaultValue) => {
      try {
        const value = fn()
        return value === undefined || value === null ? defaultValue : value
      } catch (e) {
        return defaultValue
      }
    }

    const mainTargetName = getSafe(() => migrationState.selectedEntityData.mainTarget[0].name, "")

    const toCamel = (str) => str.replace(/_([a-z])/g, (g) => g[1].toUpperCase())
    const keysToCamel = (obj) => {
      if (Array.isArray(obj)) return obj.map(keysToCamel)
      if (obj !== null && typeof obj === "object") {
        return Object.entries(obj).reduce((acc, [key, value]) => {
          acc[toCamel(key)] = keysToCamel(value)
          return acc
        }, {})
      }
      return obj
    }

    const processFieldMappings = (fieldMappings) => {
      if (!Array.isArray(fieldMappings)) return []

      return fieldMappings.map((field) => {
        const processedField = { ...field }

        if (processedField.override && Array.isArray(processedField.override) && processedField.override.length > 0) {
          processedField.mappingType = "valueMapped"
          // Set type based on the first non-null, non-undefined targetvalue
          const firstValid = processedField.override.find(
            (o) => o && o.targetvalue !== null && o.targetvalue !== undefined
          )
          if (firstValid) {
            const val = firstValid.targetvalue
            if (Array.isArray(val)) {
              processedField.type = "array"
            } else if (typeof val === "boolean") {
              processedField.type = "boolean"
            } else if (typeof val === "number") {
              processedField.type = "number"
            } else {
              processedField.type = "string"
            }
          }
        }
        else if (
          processedField.sourcefield &&
          processedField.sourcefield !== "" &&
          processedField.targetfield &&
          processedField.targetfield !== ""
        ) {
          processedField.mappingType = "sourceAttribute"
        }
        else if (
          (!processedField.sourcefield || processedField.sourcefield === "") &&
          processedField.targetfield &&
          processedField.targetfield !== ""
        ) {
          processedField.mappingType = "static"

          if (!processedField.default || processedField.default === "") {
            processedField.default = processedField.value || ""
          }
          if (!processedField.value || processedField.value === "") {
            processedField.value = processedField.default || ""
          }

          if (processedField.default !== processedField.value && processedField.value) {
            processedField.default = processedField.value
          }

          // Try to find the field definition in ticketFields or ticket_fields
          let fieldDefArr = migrationState.dataMappingData.targetExeRes.ticketFields || migrationState.dataMappingData.targetExeRes.ticket_fields || []
          let fieldDef = fieldDefArr.find(item => item.name === processedField.targetfield)

          // If choices exist, determine type based on choices.id or choices.value
          if (fieldDef && Array.isArray(fieldDef.choices) && fieldDef.choices.length > 0) {
            const choice = fieldDef.choices[0]
            if (choice) {
              if (choice.id !== undefined && choice.id !== null) {
                if (Array.isArray(choice.id)) {
                  processedField.type = "array"
                } else if (typeof choice.id === "boolean") {
                  processedField.type = "boolean"
                } else if (typeof choice.id === "number") {
                  processedField.type = "number"
                } else {
                  processedField.type = "string"
                }
              } else if (choice.value !== undefined && choice.value !== null) {
                if (Array.isArray(choice.value)) {
                  processedField.type = "array"
                } else if (typeof choice.value === "boolean") {
                  processedField.type = "boolean"
                } else if (typeof choice.value === "number") {
                  processedField.type = "number"
                } else {
                  processedField.type = "string"
                }
              } else {
                processedField.type = "string"
              }
            } else {
              processedField.type = "string"
            }
          }
        }

        if (processedField.type === "array") {
          if (typeof processedField.value === "string") {
            processedField.value = processedField.value.split(",").map((item) => item.trim())
          }
          if (typeof processedField.default === "string") {
            processedField.default = processedField.default.split(",").map((item) => item.trim())
          }
        } else if (processedField.type === "boolean") {
          const toBool = (val) => val === "true" || val === true

          processedField.value = toBool(processedField.value)
          processedField.default = toBool(processedField.default)

          if (Array.isArray(processedField.override)) {
            processedField.override = processedField.override.map((o) => ({
              sourcevalue: toBool(o.sourcevalue),
              targetvalue: toBool(o.targetvalue),
            }))
          }
        } else if (processedField.type === "number") {
          processedField.value = Number(processedField.value)
          processedField.default = Number(processedField.default)
        }

        if (processedField.includeValuesFrom) {
          if (processedField.type === "array") {
            processedField.value = Array.isArray(processedField.value) ? processedField.value : []

            const variableMap = {
              tag: tag,
            }

            const dynamicValue = variableMap[processedField.includeValuesFrom]
            if (dynamicValue !== undefined) {
              processedField.value.push(dynamicValue)
            }
          }
        }

        return keysToCamel(processedField)
      })
    }

    const mainTargetFieldMappings = getSafe(() => {
      const target = migrationState.dataMappingData.targets.find(
        (t) => t.name.toLowerCase() === mainTargetName.toLowerCase(),
      )
      const fieldMappings = target ? target.fieldMappings : []
      return processFieldMappings(fieldMappings)
    }, [])

    const syncQueryParamsToHeadersRecursive = (executor) => {
      if (!executor || !executor.queryParams || !executor.headers) return

      executor.queryParams.forEach((queryParam) => {
        if (queryParam.key && queryParam.value) {
          const existingHeaderIndex = executor.headers.findIndex((header) => header.key === queryParam.key)

          if (existingHeaderIndex > -1) {
            executor.headers[existingHeaderIndex].value = queryParam.value
            executor.headers[existingHeaderIndex].description = queryParam.description || ""
            executor.headers[existingHeaderIndex].req = queryParam.req || false
          }
        }
      })
    }

    const normalizeQueryParams = (queryParams) => {
      if (!Array.isArray(queryParams)) return [];
      const out = [];
      for (const qp of queryParams) {
        if (!qp) continue;
        if (Array.isArray(qp.value)) {
          qp.value
            .filter(v => v !== undefined && v !== null && v !== "")
            .forEach(v => out.push({ ...qp, value: v }));
        } else {
          out.push(qp);
        }
      }
      return out;
    };

    const constructSourceExeData = (selectedObjectData) => {
      if (!selectedObjectData) return {};
      if (selectedObjectData.source == "csv") {
        selectedObjectData.sourceExecutor.queryParams.map((queryParam) => {
          if (queryParam.key == "page[size]") {
            queryParam.value = String(sampleBatchSize) || "50";
          }
          else if (queryParam.key == "fileName") {
            queryParam.value = (
              migrationState.dataTypeData.uploadedFiles.find(
                (file) =>
                  file.entity &&
                  file.entity.toLowerCase() === migrationState.selectedEntityData.migrationObject[0].migrationEntity.toLowerCase()
              )?.name // or .blobPath or whatever property holds the Azure blob file path
            );

            console.log("File name for CSV source:", queryParam.value);
          }
          else if (queryParam.key == "filePath") {
            queryParam.value = "csv";
          }
        });

        return selectedObjectData.sourceExecutor;
      }
      else {
        const isJsonSource = (() => {
          const src = migrationState?.sourceObjData?.source;
          // src may be an object with type/name or a string identifier
          if (src) {
            if (typeof src === 'object') {
              const candidate = (src.type || src.sourceType || src.name || '').toString().toLowerCase();
              if (candidate === 'json' || candidate.endsWith('json')) return true;
            } else if (typeof src === 'string') {
              const s = src.toLowerCase();
              if (s === 'json' || s.endsWith('json')) return true;
            }
          }
          const alt = (selectedObjectData?.type || '').toString().toLowerCase();
          return alt === 'json' || alt.endsWith('json');
        })();

        if (isJsonSource) {
          selectedObjectData.sourceExecutor.queryParams.map((queryParam) => {
            migrationState.selectedEntityData?.sourceExtender?.map((extender) => {
              const params = ["page[size]", "offset", "filePath"];
              if (params.includes(queryParam.key)) {
                const param = extender.sourceExecutor.queryParams.find((qp) => qp.key === queryParam.key);
                if (param) {
                  param.value = queryParam.value || "";
                }
              }
            })
          });
        }
        
        // Normalize array values -> multiple entries
        selectedObjectData.sourceExecutor = {
          ...selectedObjectData.sourceExecutor,
          queryParams: normalizeQueryParams(selectedObjectData.sourceExecutor?.queryParams),
        };

        return selectedObjectData.sourceExecutor;
      }
    }

    const constructSourceDepExeData = (selectedObjectData, sourceExecutor, dependentName) => {
      if (!selectedObjectData) return {};
      if (selectedObjectData.sourceType == "csv") {
        sourceExecutor.queryParams.map((queryParam) => {
          if (queryParam.key == "page[size]") {
            queryParam.value = String(sampleBatchSize) || "50";
          }
          // else if (queryParam.key == "fileName" && !dependentName) {
          //   queryParam.value = (
          //     migrationState.dataTypeData.uploadedFiles.find(
          //       (file) =>
          //         file.entity &&
          //         file.entity.toLowerCase() === migrationState.selectedEntityData.sourceExtender[0].name.toLowerCase()
          //     )?.name // or .blobPath or whatever property holds the Azure blob file path
          //   );
          //   console.log("File name for CSV source:", queryParam.value);
          // }
          else if (queryParam.key == "fileName" && dependentName) {
            queryParam.value = (
              migrationState.dataTypeData.uploadedFiles.find(
                (file) =>
                  file.entity &&
                  file.entity.toLowerCase() === (
                    (migrationState.selectedEntityData.sourceExtender.find(
                      (item) => item.name.toLowerCase() === dependentName.toLowerCase()
                    ) || {}).name || ""
                  ).toLowerCase()
              )?.name // or .blobPath or whatever property holds the Azure blob file path
            );
            console.log("File name for CSV source:", queryParam.value);
          }
          else if (queryParam.key == "filePath") {
            queryParam.value = "csv";
          }
          else if (queryParam.key == "ticket_id") {
            if (isCSVSource) {
              let migrationEntity = migrationState.selectedObjectData.migrationEntity.toLowerCase();
              const sourceUuidObj = migrationState.dataTypeData?.uuidColumns?.find(
                (col) => col.name.toLowerCase() === migrationEntity
              );
              queryParam.value = `{{${sourceUuidObj?.name?.toLowerCase()}.${sourceUuidObj?.uuidColumn}}}` || migrationState.selectedObjectData.sourceUniqueIdentifier || "";
            }
          }
        });

        return sourceExecutor;
      }
      else {
        return sourceExecutor;
      }
    }

    const dependentSourcesPayload = () => {
      const isDependentAvailable = migrationState.dataMappingData.targets.find(
        (t) => {
          if ((t.isMainTarget === false || t.isMainTarget === null) && t.isMappingTarget == "false") {
            return true
          }
          return false
        }
      )
      if (isDependentAvailable) {
        return migrationState.selectedEntityData.sourceExtender.map((ds) => {
          const processedSourceExecutor = { ...ds.sourceExecutor }
          // processedSourceExecutor = constructSourceExeData(ds, ds.name)

          syncQueryParamsToHeadersRecursive(processedSourceExecutor)

          const processedDependentSources = ds.dependentSources
            ? ds.dependentSources.map((depSource) => {
              const processedDepExecutor = { ...depSource.sourceExecutor }
              // processedDepExecutor = constructSourceExeData(depSource, depSource.name)

              syncQueryParamsToHeadersRecursive(processedDepExecutor)

              return {
                ...depSource,
                name: depSource.name.toLowerCase(),
                // uniqueIdentifier: getSafe(() => depSource.uniqueIdentifier, ""),
                uniqueIdentifier: getSafe(() => {
                  if (isCSVSource) {
                    let sourceUuidObj = depSource.uniqueIdentifier || "";
                    migrationState.selectedEntityData.sourceExtender.map((extender) => {
                      sourceUuidObj = migrationState.dataTypeData?.uuidColumns?.find(
                        (col) => col.name.toLowerCase() === extender.name.toLowerCase()
                      );
                    })
                    return `{{${sourceUuidObj.name.toLowerCase()}.${sourceUuidObj?.uuidColumn}}}` || depSource.uniqueIdentifier || "";
                  }
                  return depSource.uniqueIdentifier || "";
                }, ""),
                sourceExecutor: constructSourceDepExeData(depSource, processedDepExecutor, depSource.name),
              }
            })
            : []

          // console.log("Processed Source Executor:", constructSourceDepExeData(ds, processedSourceExecutor, ds.name))

          return {
            name: ds.name.toLowerCase(),
            uniqueIdentifier: getSafe(() => {
              if (isCSVSource) {
                let sourceUuidObj = ds.uniqueIdentifier || "";
                migrationState.selectedEntityData.sourceExtender.map((extender) => {
                  sourceUuidObj = migrationState.dataTypeData?.uuidColumns?.find(
                    (col) => col.name.toLowerCase() === extender.name.toLowerCase()
                  );
                })
                return `{{${sourceUuidObj.name.toLowerCase()}.${sourceUuidObj?.uuidColumn}}}` || ds.uniqueIdentifier || "";
              }
              return ds.uniqueIdentifier || "";
            }, ""),
            sourceExecutor: constructSourceDepExeData(ds, processedSourceExecutor, ds.name),
            dependentSources: processedDependentSources,
          }
        })
      }
      else {
        return []
      }
    }

    const dependentTargetsPayload = () => {
      const isDependentAvailable = migrationState.dataMappingData.targets.find(
        (t) => {
          if ((t.isMainTarget === false || t.isMainTarget === null) && t.isMappingTarget == "false") {
            return true
          }
          return false
        }
      )
      if (isDependentAvailable) {
        return migrationState.selectedEntityData.dependentTargets.map((dt) => {
          const fieldMappingsForDependentTarget = getSafe(() => {
            const target = migrationState.dataMappingData.targets.find(
              (t) => t.name.toLowerCase() === dt.name.toLowerCase(),
            )
            return target ? target.fieldMappings : []
          }, [])

          const processedDependentTargets = dt.dependentTargets
            ? dt.dependentTargets.map((depTarget) => {
              const fieldMappingsForNestedTarget = getSafe(() => {
                const nestedTarget = migrationState.dataMappingData.targets.find(
                  (t) => t.name.toLowerCase() === depTarget.name.toLowerCase(),
                )
                return nestedTarget ? nestedTarget.fieldMappings : []
              }, [])

              return {
                ...depTarget,
                name: depTarget.name.toLowerCase(),
                uniqueIdentifier: getSafe(() => depTarget.targetUniqueIdentifier, ""),
                targetResponseType: depTarget.target_response_type,
                targetExecutor: depTarget.targetExecutor,
                fieldMappings: processFieldMappings(fieldMappingsForNestedTarget),
              }
            })
            : []

          return {
            name: dt.name.toLowerCase(),
            uniqueIdentifier: getSafe(() => dt.targetUniqueIdentifier, ""),
            targetResponseType: dt.target_response_type,
            targetExecutor: dt.targetExecutor,
            fieldMappings: processFieldMappings(fieldMappingsForDependentTarget),
            dependentTargets: processedDependentTargets,
          }
        })
      }
      else {
        return []
      }
    }

    const syncQueryParamsToHeaders = (executor) => {
      if (!executor || !executor.queryParams || !executor.headers) return

      executor.queryParams.forEach((queryParam) => {
        if (queryParam.key && queryParam.value) {
          const existingHeaderIndex = executor.headers.findIndex((header) => header.key === queryParam.key)

          if (existingHeaderIndex > -1) {
            executor.headers[existingHeaderIndex].value = queryParam.value
            executor.headers[existingHeaderIndex].description = queryParam.description || ""
            executor.headers[existingHeaderIndex].req = queryParam.req || false
          }
        }
      })
    }

    syncQueryParamsToHeaders(migrationState.selectedObjectData.sourceExecutor)

    const getHostnameOrSubdomain = (url) => {
      const hostname = url.hostname;

      // If hostname starts with 'api.' or 'www.', return the full hostname
      if (hostname.startsWith('api.') || hostname.startsWith('www.')) {
        return hostname;
      }

      // For other cases, check if it's a subdomain pattern
      const parts = hostname.split('.');
      if (parts.length >= 3) {
        // It's a subdomain pattern like "sevahai.freshdesk.com"
        return parts[0];
      }

      // For simple domains or edge cases, return full hostname
      return hostname;
    };

    const buildDynamicHeaders = (target) => {
      if (!target?.formData) return []

      const headers = []
      const addedKeys = new Set()

      for (const [key, value] of Object.entries(target.formData)) {
        if (typeof value === "string" && value.startsWith("http")) {
          try {
            const url = new URL(value)
            // const subdomain = url.hostname.split(".")[0]
            const subdomain = getHostnameOrSubdomain(url);

            const derivedKey = key === "instance_url" ? "domainUrl" : key

            if (!addedKeys.has(derivedKey)) {
              headers.push({
                key: derivedKey,
                value: subdomain,
                description: "",
                req: false,
              })
              addedKeys.add(derivedKey)
            }
          } catch (e) {
            console.warn(`Skipping invalid URL in form field "${key}":`, value)
          }
        }

        if (!addedKeys.has(key)) {
          headers.push({
            key,
            value,
            description: "",
            req: false,
          })
          addedKeys.add(key)
        }
      }

      return headers
    }

    const transformationJson = {
      name: templateName || "",
      executedBy: emailFromStorage || "",
      environment: "production",
      templateId: planId?.toString() || "",
      totalRecords: isSampleMigration
        ? Number(sampleBatchSize) || 0
        : Number(getSafe(() => migrationState.dataTypeData.numberOfRecords, 1)),
      scheduledTime: Date.now(),
      migrationType: isSampleMigration ? "sample" : "live",
      parallelExecution: false,
      sourceModuleName: getSafe(() => migrationState.selectedObjectData.source, ""),
      targetModuleName: getSafe(() => migrationState.selectedObjectData.provider, ""),
      tagName: tag || "", // Add tag information to the transformation JSON
      common: {
        source: {
          headers: getSafe(() => migrationState.selectedObjectData.sourceExecutor.headers, {}),
          authType: getSafe(() => migrationState.selectedObjectData.sourceExecutor.authType, ""),
        },
        target: {
          headers: getSafe(() => buildDynamicHeaders(migrationState.targetData), {}),
          authType: getSafe(() => migrationState.selectedEntityData.mainTarget[0].targetExecutor.authType, ""),
        },
      },
      source: {
        name: getSafe(() => migrationState.selectedObjectData.migrationEntity.toLowerCase(), ""),
        uniqueIdentifier: getSafe(() => {
          if (isCSVSource) {
            let migrationEntity = migrationState.selectedObjectData.migrationEntity.toLowerCase();
            const sourceUuidObj = migrationState.dataTypeData?.uuidColumns?.find(
              (col) => col.name.toLowerCase() === migrationEntity
            );
            return `{{${sourceUuidObj?.name?.toLowerCase()}.${sourceUuidObj?.uuidColumn}}}` || migrationState.selectedObjectData.sourceUniqueIdentifier || "";
          }
          return migrationState.selectedObjectData.sourceUniqueIdentifier || "";
        }, ""),
        sourceResponseType: getSafe(() => migrationState.selectedObjectData.source_response_type, ""),
        sourceExecutor: constructSourceExeData(migrationState.selectedObjectData),
        dependentSources: dependentSourcesPayload(),
      },
      target: {
        name: mainTargetName.toLowerCase(),
        uniqueIdentifier: getSafe(() => migrationState.selectedEntityData.mainTarget[0].targetUniqueIdentifier, ""),
        targetResponseType: getSafe(() => migrationState.selectedEntityData.mainTarget[0].target_response_type, ""),
        isMainTarget: true,
        fieldMappings: mainTargetFieldMappings,
        targetExecutor: getSafe(() => migrationState.selectedEntityData.mainTarget[0].targetExecutor, {}),
        dependentTargets: dependentTargetsPayload(),
      },
    }

    const payload = {
      data: {
        template_id: Number.parseInt(planId, 10),
        status: "IN_PROGRESS",
        email_id: emailFromStorage || "",
        transformation_json: JSON.stringify(transformationJson),
      },
    }

    const postMigration = await postTransformation(payload)
    const transformationId = postMigration?.id || null

    if (transformationId) {
      try {
        const queuePayload = {
          migration_id: transformationId.toString(),
        }
        localStorage.setItem("latestMigrationId", transformationId.toString())
        const serviceBusResponse = await postToQueue(queuePayload)

        if (serviceBusResponse?.isSuccess === false) {
          throw new Error("Failed to send message to queue")
        }

        console.log("Message sent to Service Bus queue successfully")
        return {
          isSuccess: serviceBusResponse?.isSuccess,
          transformationJson,
        }
      } catch (error) {
        console.error("Error sending message to Service Bus queue:", error)
        toast.error("Failed to queue migration task", {
          position: "top-right",
        })
        return {
          isSuccess: false,
          transformationJson,
          message: "Failed to queue migration task",
        }
      }
    }

    return postMigration
  }

  // Add loader animation CSS if not already present
  if (typeof window !== 'undefined' && !document.getElementById('review-loader-style')) {
    const loaderStyle = document.createElement('style');
    loaderStyle.id = 'review-loader-style';
    loaderStyle.innerHTML = `@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }`;
    document.head.appendChild(loaderStyle);
  }

  return (
    <div>
      {!showPricing ? (
        <div>
          <div className={styles.dFlex}>
            <div className={styles.section}>
              <div className={styles["migration-container"]}>
                <div className={styles["db-icon-left"]}>
                  <div className={styles["db-lines"]}></div>
                  <div className={styles["db-lines"]}></div>
                  <div className={styles["db-lines"]}></div>
                </div>
                <div className={styles["db-icon-right"]}>
                  <div className={styles["db-lines"]}></div>
                  <div className={styles["db-lines"]}></div>
                  <div className={styles["db-lines"]}></div>
                </div>
                <div className={styles["packets-area"]}>
                  <div className={styles["data-packet"]}></div>
                </div>
              </div>
            </div>
            <div className={styles.section}>
              {isSampleMigration ? (
                <div style={{ width: "90%" }}>
                  <div className={styles.dFlex}>
                    <span className={globalStyles.poppinsHeaderStyle}>Sample Migration Settings</span>
                  </div>
                  <input
                    style={{ width: "95%", marginTop: "10px" }}
                    className="form-control"
                    onChange={handleSampleBatchSizeChange}
                    value={sampleBatchSize}
                    placeholder="Enter the number of records you want to migrate*"
                  />
                  <div className={`${styles.dFlex} ${styles.dotName}`}>
                    <div>Maximum of 50 records for sample migration</div>
                  </div>
                  <div className={styles.dFlex} style={{ marginTop: "20px" }}>
                    <span className={globalStyles.poppinsHeaderStyle}>Tag your target records</span>
                    <div className={styles.dFlex} style={{ marginLeft: "auto" }}>
                      <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} onClick={() => displayArticle("What is a tag name?")} />
                      <span className={globalStyles.guideName}>What is a tag name?</span>
                    </div>
                  </div>
                  <input
                    className="form-control"
                    style={{ width: "95%", marginTop: "10px" }}
                    placeholder="Tag name"
                    value={tag}
                    onChange={handleTagChange}
                    maxLength={32}
                  />

                  <div
                    style={{
                      display: "flex",
                      gap: "10px",
                      margin: "20px 0",
                      flexDirection: hasAtleastOneSampleMigration ? "column" : "column",
                      flexWrap: "wrap",
                    }}
                  >
                    <button
                      className={globalStyles.mainButton}
                      style={{
                        width: "100%",
                        // opacity: sampleMigrationLimitReached ? 0.5 : 1,
                        // cursor: sampleMigrationLimitReached ? "not-allowed" : "pointer",
                        // backgroundColor: sampleMigrationLimitReached ? "#ccc" : "",
                      }}
                      onClick={handleSampleMigration}
                    // disabled={sampleMigrationLimitReached}
                    >
                      Start Sample Migration
                    </button>

                    {(hasAtleastOneSampleMigration || hasSampleMigration) && (
                      <button
                        className={globalStyles.mainButton}
                        style={{ 
                          width: "100%",
                        }}
                        onClick={handleRerunPreviousBatch}
                      >
                        Rerun Previous Batch
                      </button>
                    )}

                    {(hasAtleastOneSampleMigration || hasSampleMigration) && (
                      <button
                        className={globalStyles.mainButton}
                        style={{ 
                          width: "100%"
                        }}
                        onClick={() => setIsSampleMigration(false)}
                      >
                        Skip to Live Migration
                      </button>
                    )}
                  </div>

                  {showSampleLoader && (
                    <div style={{
                      position: "fixed",
                      top: 0,
                      left: 0,
                      width: "100vw",
                      height: "100vh",
                      background: "rgba(255, 255, 255, 0.84)",
                      zIndex: 20000,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center"
                    }}>
                      <LoaderSpinner fullpage={false} text="Starting Sample Migration..." />
                    </div>
                  )}

                  {/* {sampleMigrationLimitReached && (
                    <div
                      className={styles.dotName}
                      style={{ color: "#E37B52", marginBottom: "15px", textAlign: "center" }}
                    >
                      Maximum sample migration limit exceeded for the template
                    </div>
                  )} */}
                </div>
              ) : (
                <div style={{ width: "90%" }}>
                  <div className={styles.dFlex}>
                    <span className={globalStyles.poppinsHeaderStyle}>Live Migration Settings</span>
                    <div className={styles.dFlex} style={{ marginLeft: "auto" }}>
                      <img
                        src="/assets/help.png"
                        alt="Help"
                        className={globalStyles.helpIcon}
                        onClick={() => {
                          displayArticle("Pricing and plan options")
                        }}
                      />
                      <span className={globalStyles.guideName}>Pricing and plan options</span>
                    </div>
                  </div>

                  <input
                    className="form-control"
                    style={{ width: "95%", marginTop: "10px", fontFamily: "Inter" }}
                    placeholder="Tag name (max 32 characters)"
                    value={tag}
                    onChange={handleTagChange}
                    maxLength={32}
                  />

                  {/* Dropdown for "When do you want to start the migration?" - COMMENTED OUT */}
                  {/* 
                  <div style={{ marginTop: "20px", marginBottom: "30px" }}>
                    <span className={globalStyles.poppinsHeaderStyle}>When do you want to start the migration?</span>
                    <FormControl fullWidth className={globalStyles.customDropdownContainer}>
                      <Select
                        value={liveMigrationOption || ""}
                        onChange={(event) => handleSourceValueChange(event.target.value)}
                        className={globalStyles.customDropdownSelect}
                        displayEmpty
                        renderValue={(selected) =>
                          selected ? (
                            <span className={globalStyles.guideName} style={{ fontSize: "14px" }}>
                              {selected === "now" ? "Now" : "Schedule for later"}
                            </span>
                          ) : (
                            <span className={globalStyles.guideName} style={{ fontSize: "14px" }}>
                              Choose below*
                            </span>
                          )
                        }
                      >
                        <MenuItem value="now" style={{ fontFamily: "Inter" }}>
                          Now
                        </MenuItem>
                        <MenuItem value="later" style={{ fontFamily: "Inter" }}>
                          Schedule for later
                        </MenuItem>
                      </Select>
                    </FormControl>
                    {liveMigrationOption === "later" && (
                      <div>
                        <div className={globalStyles.passwordContainer}>
                          <input
                            className={globalStyles.passwordInput}
                            placeholder="Set Date and time"
                            style={{ width: "95%" }}
                            value={migrationDate}
                            readOnly={true}
                          />
                          <button type="button" className={globalStyles.passwordToggle} onClick={() => setOpen(true)}>
                            <HiCalendarDays className={globalStyles.closeIcon} />
                          </button>
                          <Dialog open={open} onClose={() => setOpen(false)}>
                            <DialogContent sx={{ backgroundColor: "#170903", padding: "0" }}>
                              <MigrationScheduler
                                open={open}
                                onClose={() => setOpen(false)}
                                fullWidth
                                maxWidth="md"
                                setMigrationDate={setMigrationDate}
                              />
                            </DialogContent>
                          </Dialog>
                        </div>
                      </div>
                    )}
                  </div>
                  */}

                  {/* Notification section - COMMENTED OUT */}
                  {/* 
                  <div
                    className={styles.dFlex}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      marginTop: "20px",
                      width: "100%",
                      maxWidth: "300px",
                    }}
                  >
                    <div className={styles.dotName} style={{ fontSize: "16px" }}>
                      Send notifications and updates
                    </div>
                    <FormControlLabel
                      disabled={true}
                      control={
                        <Switch
                          checked={sentNotification}
                          onChange={(event) => setSentNotification(!sentNotification)}
                          sx={{
                            width: 40,
                            height: 20,
                            padding: 0,
                            "& .MuiSwitch-switchBase": {
                              padding: 0,
                              margin: "2px 3px",
                              transitionDuration: "300ms",
                              "&.Mui-checked": {
                                transform: "translateX(20px)",
                                color: "#fff",
                                "& + .MuiSwitch-track": {
                                  backgroundColor: "#E97451",
                                  opacity: 1,
                                  border: 0,
                                },
                                "& .MuiSwitch-thumb": {
                                  backgroundColor: "#fff",
                                  width: 14,
                                  height: 14,
                                },
                                "&.Mui-disabled + .MuiSwitch-track": {
                                  opacity: 0.5,
                                },
                              },
                            },
                            "& .MuiSwitch-thumb": {
                              backgroundColor: "#fff",
                              boxSizing: "border-box",
                              width: 14,
                              height: 14,
                              borderRadius: "50%",
                              transition: "width 0.2s, height 0.2s",
                            },
                            "& .MuiSwitch-track": {
                              borderRadius: 10,
                              backgroundColor: "#B9B5B3",
                              opacity: 1,
                              transition: "background-color 0.5s",
                            },
                          }}
                        />
                      }
                    />
                  </div>
                  */}

                  {/* Notification contacts section - COMMENTED OUT since notification switch is commented */}
                  {/* 
                    {sentNotification && (
                      <div>
                        <div className={styles.dFlex} style={{ gap: "15px" }}>
                          <input
                            className={globalStyles.formCheckInput}
                            type="checkbox"
                            id="checkbox"
                            style={{ marginTop: "8px" }}
                            value={sentDetailsToMyself}
                            aria-label="..."
                            onChange={(event) => setSentDetailsToMyself(!sentDetailsToMyself)}
                          />
                          <span className={styles.dotName} style={{ fontSize: "16px" }}>
                            Send details to myself
                          </span>
                        </div>
                        {contacts.map((contact, index) => (
                          <div key={index} style={{ marginBottom: "15px" }}>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                marginTop: "15px",
                              }}
                            >
                              {contacts.length === 1 ? (
                                <div
                                  className={styles.dFlex}
                                  style={{
                                    marginTop: "15px",
                                    width: "100%",
                                  }}
                                >
                                  <div className={globalStyles.poppinsHeaderStyle}>Or enter the contact details</div>
                                  <button
                                    className={globalStyles.pageButton}
                                    style={{ marginLeft: "auto" }}
                                    onClick={() => setSentNotification(!sentNotification)}
                                  >
                                    <HiXMark className={globalStyles.closeIcon} />
                                  </button>
                                </div>
                              ) : (
                                <div className={styles.dFlex} style={{ marginTop: "15px", width: "100%" }}>
                                  <div className={globalStyles.poppinsHeaderStyle}>Enter the contact details</div>
                                  <button
                                    className={globalStyles.pageButton}
                                    style={{
                                      marginLeft: "auto",
                                      justifyContent: "flex-end",
                                    }}
                                    onClick={removeContact}
                                  >
                                    <HiXMark className={globalStyles.closeIcon} />
                                  </button>
                                </div>
                              )}
                            </div>
                            <input
                              className="form-control"
                              style={{ width: "95%", marginTop: "10px" }}
                              placeholder="Enter the Email ID*"
                            />
                            <div style={{ display: "flex", gap: "10px" }}>
                              <FormControl className={globalStyles.customDropdownContainer} style={{ width: "15%" }}>
                                <Select
                                  value={contact.code}
                                  displayEmpty
                                  className={globalStyles.customDropdownSelect}
                                  renderValue={(selected) => (
                                    <span className={globalStyles.guideName} style={{ fontSize: "14px" }}>
                                      {selected}
                                    </span>
                                  )}
                                >
                                  {countryCodes.map((country) => (
                                    <MenuItem key={country.code} value={country.code}>
                                      {country.code} ({country.name})
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                              <input
                                className="form-control"
                                style={{ width: "80%", marginTop: "12px" }}
                                placeholder="Enter the phone number*"
                              />
                            </div>
                          </div>
                        ))}
                        {contacts.length === 1 && (
                          <button
                            className={globalStyles.pageButton}
                            style={{
                              width: "100%",
                              height: "40px",
                              marginTop: "20px",
                            }}
                            onClick={addContact}
                          >
                            <HiPlus />
                            Add another contact
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                  */}

                  {/* Pricing package button - COMMENTED OUT */}
                  {/* <button
                    className={globalStyles.mainButton}
                    style={{ width: "100%" }}
                    onClick={() => {
                      if (!liveMigrationOption) {
                        toast.error("Please select when you want to start the migration", {
                          position: "top-right",
                        })
                        return
                      }

                      if (liveMigrationOption === "later" && !migrationDate) {
                        toast.error("Please select a date and time for the scheduled migration", {
                          position: "top-right",
                        })
                        return
                      }

                      setShowPricing(!showPricing)
                    }}
                  >
                    Select your pricing package
                  </button> */}
                  <div
                    style={{
                      display: "flex",
                      gap: "10px",
                      margin: "20px 0",
                      flexDirection: "column",
                    }}
                  >
                    <button
                      className={globalStyles.mainButton}
                      style={{ 
                        width: "100%", 
                        position: "relative" 
                      }}
                      onClick={handleLiveMigrationClick}
                      disabled={liveMigrationLoading}
                    >
                      Start Live Migration
                    </button>

                    {(hasAtleastOneSampleMigration || hasSampleMigration) && (
                      <button
                        className={globalStyles.mainButton}
                        style={{ 
                          width: "100%",
                        }}
                        onClick={handleRerunPreviousBatch}
                      >
                        Rerun Previous Batch
                      </button>
                    )}
                  </div>

                  {liveMigrationLoading && (
                    <div style={{
                      position: "fixed",
                      top: 0,
                      left: 0,
                      width: "100vw",
                      height: "100vh",
                      background: "rgba(255, 255, 255, 0.84)",
                      zIndex: 20000,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center"
                    }}>
                      <LoaderSpinner fullpage={false} text="Starting Live Migration..." />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          <ToastContainer />
        </div>
      ) : (
        <div>
          {!isPaymentSucces ? (
            <Pay />) : (
            <div>
              <PaymentDetails liveMigrationPayload={liveMigrationPayload} planId={planId} initiateMigration={initiateMigration} />
            </div>
          )}
        </div>
      )}
    </div>
  )
}
